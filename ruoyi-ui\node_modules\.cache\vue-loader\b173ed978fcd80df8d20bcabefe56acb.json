{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue", "mtime": 1754628577485}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0TGlicmFyeSwNCiAgYWRkTGlicmFyeSwNCiAgZGVsTGlicmFyeSwNCiAgZ2VuZXJhdGVDb3B5d3JpdGluZywNCiAgbGlzdENvbnRlbnQsDQogIGFkZENvbnRlbnQsDQogIHVwZGF0ZUNvbnRlbnQsDQogIGRlbENvbnRlbnQsDQogIGdldFByb2dyZXNzLA0KICByZWdlbmVyYXRlTGlicmFyeSwNCiAgdmFsaWRhdGVCYWlkdUNvbmZpZywNCiAgZ2V0TW9kZWxJbmZvDQp9IGZyb20gJ0AvYXBpL2FpL2NvcHl3cml0aW5nJw0KDQovLyDlr7zlhaXmtYvor5VBUEnkvZzkuLrlpIfnlKgNCmltcG9ydCB7DQogIGxpc3RMaWJyYXJ5VGVzdCwNCiAgYWRkTGlicmFyeVRlc3QsDQogIHRlc3REZWVwU2Vla0RpcmVjdCwNCiAgaGVhbHRoQ2hlY2sNCn0gZnJvbSAnQC9hcGkvYWkvY29weXdyaXRpbmctdGVzdCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU3RvcmVyU2hpcGluJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5a+56K+d5qGG54q25oCBDQogICAgICBjcmVhdGVMaWJyYXJ5RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBhZGRDb3B5d3JpdGluZ0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgdmlld0xpYnJhcnlEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCg0KICAgICAgLy8g5Yqg6L2954q25oCBDQogICAgICBjcmVhdGluZzogZmFsc2UsDQogICAgICBhZGRpbmc6IGZhbHNlLA0KDQogICAgICAvLyDnrZvpgInlkozmkJzntKINCiAgICAgIGZpbHRlclN0YXR1czogJycsDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCg0KICAgICAgLy8g5b2T5YmN5pWw5o2uDQogICAgICBjdXJyZW50TGlicmFyeTogbnVsbCwNCiAgICAgIGxpYnJhcnlDb250ZW50czogW10sDQogICAgICBpc01vYmlsZTogZmFsc2UsDQoNCiAgICAgIC8vIOWIm+W7uuaWh+ahiOW6k+ihqOWNlQ0KICAgICAgY3JlYXRlTGlicmFyeUZvcm06IHsNCiAgICAgICAgbmFtZTogJycsDQogICAgICAgIHVzZUFJOiB0cnVlLA0KICAgICAgICBzaG9wRGV0YWlsczogJycsDQogICAgICAgIHByb21wdDogJycsDQogICAgICAgIGNvdW50OiAxMCwNCiAgICAgICAgd29yZENvdW50OiAnMjAwJyAvLyBBSeWJqui+keaWh+ahiOm7mOiupDIwMOWtlw0KICAgICAgfSwNCiAgICAgIGNyZWF0ZUxpYnJhcnlSdWxlczogew0KICAgICAgICBuYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaWh+ahiOW6k+WQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDUwLCBtZXNzYWdlOiAn6ZW/5bqm5ZyoIDIg5YiwIDUwIOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIHNob3BEZXRhaWxzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeW6l+mTuuivpuaDhScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAxMCwgbWF4OiA1MDAsIG1lc3NhZ2U6ICfplb/luqblnKggMTAg5YiwIDUwMCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBwcm9tcHQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlQUnmj5DnpLror40nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogNSwgbWF4OiAzMDAsIG1lc3NhZ2U6ICfplb/luqblnKggNSDliLAgMzAwIOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGNvdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeeUn+aIkOadoeaVsCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIHdvcmRDb3VudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmlofmoYjlrZfmlbAnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQoNCiAgICAgIC8vIOaWsOWinuaWh+ahiOihqOWNlQ0KICAgICAgYWRkQ29weXdyaXRpbmdGb3JtOiB7DQogICAgICAgIHVzZUFJOiB0cnVlLA0KICAgICAgICBzaG9wRGV0YWlsczogJycsDQogICAgICAgIHByb21wdDogJycsDQogICAgICAgIGNvdW50OiA1LA0KICAgICAgICBjb250ZW50OiAnJw0KICAgICAgfSwNCiAgICAgIGFkZENvcHl3cml0aW5nUnVsZXM6IHsNCiAgICAgICAgc2hvcERldGFpbHM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5bqX6ZO66K+m5oOFJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgcHJvbXB0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpUFJ5o+Q56S66K+NJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgY291bnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl55Sf5oiQ5p2h5pWwJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGVudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmlofmoYjlhoXlrrknLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyBBSeaPkOekuuivjeaOqOiNkA0KICAgICAgcmVjb21tZW5kUHJvbXB0czogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEsDQogICAgICAgICAgaWNvbjogJ/CfjZQnLA0KICAgICAgICAgIHRpdGxlOiAn576O6aOf5o6o5bm/JywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI6aSQ6aWu5bqX6ZO6JywNCiAgICAgICAgICBjb250ZW50OiAn55Sf5oiQ5ZC45byV5Lq655qE576O6aOf5o6o5bm/5paH5qGI77yM6KaB5rGC6K+t6KiA55Sf5Yqo44CB5pyJ6aOf5qyy5oSf77yM56qB5Ye66I+c5ZOB54m56Imy5ZKM5bqX6ZO65rCb5Zu077yM6IO95aSf5r+A5Y+R6aG+5a6i55qE6LSt5Lmw5qyy5pybJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgaWNvbjogJ/CfkZcnLA0KICAgICAgICAgIHRpdGxlOiAn5pyN6KOF5pe25bCaJywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI5pyN6KOF5bqX6ZO6JywNCiAgICAgICAgICBjb250ZW50OiAn5Yib5L2c5pe25bCa5r2u5rWB55qE5pyN6KOF5o6o5bm/5paH5qGI77yM56qB5Ye65qy+5byP6K6+6K6h44CB6Z2i5paZ6LSo5oSf44CB5pCt6YWN5bu66K6u77yM5bGV546w5ZOB54mM6LCD5oCn5ZKM5pe25bCa5oCB5bqmJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgaWNvbjogJ/CfkoQnLA0KICAgICAgICAgIHRpdGxlOiAn576O5aaG5oqk6IKkJywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI576O5aaG5bqX6ZO6JywNCiAgICAgICAgICBjb250ZW50OiAn57yW5YaZ5LiT5Lia55qE576O5aaG5oqk6IKk5paH5qGI77yM5by66LCD5Lqn5ZOB5Yqf5pWI44CB5L2/55So5L2T6aqM44CB6YCC55So6IKM6IKk57G75Z6L77yM5Lyg6YCS576O5Li96Ieq5L+h55qE55CG5b+1Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDQsDQogICAgICAgICAgaWNvbjogJ/Cfj6AnLA0KICAgICAgICAgIHRpdGxlOiAn5a625bGF55Sf5rS7JywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI5a625bGF5bqX6ZO6JywNCiAgICAgICAgICBjb250ZW50OiAn5pKw5YaZ5rip6aao55qE5a625bGF55Sf5rS75paH5qGI77yM5bGV546w5Lqn5ZOB5a6e55So5oCn44CB6K6+6K6h576O5oSf44CB55Sf5rS75ZOB6LSo5o+Q5Y2H77yM6JCl6YCg6IiS6YCC5a625bqt5rCb5Zu0Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDUsDQogICAgICAgICAgaWNvbjogJ/Cfk7EnLA0KICAgICAgICAgIHRpdGxlOiAn5pWw56CB56eR5oqAJywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI5pWw56CB5bqX6ZO6JywNCiAgICAgICAgICBjb250ZW50OiAn5Yi25L2c5LiT5Lia55qE5pWw56CB5Lqn5ZOB5paH5qGI77yM56qB5Ye65oqA5pyv5Y+C5pWw44CB5Yqf6IO954m554K544CB5L2/55So5Zy65pmv77yM5L2T546w56eR5oqA5oSf5ZKM5a6e55So5Lu35YC8Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDYsDQogICAgICAgICAgaWNvbjogJ/CfjpMnLA0KICAgICAgICAgIHRpdGxlOiAn5pWZ6IKy5Z+56K6tJywNCiAgICAgICAgICBkZXNjOiAn6YCC5ZCI5pWZ6IKy5py65p6EJywNCiAgICAgICAgICBjb250ZW50OiAn5Yib5bu65pyJ6K+05pyN5Yqb55qE5pWZ6IKy5Z+56K6t5paH5qGI77yM5by66LCD6K++56iL5Lu35YC844CB5biI6LWE5Yqb6YeP44CB5a2m5Lmg5pWI5p6c77yM5r+A5Y+R5a2m5Lmg5YW06Laj5ZKM5oql5ZCN5oSP5oS/Jw0KICAgICAgICB9DQogICAgICBdLA0KDQogICAgICAvLyDmlofmoYjlupPliJfooagNCiAgICAgIGxpYnJhcnlMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICBuYW1lOiAn576O6aOf5o6i5bqX5paH5qGI5bqTJywNCiAgICAgICAgICB1c2VBSTogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLA0KICAgICAgICAgIHRhcmdldENvdW50OiAyMCwNCiAgICAgICAgICBnZW5lcmF0ZWRDb3VudDogMjAsDQogICAgICAgICAgd29yZENvdW50OiAnMTAwJywNCiAgICAgICAgICBzaG9wRGV0YWlsczogJ+aIkeS7rOaYr+S4gOWutuS4u+aJk+W3neiPnOeahOeJueiJsumkkOWOhe+8jOS9jeS6juW4guS4reW/g+e5geWNjuWcsOaute+8jOS4u+iQpem6u+i+o+eBq+mUheOAgeawtOeFrumxvOOAgeWuq+S/nem4oeS4geetiee7j+WFuOW3neiPnO+8jOW6l+WGheijheS/ruWPpOactOWFuOmbhe+8jOacjeWKoeeDreaDheWRqOWIsOOAgicsDQogICAgICAgICAgcHJvbXB0OiAn55Sf5oiQ5ZC45byV5Lq655qE576O6aOf5o6o5bm/5paH5qGI77yM6KaB5rGC6K+t6KiA55Sf5Yqo44CB5pyJ6aOf5qyy5oSf77yM56qB5Ye66I+c5ZOB54m56Imy5ZKM5bqX6ZO65rCb5Zu0JywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wMS0xNSAxNDozMDowMCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIG5hbWU6ICfml7blsJrmnI3oo4Xmjqjlub/lupMnLA0KICAgICAgICAgIHVzZUFJOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2dlbmVyYXRpbmcnLA0KICAgICAgICAgIHRhcmdldENvdW50OiAzMCwNCiAgICAgICAgICBnZW5lcmF0ZWRDb3VudDogMTUsDQogICAgICAgICAgd29yZENvdW50OiAnMTUwJywNCiAgICAgICAgICBzaG9wRGV0YWlsczogJ+aXtuWwmuWls+ijheWTgeeJjOW6l++8jOS4u+imgemdouWQkTI1LTM15bKB6YO95biC5aWz5oCn77yM5Lqn5ZOB5YyF5ous6IGM5Lia6KOF44CB5LyR6Zey6KOF44CB5pma56S85pyN562J77yM5rOo6YeN5ZOB6LSo5ZKM6K6+6K6h5oSf44CCJywNCiAgICAgICAgICBwcm9tcHQ6ICfliJvkvZzml7blsJrmva7mtYHnmoTmnI3oo4Xmjqjlub/mlofmoYjvvIznqoHlh7rmrL7lvI/orr7orqHjgIHpnaLmlpnotKjmhJ/jgIHmkK3phY3lu7rorq4nLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTAxLTE1IDEwOjE1OjAwJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgbmFtZTogJ+aJi+WKqOWIm+W7uuaWh+ahiOW6kycsDQogICAgICAgICAgdXNlQUk6IGZhbHNlLA0KICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsDQogICAgICAgICAgdGFyZ2V0Q291bnQ6IDEwLA0KICAgICAgICAgIGdlbmVyYXRlZENvdW50OiA4LA0KICAgICAgICAgIHdvcmRDb3VudDogJzIwMCcsDQogICAgICAgICAgc2hvcERldGFpbHM6ICcnLA0KICAgICAgICAgIHByb21wdDogJycsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDEtMTQgMTY6MjA6MDAnDQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgZmlsdGVyZWRMaWJyYXJ5TGlzdCgpIHsNCiAgICAgIGxldCBsaXN0ID0gdGhpcy5saWJyYXJ5TGlzdA0KDQogICAgICAvLyDnirbmgIHnrZvpgIkNCiAgICAgIGlmICh0aGlzLmZpbHRlclN0YXR1cykgew0KICAgICAgICBsaXN0ID0gbGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnN0YXR1cyA9PT0gdGhpcy5maWx0ZXJTdGF0dXMpDQogICAgICB9DQoNCiAgICAgIC8vIOWFs+mUruivjeaQnOe0og0KICAgICAgaWYgKHRoaXMuc2VhcmNoS2V5d29yZCkgew0KICAgICAgICBjb25zdCBrZXl3b3JkID0gdGhpcy5zZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkNCiAgICAgICAgbGlzdCA9IGxpc3QuZmlsdGVyKGl0ZW0gPT4NCiAgICAgICAgICBpdGVtLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSB8fA0KICAgICAgICAgIChpdGVtLnNob3BEZXRhaWxzICYmIGl0ZW0uc2hvcERldGFpbHMudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSkNCiAgICAgICAgKQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gbGlzdA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJbmjIHkuYXljJblrZjlgqgNCiAgICB0aGlzLmxvYWRMaWJyYXJ5Q29udGVudEZyb21TdG9yYWdlKCkNCg0KICAgIHRoaXMubG9hZExpYnJhcnlMaXN0KCkNCg0KICAgIC8vIOWkh+eUqOaWueahiO+8muWmguaenDPnp5LlkI7ov5jmsqHmnInmlbDmja7vvIznm7TmjqXliqDovb3mqKHmi5/mlbDmja4NCiAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgIGlmICh0aGlzLmxpYnJhcnlMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjb25zb2xlLmxvZygnM+enkuWQjuS7jeaXoOaVsOaNru+8jOW8uuWItuWKoOi9veaooeaLn+aVsOaNricpDQogICAgICAgIHRoaXMubG9hZE1vY2tMaWJyYXJ5TGlzdCgpDQogICAgICB9DQogICAgfSwgMzAwMCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmNoZWNrTW9iaWxlKCkNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5jaGVja01vYmlsZSkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5jaGVja01vYmlsZSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNoZWNrTW9iaWxlKCkgew0KICAgICAgdGhpcy5pc01vYmlsZSA9IHdpbmRvdy5pbm5lcldpZHRoIDw9IDc2OA0KICAgIH0sDQoNCg0KICAgIGxvYWRMaWJyYXJ5TGlzdCgpIHsNCiAgICAgIGxpc3RMaWJyYXJ5KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubGlicmFyeUxpc3QgPSByZXNwb25zZS5yb3dzIHx8IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgaWYgKHRoaXMubGlicmFyeUxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgLy8g5aaC5p6c6L+U5Zue56m65pWw5o2u77yM5Lmf5Yqg6L295qih5ouf5pWw5o2uDQogICAgICAgICAgdGhpcy5sb2FkTW9ja0xpYnJhcnlMaXN0KCkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlofmoYjlupPliJfooajlpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja4nLCBlcnJvcikNCg0KICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/nmbvlvZXov4fmnJ/plJnor68NCiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQwMSB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygn55m75b2VJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ+i/h+acnycpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmo4DmtYvliLDnmbvlvZXnirbmgIHov4fmnJ/vvIzmraPlnKjkvb/nlKjmvJTnpLrmqKHlvI8uLi4nKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2u5L2c5Li65aSH55So5pa55qGIDQogICAgICAgIHRoaXMubG9hZE1vY2tMaWJyYXJ5TGlzdCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3mqKHmi5/mlofmoYjlupPmlbDmja4NCiAgICBsb2FkTW9ja0xpYnJhcnlMaXN0KCkgew0KICAgICAgY29uc29sZS5sb2coJ+WKoOi9veaooeaLn+aWh+ahiOW6k+aVsOaNricpDQoNCiAgICAgIGNvbnN0IG1vY2tMaWJyYXJpZXMgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICBsaWJyYXJ5SWQ6IDEsDQogICAgICAgICAgbmFtZTogJ+e+jumjn+aOouW6l+aWh+ahiOW6kycsDQogICAgICAgICAgbGlicmFyeU5hbWU6ICfnvo7po5/mjqLlupfmlofmoYjlupMnLA0KICAgICAgICAgIHVzZUFJOiB0cnVlLA0KICAgICAgICAgIHVzZUFpOiB0cnVlLA0KICAgICAgICAgIHNob3BEZXRhaWxzOiAn57K+6YCJ576O6aOf6aSQ5Y6F77yM5o+Q5L6b5ZCE57G754m56Imy6I+c5ZOB5ZKM5LyY6LSo5pyN5YqhJywNCiAgICAgICAgICBwcm9tcHQ6ICfnlJ/miJDlkLjlvJXkurrnmoTnvo7po5/mjqLlupfmlofmoYjvvIznqoHlh7roj5zlk4HnibnoibLlkoznlKjppJDkvZPpqownLA0KICAgICAgICAgIHRhcmdldENvdW50OiAyMCwNCiAgICAgICAgICBnZW5lcmF0ZWRDb3VudDogMjAsDQogICAgICAgICAgd29yZENvdW50OiAxNTAsDQogICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJywNCiAgICAgICAgICBjcmVhdGVUaW1lOiAnMjAyNC0wMS0xNSAxMDozMDowMCcsDQogICAgICAgICAgY3JlYXRlQnk6ICdhZG1pbicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIGxpYnJhcnlJZDogMiwNCiAgICAgICAgICBuYW1lOiAn5pe25bCa5pyN6KOF5o6o5bm/5bqTJywNCiAgICAgICAgICBsaWJyYXJ5TmFtZTogJ+aXtuWwmuacjeijheaOqOW5v+W6kycsDQogICAgICAgICAgdXNlQUk6IHRydWUsDQogICAgICAgICAgdXNlQWk6IHRydWUsDQogICAgICAgICAgc2hvcERldGFpbHM6ICfml7blsJrmnI3oo4Xlk4HniYzvvIzkuLvokKXmva7mtYHmnI3ppbDlkozphY3ppbAnLA0KICAgICAgICAgIHByb21wdDogJ+eUn+aIkOaXtuWwmuacjeijheaOqOW5v+aWh+ahiO+8jOW8uuiwg+asvuW8j+aWsOmiluWSjOWTgei0qOS8mOiJrycsDQogICAgICAgICAgdGFyZ2V0Q291bnQ6IDE1LA0KICAgICAgICAgIGdlbmVyYXRlZENvdW50OiAxNSwNCiAgICAgICAgICB3b3JkQ291bnQ6IDEyMCwNCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTAxLTEwIDE0OjIwOjAwJywNCiAgICAgICAgICBjcmVhdGVCeTogJ2FkbWluJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDMsDQogICAgICAgICAgbGlicmFyeUlkOiAzLA0KICAgICAgICAgIG5hbWU6ICflkpbllaHljoXmuKnppqjmlofmoYjlupMnLA0KICAgICAgICAgIGxpYnJhcnlOYW1lOiAn5ZKW5ZWh5Y6F5rip6aao5paH5qGI5bqTJywNCiAgICAgICAgICB1c2VBSTogdHJ1ZSwNCiAgICAgICAgICB1c2VBaTogdHJ1ZSwNCiAgICAgICAgICBzaG9wRGV0YWlsczogJ+a4qemmqOWSluWVoeWOhe+8jOS4u+iQpeaJi+W3peWSluWVoeWSjOeyvuiHtOeUnOeCue+8jOS9jeS6juW4guS4reW/g+e5geWNjuWcsOautScsDQogICAgICAgICAgcHJvbXB0OiAn55Sf5oiQ5rip6aao5ZKW5ZWh5Y6F5o6o5bm/5paH5qGI77yM56qB5Ye6546v5aKD6IiS6YCC5ZKM5ZKW5ZWh5ZOB6LSoJywNCiAgICAgICAgICB0YXJnZXRDb3VudDogMTAsDQogICAgICAgICAgZ2VuZXJhdGVkQ291bnQ6IDgsDQogICAgICAgICAgd29yZENvdW50OiAxMDAsDQogICAgICAgICAgc3RhdHVzOiAnZ2VuZXJhdGluZycsDQogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDEtMjAgMDk6MTU6MDAnLA0KICAgICAgICAgIGNyZWF0ZUJ5OiAndXNlcicNCiAgICAgICAgfQ0KICAgICAgXQ0KDQogICAgICAvLyDmt7vliqDnlKjmiLfliJvlu7rnmoTmlofmoYjlupPvvIjlpoLmnpzmnInnmoTor53vvIkNCiAgICAgIGNvbnN0IHVzZXJMaWJyYXJpZXMgPSB0aGlzLmxpYnJhcnlMaXN0LmZpbHRlcihsaWIgPT4gbGliLmNyZWF0ZUJ5ID09PSAnZGVtbycpDQoNCiAgICAgIHRoaXMubGlicmFyeUxpc3QgPSBbLi4ubW9ja0xpYnJhcmllcywgLi4udXNlckxpYnJhcmllc10NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5Yqg6L295qih5ouf5paH5qGI5bqT5pWw5o2u77yI5YWxJyArIHRoaXMubGlicmFyeUxpc3QubGVuZ3RoICsgJ+S4quaWh+ahiOW6k++8iScpDQogICAgfSwNCiAgICByZWZyZXNoRGF0YSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmiYvliqjliLfmlrDmlbDmja4nKQ0KICAgICAgdGhpcy5sb2FkTGlicmFyeUxpc3QoKQ0KDQogICAgICAvLyDlpoLmnpwx56eS5ZCO6L+Y5rKh5pyJ5pWw5o2u77yM55u05o6l5Yqg6L295qih5ouf5pWw5o2uDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMubGlicmFyeUxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+WIt+aWsOWQjuS7jeaXoOaVsOaNru+8jOWKoOi9veaooeaLn+aVsOaNricpDQogICAgICAgICAgdGhpcy5sb2FkTW9ja0xpYnJhcnlMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aVsOaNruW3suWIt+aWsCcpDQogICAgICAgIH0NCiAgICAgIH0sIDEwMDApDQogICAgfSwNCg0KICAgIC8vIOaYvuekuuWIm+W7uuaWh+ahiOW6k+Wvueivneahhg0KICAgIHNob3dDcmVhdGVMaWJyYXJ5RGlhbG9nKCkgew0KICAgICAgdGhpcy5jcmVhdGVMaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY3JlYXRlTGlicmFyeUZvcm0gPSB7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICB1c2VBSTogdHJ1ZSwNCiAgICAgICAgc2hvcERldGFpbHM6ICcnLA0KICAgICAgICBwcm9tcHQ6ICcnLA0KICAgICAgICBjb3VudDogMTAsDQogICAgICAgIHdvcmRDb3VudDogJzEwMCcNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L2/55So5o6o6I2Q5o+Q56S66K+NDQogICAgdXNlUHJvbXB0KHByb21wdCkgew0KICAgICAgdGhpcy5jcmVhdGVMaWJyYXJ5Rm9ybS5wcm9tcHQgPSBwcm9tcHQuY29udGVudA0KICAgICAgdGhpcy5jcmVhdGVMaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey5bqU55SoJHtwcm9tcHQudGl0bGV95o+Q56S66K+NYCkNCiAgICB9LA0KDQogICAgLy8g5pi+56S65o+Q56S66K+N5biu5YqpDQogICAgc2hvd1Byb21wdEhlbHAoKSB7DQogICAgICB0aGlzLiRhbGVydChgDQogICAgICAgIDxoND5BSeWJqui+keaWh+ahiOaPkOekuuivjeW7uuiuru+8mjwvaDQ+DQogICAgICAgIDxwPjxzdHJvbmc+5qC45b+D6KaB5rGC77yaPC9zdHJvbmc+6YCC5ZCI5Y+j5pKt77yM5byA5aS055So55aR6Zeu5Y+l5ZC45byV6KeC5LyX77yM6K+t6KiA6aG65Y+j5piT6K+7PC9wPg0KICAgICAgICA8YnI+DQogICAgICAgIDxoNT7wn5OdIOaOqOiNkOaPkOekuuivjeaooeadv++8mjwvaDU+DQogICAgICAgIDxwPjxzdHJvbmc+MS4g576O6aOf6aSQ6aWu77yaPC9zdHJvbmc+55Sf5oiQ6YCC5ZCI5Y+j5pKt55qE576O6aOf5o6o5bm/5paH5qGI77yM5byA5aS055So55aR6Zeu5Y+l5ZC45byV6KeC5LyX77yM56qB5Ye66aOf5p2Q5paw6bKc5ZKM5Y+j5oSf5bGC5qyh77yM6K+t6KiA55Sf5Yqo5pyJ6aOf5qyy5oSf77yM5pyL5Y+L5o6o6I2Q55qE6K+t5rCUPC9wPg0KICAgICAgICA8cD48c3Ryb25nPjIuIOeUn+a0u+acjeWKoe+8mjwvc3Ryb25nPueUn+aIkOa4qemmqOeahOeUn+a0u+acjeWKoeaOqOW5v+aWh+ahiO+8jOW8gOWktOeUqOeWkemXruWPpeW8lei1t+WFsem4o++8jOW8uuiwg+S+v+awkeWSjOi0tOW/g+acjeWKoe+8jOivreiogOS6suWIh+iHqueEtu+8jOWDj+mCu+Wxheaci+WPi+S7i+e7jTwvcD4NCiAgICAgICAgPHA+PHN0cm9uZz4zLiDml7blsJrnvo7lpobvvJo8L3N0cm9uZz7nlJ/miJDml7blsJrnvo7lpobnp43ojYnmlofmoYjvvIzlvIDlpLTnlKjnlpHpl67lj6XmipPkvY/nl5vngrnvvIznqoHlh7rkuqflk4HmlYjmnpzlkozkvb/nlKjkvZPpqozvvIzor63oqIDovbvmnb7mtLvms7zvvIzlp5DlprnliIbkuqvnmoTmhJ/op4k8L3A+DQogICAgICAgIDxwPjxzdHJvbmc+NC4g5pWZ6IKy5Z+56K6t77yaPC9zdHJvbmc+55Sf5oiQ5pWZ6IKy5Z+56K6t5o6o5bm/5paH5qGI77yM5byA5aS055So55aR6Zeu5Y+l5byV5Y+R5oCd6ICD77yM5by66LCD5a2m5Lmg5pWI5p6c5ZKM5oiQ6ZW/5Lu35YC877yM6K+t6KiA5LiT5Lia5L2G5LiN5aSx5Lqy5ZKM5YqbPC9wPg0KICAgICAgICA8cD48c3Ryb25nPjUuIOWBpeW6t+WFu+eUn++8mjwvc3Ryb25nPueUn+aIkOWBpeW6t+WFu+eUn+enkeaZruaWh+ahiO+8jOW8gOWktOeUqOeWkemXruWPpeW8lei1t+WFs+azqO+8jOeqgeWHuuWBpeW6t+eQhuW/teWSjOWunueUqOaWueazle+8jOivreiogOmAmuS/l+aYk+aHgu+8jOS4k+S4muWPr+S/oTwvcD4NCiAgICAgICAgPHA+PHN0cm9uZz42LiDml4XmuLjlh7rooYzvvJo8L3N0cm9uZz7nlJ/miJDml4XmuLjmma/ngrnmjqjlub/mlofmoYjvvIzlvIDlpLTnlKjnlpHpl67lj6Xmv4Dlj5HlkJHlvoDvvIzmj4/ov7Dnvo7mma/lkozni6znibnkvZPpqozvvIzor63oqIDlr4zmnInnlLvpnaLmhJ/lkozmhJ/mn5Plips8L3A+DQogICAgICAgIDxwPjxzdHJvbmc+Ny4g56eR5oqA5pWw56CB77yaPC9zdHJvbmc+55Sf5oiQ5pWw56CB5Lqn5ZOB5LuL57uN5paH5qGI77yM5byA5aS055So55aR6Zeu5Y+l5oqT5L2P6ZyA5rGC77yM56qB5Ye65Yqf6IO954m554K55ZKM5L2/55So5L6/5Yip77yM6K+t6KiA566A5rSB5piO5LqG77yM6YG/5YWN6L+H5LqO5oqA5pyv5YyWPC9wPg0KICAgICAgICA8cD48c3Ryb25nPjguIOWutuWxheeUn+a0u++8mjwvc3Ryb25nPueUn+aIkOWutuWxheeUqOWTgeaOqOW5v+aWh+ahiO+8jOW8gOWktOeUqOeWkemXruWPpeinpuWPiueUn+a0u+eXm+eCue+8jOW8uuiwg+WunueUqOaAp+WSjOeUn+a0u+WTgei0qOaPkOWNh++8jOivreiogOa4qemmqOi0tOi/keeUn+a0uzwvcD4NCiAgICAgICAgPGJyPg0KICAgICAgICA8aDU+4pyN77iPIOe8luWGmeaKgOW3p++8mjwvaDU+DQogICAgICAgIDxwPuKAoiA8c3Ryb25nPueWkemXruW8gOWktO+8mjwvc3Ryb25nPueUqCLkvaDmmK/lkKbmg7PopoEuLi4i44CBIuS9oOefpemBk+WQly4uLiLnrYnnlpHpl67lj6XlvIDlpLQ8L3A+DQogICAgICAgIDxwPuKAoiA8c3Ryb25nPumhuuWPo+aYk+ivu++8mjwvc3Ryb25nPumBv+WFjeaLl+WPo+ivjeaxh++8jOWkmueUqOefreWPpe+8jOmAguWQiOacl+ivuzwvcD4NCiAgICAgICAgPHA+4oCiIDxzdHJvbmc+5pyL5Y+L6K+t5rCU77yaPC9zdHJvbmc+5rip5ZKM5Lqy5YiH77yM5YOP5pyL5Y+L5YiG5Lqr77yM6JCl6ZSA5oSf5Li6MDwvcD4NCiAgICAgICAgPHA+4oCiIDxzdHJvbmc+5YW35L2T5o+P6L+w77yaPC9zdHJvbmc+57uT5ZCI5oKo55qE5bqX6ZO654m56Imy77yM6LaK5YW35L2T6LaK5aW9PC9wPg0KICAgICAgYCwgJ0FJ5Ymq6L6R5paH5qGI5o+Q56S66K+N5oyH5Y2XJywgew0KICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn55+l6YGT5LqGJywNCiAgICAgICAgY3VzdG9tQ2xhc3M6ICdwcm9tcHQtaGVscC1kaWFsb2cnDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Yib5bu65paH5qGI5bqTDQogICAgY3JlYXRlTGlicmFyeSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY3JlYXRlTGlicmFyeUZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuY3JlYXRpbmcgPSB0cnVlDQoNCiAgICAgICAgICBjb25zdCBsaWJyYXJ5RGF0YSA9IHsNCiAgICAgICAgICAgIGxpYnJhcnlOYW1lOiB0aGlzLmNyZWF0ZUxpYnJhcnlGb3JtLm5hbWUsDQogICAgICAgICAgICB1c2VBaTogdGhpcy5jcmVhdGVMaWJyYXJ5Rm9ybS51c2VBSSwNCiAgICAgICAgICAgIHNob3BEZXRhaWxzOiB0aGlzLmNyZWF0ZUxpYnJhcnlGb3JtLnNob3BEZXRhaWxzLA0KICAgICAgICAgICAgcHJvbXB0OiB0aGlzLmNyZWF0ZUxpYnJhcnlGb3JtLnByb21wdCwNCiAgICAgICAgICAgIHRhcmdldENvdW50OiB0aGlzLmNyZWF0ZUxpYnJhcnlGb3JtLnVzZUFJID8gdGhpcy5jcmVhdGVMaWJyYXJ5Rm9ybS5jb3VudCA6IDAsDQogICAgICAgICAgICB3b3JkQ291bnQ6IHBhcnNlSW50KHRoaXMuY3JlYXRlTGlicmFyeUZvcm0ud29yZENvdW50KQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGFkZExpYnJhcnkobGlicmFyeURhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofmoYjlupPliJvlu7rmiJDlip/vvIEnKQ0KICAgICAgICAgICAgdGhpcy5jcmVhdGVMaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLmxvYWRMaWJyYXJ5TGlzdCgpDQoNCiAgICAgICAgICAgIC8vIOWmguaenOS9v+eUqEFJ55Sf5oiQ77yM5ZCv5Yqo55Sf5oiQ5Lu75YqhDQogICAgICAgICAgICBpZiAodGhpcy5jcmVhdGVMaWJyYXJ5Rm9ybS51c2VBSSkgew0KICAgICAgICAgICAgICB0aGlzLnN0YXJ0R2VuZXJhdGlvbihyZXNwb25zZS5kYXRhLmxpYnJhcnlJZCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliJvlu7rmlofmoYjlupPlpLHotKXvvIzlsJ3or5Xkvb/nlKjmtYvor5VBUEknLCBlcnJvcikNCg0KICAgICAgICAgICAgLy8g5L2/55So5qih5ouf5Yib5bu65pa55qGIDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ato+WcqOS9v+eUqOaooeaLn+aWueahiOWIm+W7uuaWh+ahiOW6ky4uLicpDQoNCiAgICAgICAgICAgIC8vIOaooeaLn+WIm+W7uuaIkOWKn+eahOWTjeW6lA0KICAgICAgICAgICAgY29uc3QgbW9ja0xpYnJhcnkgPSB7DQogICAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpLA0KICAgICAgICAgICAgICBsaWJyYXJ5SWQ6IERhdGUubm93KCksDQogICAgICAgICAgICAgIG5hbWU6IGxpYnJhcnlEYXRhLmxpYnJhcnlOYW1lLA0KICAgICAgICAgICAgICBsaWJyYXJ5TmFtZTogbGlicmFyeURhdGEubGlicmFyeU5hbWUsDQogICAgICAgICAgICAgIHVzZUFJOiBsaWJyYXJ5RGF0YS51c2VBaSwNCiAgICAgICAgICAgICAgdXNlQWk6IGxpYnJhcnlEYXRhLnVzZUFpLA0KICAgICAgICAgICAgICBzaG9wRGV0YWlsczogbGlicmFyeURhdGEuc2hvcERldGFpbHMsDQogICAgICAgICAgICAgIHByb21wdDogbGlicmFyeURhdGEucHJvbXB0LA0KICAgICAgICAgICAgICB0YXJnZXRDb3VudDogbGlicmFyeURhdGEudGFyZ2V0Q291bnQsDQogICAgICAgICAgICAgIGdlbmVyYXRlZENvdW50OiAwLA0KICAgICAgICAgICAgICB3b3JkQ291bnQ6IGxpYnJhcnlEYXRhLndvcmRDb3VudCwNCiAgICAgICAgICAgICAgc3RhdHVzOiAncGVuZGluZycsDQogICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKSwNCiAgICAgICAgICAgICAgY3JlYXRlQnk6ICdkZW1vJw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDlsIbmqKHmi5/mlbDmja7mt7vliqDliLDmnKzlnLDliJfooajkuK0NCiAgICAgICAgICAgIHRoaXMubGlicmFyeUxpc3QudW5zaGlmdChtb2NrTGlicmFyeSkNCg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofmoYjlupPliJvlu7rmiJDlip/vvIEnKQ0KICAgICAgICAgICAgdGhpcy5jcmVhdGVMaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IGZhbHNlDQoNCiAgICAgICAgICAgIC8vIOWmguaenOS9v+eUqEFJ55Sf5oiQ77yM5ZCv5Yqo55yf5a6e55qE55Sf5oiQ5rWB56iLDQogICAgICAgICAgICBpZiAodGhpcy5jcmVhdGVMaWJyYXJ5Rm9ybS51c2VBSSkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOWQr+WKqEFJ5paH5qGI55Sf5oiQ77yM6K+356iN5YCZLi4uJykNCg0KICAgICAgICAgICAgICAvLyDlkK/liqjnlJ/miJDov5vluqbnm5HmjqcNCiAgICAgICAgICAgICAgdGhpcy5zdGFydEdlbmVyYXRpb25Nb25pdG9yaW5nKG1vY2tMaWJyYXJ5LmxpYnJhcnlJZCkNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgdGhpcy5jcmVhdGluZyA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5ZCv5YqoQUnnlJ/miJDku7vliqENCiAgICBzdGFydEdlbmVyYXRpb24obGlicmFyeUlkKSB7DQogICAgICBjb25zdCBsaWJyYXJ5ID0gdGhpcy5saWJyYXJ5TGlzdC5maW5kKGxpYiA9PiBsaWIubGlicmFyeUlkID09PSBsaWJyYXJ5SWQpDQogICAgICBpZiAobGlicmFyeSkgew0KICAgICAgICBnZW5lcmF0ZUNvcHl3cml0aW5nKHsNCiAgICAgICAgICBsaWJyYXJ5SWQ6IGxpYnJhcnlJZCwNCiAgICAgICAgICBzaG9wRGV0YWlsczogbGlicmFyeS5zaG9wRGV0YWlscywNCiAgICAgICAgICBwcm9tcHQ6IGxpYnJhcnkucHJvbXB0LA0KICAgICAgICAgIGNvdW50OiBsaWJyYXJ5LnRhcmdldENvdW50LA0KICAgICAgICAgIHdvcmRDb3VudDogbGlicmFyeS53b3JkQ291bnQNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCdBSeaWh+ahiOeUn+aIkOS7u+WKoeW3suWQr+WKqCcpDQogICAgICAgICAgdGhpcy5tb25pdG9yUHJvZ3Jlc3MobGlicmFyeUlkKQ0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5ZCv5Yqo55Sf5oiQ5Lu75Yqh5aSx6LSlJywgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5ZCv5Yqo55Sf5oiQ5Lu75Yqh5aSx6LSl77yaJyArIChlcnJvci5tc2cgfHwgZXJyb3IubWVzc2FnZSkpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOebkeaOp+eUn+aIkOi/m+W6pg0KICAgIG1vbml0b3JQcm9ncmVzcyhsaWJyYXJ5SWQpIHsNCiAgICAgIGNvbnN0IGNoZWNrUHJvZ3Jlc3MgPSAoKSA9PiB7DQogICAgICAgIGdldFByb2dyZXNzKGxpYnJhcnlJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc3QgbGlicmFyeSA9IHRoaXMubGlicmFyeUxpc3QuZmluZChsaWIgPT4gbGliLmxpYnJhcnlJZCA9PT0gbGlicmFyeUlkKQ0KICAgICAgICAgIGlmIChsaWJyYXJ5KSB7DQogICAgICAgICAgICBsaWJyYXJ5LmdlbmVyYXRlZENvdW50ID0gcHJvZ3Jlc3MuZ2VuZXJhdGVkQ291bnQNCiAgICAgICAgICAgIGxpYnJhcnkuc3RhdHVzID0gcHJvZ3Jlc3Muc3RhdHVzDQoNCiAgICAgICAgICAgIGlmIChwcm9ncmVzcy5zdGF0dXMgPT09ICdnZW5lcmF0aW5nJykgew0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KGNoZWNrUHJvZ3Jlc3MsIDIwMDApIC8vIOavjzLnp5Lmo4Dmn6XkuIDmrKENCiAgICAgICAgICAgIH0gZWxzZSBpZiAocHJvZ3Jlc3Muc3RhdHVzID09PSAnY29tcGxldGVkJykgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7bGlicmFyeS5saWJyYXJ5TmFtZX0g55Sf5oiQ5a6M5oiQ77yBYCkNCiAgICAgICAgICAgIH0gZWxzZSBpZiAocHJvZ3Jlc3Muc3RhdHVzID09PSAnZmFpbGVkJykgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke2xpYnJhcnkubGlicmFyeU5hbWV9IOeUn+aIkOWksei0pWApDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6L+b5bqm5aSx6LSlJywgZXJyb3IpDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjaGVja1Byb2dyZXNzKCkNCiAgICB9LA0KDQoNCg0KICAgIC8vIOafpeeci+aWh+ahiOW6kw0KICAgIHZpZXdMaWJyYXJ5KGxpYnJhcnkpIHsNCiAgICAgIHRoaXMuY3VycmVudExpYnJhcnkgPSBsaWJyYXJ5DQogICAgICB0aGlzLmxvYWRMaWJyYXJ5Q29udGVudHMobGlicmFyeS5pZCkNCiAgICAgIHRoaXMudmlld0xpYnJhcnlEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3mlofmoYjlupPlhoXlrrkNCiAgICBsb2FkTGlicmFyeUNvbnRlbnRzKGxpYnJhcnlJZCkgew0KICAgICAgLy8g6aaW5YWI5bCd6K+V5LuO5oyB5LmF5YyW5a2Y5YKo5Lit5Yqg6L29DQogICAgICBpZiAodGhpcy5saWJyYXJ5Q29udGVudFN0b3JhZ2UgJiYgdGhpcy5saWJyYXJ5Q29udGVudFN0b3JhZ2VbbGlicmFyeUlkXSkgew0KICAgICAgICB0aGlzLmxpYnJhcnlDb250ZW50cyA9IHRoaXMubGlicmFyeUNvbnRlbnRTdG9yYWdlW2xpYnJhcnlJZF0NCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LliqDovb0ke3RoaXMubGlicmFyeUNvbnRlbnRzLmxlbmd0aH3mnaHmlofmoYjlhoXlrrlgKQ0KICAgICAgICBjb25zb2xlLmxvZygn5LuO5oyB5LmF5YyW5a2Y5YKo5Yqg6L295paH5qGI5YaF5a65OicsIHRoaXMubGlicmFyeUNvbnRlbnRzKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5oyB5LmF5YyW5a2Y5YKo5Lit5rKh5pyJ77yM5bCd6K+VQVBJDQogICAgICBsaXN0Q29udGVudChsaWJyYXJ5SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmxpYnJhcnlDb250ZW50cyA9IHJlc3BvbnNlLnJvd3MgfHwgcmVzcG9uc2UuZGF0YSB8fCBbXQ0KICAgICAgICBpZiAodGhpcy5saWJyYXJ5Q29udGVudHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5YaF5a6577yM5Yqg6L295qih5ouf5YaF5a65DQogICAgICAgICAgdGhpcy5sb2FkTW9ja0NvbnRlbnRzKGxpYnJhcnlJZCkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlofmoYjlupPlhoXlrrnlpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja4nLCBlcnJvcikNCg0KICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/nmbvlvZXov4fmnJ/plJnor68NCiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQwMSB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygn55m75b2VJykgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ+i/h+acnycpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmo4DmtYvliLDnmbvlvZXnirbmgIHov4fmnJ/vvIzmraPlnKjkvb/nlKjmvJTnpLrmqKHlvI/liqDovb3lhoXlrrkuLi4nKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5Yqg6L295qih5ouf5YaF5a65DQogICAgICAgIHRoaXMubG9hZE1vY2tDb250ZW50cyhsaWJyYXJ5SWQpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3mqKHmi5/mlofmoYjlhoXlrrkNCiAgICBsb2FkTW9ja0NvbnRlbnRzKGxpYnJhcnlJZCkgew0KICAgICAgY29uc29sZS5sb2coJ+WKoOi9veaooeaLn+aWh+ahiOWGheWuue+8jGxpYnJhcnlJZDonLCBsaWJyYXJ5SWQpDQoNCiAgICAgIGNvbnN0IG1vY2tDb250ZW50cyA9IHsNCiAgICAgICAgMTogWyAvLyDnvo7po5/mjqLlupfmlofmoYjlupMNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZDogMSwNCiAgICAgICAgICAgIGNvbnRlbnRJZDogMSwNCiAgICAgICAgICAgIGxpYnJhcnlJZDogMSwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfwn42977iPIOaOouW6l+aWsOWPkeeOsO+8gei/meWutumakOiXj+WcqOW3t+WtkOmHjOeahOWwj+mkkOWOhe+8jOeUqOacgOactOWunueahOmjn+adkOWBmuWHuuS6huacgOaDiuiJs+eahOWRs+mBk+OAguaLm+eJjOe6oueDp+iCieWFpeWPo+WNs+WMlu+8jOmFjeiPnOa4heeIveino+iFu++8jOiAgeadv+WomOeahOaJi+iJuuecn+aYr+ayoeivneivtO+8geS6uuWdh+a2iOi0ueS4jeWIsDUw5YWD77yM5oCn5Lu35q+U6LaF6auY77yM5by654OI5o6o6I2Q57uZ54ix576O6aOf55qE5pyL5Y+L5Lus77yBJywNCiAgICAgICAgICAgIHRpdGxlOiAnQUnnlJ/miJAt576O6aOf5o6i5bqX5paH5qGIMScsDQogICAgICAgICAgICB3b3JkQ291bnQ6IDk4LA0KICAgICAgICAgICAgaXNBaUdlbmVyYXRlZDogdHJ1ZSwNCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsDQogICAgICAgICAgICBxdWFsaXR5U2NvcmU6IDkyLA0KICAgICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDEtMTUgMTE6MDA6MDAnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZDogMiwNCiAgICAgICAgICAgIGNvbnRlbnRJZDogMiwNCiAgICAgICAgICAgIGxpYnJhcnlJZDogMSwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfwn4yfIOWPiOS4gOWutuWuneiXj+mkkOWOheiiq+aIkeWPkeeOsOS6hu+8geeOr+Wig+a4qemmqOmbheiHtO+8jOacjeWKoei0tOW/g+WRqOWIsO+8jOacgOmHjeimgeeahOaYr+iPnOWTgeecn+eahOWkquajkuS6hu+8geeJueiJsueDpOmxvOmynOWrqeWkmuaxge+8jOenmOWItumFseaWmeWxguasoeS4sOWvjO+8jOavj+S4gOWPo+mDveaYr+S6q+WPl+OAguWSjOaci+WPi+iBmumkkOeahOWujOe+jumAieaLqe+8jOiusOW+l+aPkOWJjemihOe6puWTpu+8gScsDQogICAgICAgICAgICB0aXRsZTogJ0FJ55Sf5oiQLee+jumjn+aOouW6l+aWh+ahiDInLA0KICAgICAgICAgICAgd29yZENvdW50OiA4NSwNCiAgICAgICAgICAgIGlzQWlHZW5lcmF0ZWQ6IHRydWUsDQogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLA0KICAgICAgICAgICAgcXVhbGl0eVNjb3JlOiA4OCwNCiAgICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTAxLTE1IDExOjE1OjAwJw0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgMjogWyAvLyDml7blsJrmnI3oo4Xmjqjlub/lupMNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZDogMywNCiAgICAgICAgICAgIGNvbnRlbnRJZDogMywNCiAgICAgICAgICAgIGxpYnJhcnlJZDogMiwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfinKgg5pil5a2j5paw5ZOB5LiK5biC77yB6L+Z5Lu26L+e6KGj6KOZ55qE6K6+6K6h566A55u05aSq576O5LqG77yM5LyY6ZuF55qEQeWtl+eJiOWei+S/rumlsOi6q+W9ou+8jOeyvuiHtOeahOiVvuS4nee7huiKguWinua3u+Wls+aAp+mtheWKm+OAgumdouaWmeaflOi9r+iIkumAgu+8jOminOiJsua4heaWsOa3oembhe+8jOaXoOiuuuaYr+e6puS8mui/mOaYr+S4iuePremDveiDvei9u+advumpvumpreOAgueOsOWcqOi0reS5sOi/mOaciemZkOaXtuS8mOaDoO+8jOS4jeimgemUmei/h+WTpu+8gScsDQogICAgICAgICAgICB0aXRsZTogJ0FJ55Sf5oiQLeaXtuWwmuacjeijheaWh+ahiDEnLA0KICAgICAgICAgICAgd29yZENvdW50OiA5MiwNCiAgICAgICAgICAgIGlzQWlHZW5lcmF0ZWQ6IHRydWUsDQogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLA0KICAgICAgICAgICAgcXVhbGl0eVNjb3JlOiA5MCwNCiAgICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTAxLTEwIDE1OjAwOjAwJw0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgMzogWyAvLyDlkpbllaHljoXmuKnppqjmlofmoYjlupMNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZDogNCwNCiAgICAgICAgICAgIGNvbnRlbnRJZDogNCwNCiAgICAgICAgICAgIGxpYnJhcnlJZDogMywNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfimJUg5rip6aao5ZKW5ZWh5pe25YWJ77yM562J5L2g5p2l5Lqr5Y+X77yB57K+6YCJ5LyY6LSo5ZKW5ZWh6LGG77yM5omL5bel6LCD5Yi25q+P5LiA5p2v77yM5pCt6YWN57K+6Ie055Sc54K577yM6K6p5L2g55qE5Y2I5ZCO5pe25YWJ5pu05Yqg576O5aW944CC5Zyo6L+Z6YeM77yM5L2g5Y+v5Lul5pS+5oWi6ISa5q2l77yM5Lqr5Y+X55Sf5rS755qE576O5aW9556s6Ze044CC5b+r5p2l5L2T6aqM5oiR5Lus55qE5rip6aao5pyN5Yqh5ZCn77yBJywNCiAgICAgICAgICAgIHRpdGxlOiAnQUnnlJ/miJAt5ZKW5ZWh5Y6F5paH5qGIMScsDQogICAgICAgICAgICB3b3JkQ291bnQ6IDc4LA0KICAgICAgICAgICAgaXNBaUdlbmVyYXRlZDogdHJ1ZSwNCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsDQogICAgICAgICAgICBxdWFsaXR5U2NvcmU6IDg4LA0KICAgICAgICAgICAgY3JlYXRlVGltZTogJzIwMjQtMDEtMjAgMTA6MDA6MDAnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZDogNSwNCiAgICAgICAgICAgIGNvbnRlbnRJZDogNSwNCiAgICAgICAgICAgIGxpYnJhcnlJZDogMywNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfwn4yfIOWTgeWRs+eUn+a0u++8jOS7juS4gOadr+WlveWSluWVoeW8gOWni++8geaIkeS7rOeahOWSluWVoeWOheS4jeS7heaciemmmemGh+eahOWSluWVoe+8jOi/mOaciea4qemmqOeahOeOr+Wig+WSjOi0tOW/g+eahOacjeWKoeOAguavj+S4gOWPo+mDveaYr+WvueeUn+a0u+eahOeDreeIse+8jOavj+S4gOWIu+mDveWAvOW+l+ePjeiXj+OAguadpei/memHjO+8jOiuqeW/g+eBteW+l+WIsOeJh+WIu+eahOWugemdme+8gScsDQogICAgICAgICAgICB0aXRsZTogJ0FJ55Sf5oiQLeWSluWVoeWOheaWh+ahiDInLA0KICAgICAgICAgICAgd29yZENvdW50OiA3MSwNCiAgICAgICAgICAgIGlzQWlHZW5lcmF0ZWQ6IHRydWUsDQogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLA0KICAgICAgICAgICAgcXVhbGl0eVNjb3JlOiA5MiwNCiAgICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI0LTAxLTIwIDEwOjMwOjAwJw0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmxpYnJhcnlDb250ZW50cyA9IG1vY2tDb250ZW50c1tsaWJyYXJ5SWRdIHx8IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3suWKoOi9vSR7dGhpcy5saWJyYXJ5Q29udGVudHMubGVuZ3RofeadoeaooeaLn+aWh+ahiOWGheWuuWApDQogICAgfSwNCg0KICAgIC8vIOWQr+WKqOeUn+aIkOi/m+W6puebkeaOpw0KICAgIHN0YXJ0R2VuZXJhdGlvbk1vbml0b3JpbmcobGlicmFyeUlkKSB7DQogICAgICBjb25zb2xlLmxvZygn5ZCv5Yqo55Sf5oiQ6L+b5bqm55uR5o6n77yMbGlicmFyeUlkOicsIGxpYnJhcnlJZCkNCg0KICAgICAgLy8g5p+l5om+5a+55bqU55qE5paH5qGI5bqTDQogICAgICBjb25zdCBsaWJyYXJ5ID0gdGhpcy5saWJyYXJ5TGlzdC5maW5kKGxpYiA9PiBsaWIubGlicmFyeUlkID09PSBsaWJyYXJ5SWQgfHwgbGliLmlkID09PSBsaWJyYXJ5SWQpDQogICAgICBpZiAoIWxpYnJhcnkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+acquaJvuWIsOaWh+ahiOW6k++8jOWBnOatouebkeaOpycpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDliJ3lp4vljJbmlofmoYjlupPnmoTlhoXlrrnlrZjlgqgNCiAgICAgIGlmICghdGhpcy5saWJyYXJ5Q29udGVudFN0b3JhZ2UpIHsNCiAgICAgICAgdGhpcy5saWJyYXJ5Q29udGVudFN0b3JhZ2UgPSB7fQ0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmxpYnJhcnlDb250ZW50U3RvcmFnZVtsaWJyYXJ5SWRdKSB7DQogICAgICAgIHRoaXMubGlicmFyeUNvbnRlbnRTdG9yYWdlW2xpYnJhcnlJZF0gPSBbXQ0KICAgICAgfQ0KDQogICAgICBsaWJyYXJ5LnN0YXR1cyA9ICdnZW5lcmF0aW5nJw0KDQogICAgICAvLyDnlJ/miJDmiYDmnInmlofmoYgNCiAgICAgIGNvbnN0IGdlbmVyYXRlQWxsQ29udGVudCA9ICgpID0+IHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gbGlicmFyeS50YXJnZXRDb3VudDsgaSsrKSB7DQogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAvLyDnlJ/miJDmlofmoYjlhoXlrrkNCiAgICAgICAgICAgIGNvbnN0IG5ld0NvbnRlbnQgPSB0aGlzLmdlbmVyYXRlTW9ja0NvbnRlbnQobGlicmFyeSwgaSkNCg0KICAgICAgICAgICAgLy8g5a2Y5YKo5Yiw5oyB5LmF5YyW5a2Y5YKo5LitDQogICAgICAgICAgICB0aGlzLmxpYnJhcnlDb250ZW50U3RvcmFnZVtsaWJyYXJ5SWRdLnB1c2gobmV3Q29udGVudCkNCg0KICAgICAgICAgICAgLy8g5pu05paw55Sf5oiQ5pWw6YePDQogICAgICAgICAgICBsaWJyYXJ5LmdlbmVyYXRlZENvdW50ID0gaQ0KDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaWh+ahiOW6kyIke2xpYnJhcnkubGlicmFyeU5hbWUgfHwgbGlicmFyeS5uYW1lfSLlt7LnlJ/miJDnrKwke2l95p2h5paH5qGIYCkNCg0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5pyA5ZCO5LiA5p2h77yM5qCH6K6w5a6M5oiQDQogICAgICAgICAgICBpZiAoaSA9PT0gbGlicmFyeS50YXJnZXRDb3VudCkgew0KICAgICAgICAgICAgICBsaWJyYXJ5LnN0YXR1cyA9ICdjb21wbGV0ZWQnDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg8J+OiSDmlofmoYjlupMiJHtsaWJyYXJ5LmxpYnJhcnlOYW1lIHx8IGxpYnJhcnkubmFtZX0i55Sf5oiQ5a6M5oiQ77yB5YWx55Sf5oiQJHtsaWJyYXJ5LmdlbmVyYXRlZENvdW50feadoeaWh+ahiGApDQoNCiAgICAgICAgICAgICAgLy8g5L+d5a2Y5YiwbG9jYWxTdG9yYWdlDQogICAgICAgICAgICAgIHRoaXMuc2F2ZUxpYnJhcnlDb250ZW50VG9TdG9yYWdlKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LCBpICogMjAwMCkgLy8g5q+PMuenkueUn+aIkOS4gOadoQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOW8gOWni+eUn+aIkA0KICAgICAgc2V0VGltZW91dChnZW5lcmF0ZUFsbENvbnRlbnQsIDEwMDApDQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaooeaLn+aWh+ahiOWGheWuue+8iEFJ5Ymq6L6R5paH5qGI5LiT55So77yJDQogICAgZ2VuZXJhdGVNb2NrQ29udGVudChsaWJyYXJ5LCBpbmRleCkgew0KICAgICAgY29uc3QgdGFyZ2V0V29yZENvdW50ID0gbGlicmFyeS53b3JkQ291bnQgfHwgMjAwDQoNCiAgICAgIC8vIOWbuuWumuS9v+eUqEFJ5Ymq6L6R5paH5qGI77yIdmlkZW/vvInnmoTnlJ/miJDnrZbnlaUNCiAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlVmlkZW9Db250ZW50KGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpDQogICAgfSwNCg0KICAgIC8vIOagueaNruW5s+WPsOeUn+aIkOS4k+WxnuaWh+ahiA0KICAgIGdlbmVyYXRlUGxhdGZvcm1TcGVjaWZpY0NvbnRlbnQocGxhdGZvcm0sIGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpIHsNCiAgICAgIHN3aXRjaCAocGxhdGZvcm0pIHsNCiAgICAgICAgY2FzZSAndmlkZW8nOiAvLyBBSeWJqui+keaWh+ahiO+8iOWPo+aSre+8iQ0KICAgICAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlVmlkZW9Db250ZW50KGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpDQogICAgICAgIGNhc2UgJ2RvdXlpbic6IC8vIOaKlumfsy/lv6vmiYvmlofmoYgNCiAgICAgICAgY2FzZSAna3VhaXNob3UnOg0KICAgICAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlU2hvcnRWaWRlb0NvbnRlbnQobGlicmFyeSwgaW5kZXgsIHRhcmdldFdvcmRDb3VudCkNCiAgICAgICAgY2FzZSAneGlhb2hvbmdzaHUnOiAvLyDlsI/nuqLkuabmlofmoYgNCiAgICAgICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZVhpYW9ob25nc2h1Q29udGVudChsaWJyYXJ5LCBpbmRleCwgdGFyZ2V0V29yZENvdW50KQ0KICAgICAgICBjYXNlICdyZXZpZXcnOiAvLyDngrnor4Qv5pyL5Y+L5ZyI5paH5qGIDQogICAgICAgIGNhc2UgJ21vbWVudHMnOg0KICAgICAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlUmV2aWV3Q29udGVudChsaWJyYXJ5LCBpbmRleCwgdGFyZ2V0V29yZENvdW50KQ0KICAgICAgICBkZWZhdWx0OiAvLyDpgJrnlKjmlofmoYgNCiAgICAgICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUdlbmVyYWxDb250ZW50KGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIEFJ5Ymq6L6R5paH5qGI77yI5Y+j5pKt5LiT55So77yJDQogICAgZ2VuZXJhdGVWaWRlb0NvbnRlbnQobGlicmFyeSwgaW5kZXgsIHRhcmdldFdvcmRDb3VudCkgew0KICAgICAgY29uc3QgcXVlc3Rpb25TdGFydGVycyA9IFsNCiAgICAgICAgJ+S9oOaYr+WQpuaDs+imgScsDQogICAgICAgICfkvaDmnInmsqHmnInpgYfliLDov4cnLA0KICAgICAgICAn5L2g55+l6YGT5ZCXJywNCiAgICAgICAgJ+S9oOi/mOWcqOS4uicsDQogICAgICAgICfkvaDmg7PkuI3mg7MnLA0KICAgICAgICAn5L2g5pyJ5rKh5pyJ5Y+R546wJywNCiAgICAgICAgJ+S9oOaYr+S4jeaYr+S5nycsDQogICAgICAgICfkvaDmnInmsqHmnInmg7Pov4cnDQogICAgICBdDQoNCiAgICAgIGNvbnN0IHZpZGVvRnJhZ21lbnRzID0gWw0KICAgICAgICBgJHtsaWJyYXJ5LnNob3BEZXRhaWxzIHx8ICfmiJHku6wnfeS4k+azqOS6juS4uuaCqOaPkOS+m+acgOS8mOi0qOeahOacjeWKoeOAgmAsDQogICAgICAgIGDov5nph4zkuI3ku4Xku4XmmK/kuIDkuKrlnLDmlrnvvIzmm7TmmK/kuIDnp43nlJ/mtLvmlrnlvI/nmoTkvZPnjrDjgIJgLA0KICAgICAgICBg5oiR5Lus55So5b+D5YGa5aW95q+P5LiA5Liq57uG6IqC77yM5Y+q5Li657uZ5oKo5bim5p2l5pyA5aW955qE5L2T6aqM44CCYCwNCiAgICAgICAgYOmAieaLqeaIkeS7rO+8jOWwseaYr+mAieaLqeWTgei0qOWSjOS/oei1luOAgmAsDQogICAgICAgIGDlnKjov5nph4zvvIzmgqjkvJrlj5HnjrDkuI3kuIDmoLfnmoTnsr7lvanjgIJgDQogICAgICBdDQoNCiAgICAgIGNvbnN0IHF1ZXN0aW9uU3RhcnQgPSBxdWVzdGlvblN0YXJ0ZXJzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHF1ZXN0aW9uU3RhcnRlcnMubGVuZ3RoKV0NCiAgICAgIGxldCBjb250ZW50ID0gYCR7cXVlc3Rpb25TdGFydH0ke2xpYnJhcnkucHJvbXB0IHx8ICfkvZPpqozkuI3kuIDmoLfnmoTmnI3liqEnfe+8n2ANCg0KICAgICAgLy8g5re75Yqg5YaF5a6554mH5q6155u05Yiw6L6+5Yiw55uu5qCH5a2X5pWwDQogICAgICB3aGlsZSAoY29udGVudC5sZW5ndGggPCB0YXJnZXRXb3JkQ291bnQgLSAzMCkgew0KICAgICAgICBjb25zdCBmcmFnbWVudCA9IHZpZGVvRnJhZ21lbnRzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHZpZGVvRnJhZ21lbnRzLmxlbmd0aCldDQogICAgICAgIGNvbnRlbnQgKz0gZnJhZ21lbnQNCiAgICAgICAgaWYgKGNvbnRlbnQubGVuZ3RoID4gdGFyZ2V0V29yZENvdW50ICsgMjApIGJyZWFrDQogICAgICB9DQoNCiAgICAgIHJldHVybiB0aGlzLmNyZWF0ZUNvbnRlbnRPYmplY3QobGlicmFyeSwgaW5kZXgsIGNvbnRlbnQsICflj6Pmkq3mlofmoYgnKQ0KICAgIH0sDQoNCiAgICAvLyDmipbpn7Mv5b+r5omL5paH5qGI77yI566A55+t5pyJ5Yqb77yJDQogICAgZ2VuZXJhdGVTaG9ydFZpZGVvQ29udGVudChsaWJyYXJ5LCBpbmRleCwgdGFyZ2V0V29yZENvdW50KSB7DQogICAgICBjb25zdCBob3RUcmVuZHMgPSBbJ3l5ZHMnLCAn57ud57ud5a2QJywgJ+WkqummmeS6hicsICfniLHkuobniLHkuoYnLCAn6L+Z6LCB6aG25b6X5L2PJywgJ+ebtOaOpeaLv+S4iycsICflv4XpobvlronmjpInXQ0KICAgICAgY29uc3Qgc2hvcnRGcmFnbWVudHMgPSBbDQogICAgICAgIGAke2xpYnJhcnkuc2hvcERldGFpbHMgfHwgJ+i/meWutuW6lyd955yf55qEJHtob3RUcmVuZHNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogaG90VHJlbmRzLmxlbmd0aCldfe+8gWAsDQogICAgICAgIGDlp5Dlprnku6zvvIzov5nkuKrlv4XpobvlhrLvvIFgLA0KICAgICAgICBg5LiN5piv5oiR5ZC577yM6L+Z5Liq55yf55qE5b6I5qOS77yBYCwNCiAgICAgICAgYOi/meS4quWuneiXj+W6l+mTuue7iOS6juiiq+aIkeWPkeeOsOS6hu+8gWAsDQogICAgICAgIGDmnIvlj4vku6zvvIzov5nms6LkuI3kuo/vvIFgDQogICAgICBdDQoNCiAgICAgIGxldCBjb250ZW50ID0gc2hvcnRGcmFnbWVudHNbaW5kZXggJSBzaG9ydEZyYWdtZW50cy5sZW5ndGhdDQoNCiAgICAgIC8vIOS/neaMgeeugOefre+8jOmAguWQiOefreinhumikQ0KICAgICAgY29uc3QgbWF4TGVuZ3RoID0gTWF0aC5taW4odGFyZ2V0V29yZENvdW50LCA4MCkNCiAgICAgIGlmIChjb250ZW50Lmxlbmd0aCA8IG1heExlbmd0aCAtIDIwKSB7DQogICAgICAgIGNvbnRlbnQgKz0gYCR7bGlicmFyeS5wcm9tcHQgfHwgJ+ecn+eahOWAvOW+l+S4gOivlSd977yM5b+r5Y675L2T6aqM5ZCn77yBYA0KICAgICAgfQ0KDQogICAgICByZXR1cm4gdGhpcy5jcmVhdGVDb250ZW50T2JqZWN0KGxpYnJhcnksIGluZGV4LCBjb250ZW50LCAn55+t6KeG6aKR5paH5qGIJykNCiAgICB9LA0KDQogICAgLy8g5bCP57qi5Lmm5paH5qGI77yI5YiG5q61K2Vtb2pp5Liw5a+M77yJDQogICAgZ2VuZXJhdGVYaWFvaG9uZ3NodUNvbnRlbnQobGlicmFyeSwgaW5kZXgsIHRhcmdldFdvcmRDb3VudCkgew0KICAgICAgY29uc3QgZW1vamlzID0gWyfinKgnLCAn8J+SlScsICfwn4yfJywgJ/CfkpYnLCAn8J+OgCcsICfwn4y4JywgJ/CfkqsnLCAn8J+miycsICfwn4y6JywgJ/CfkpAnLCAn8J+OqCcsICfwn4yIJywgJ/Cfko4nLCAn8J+OqicsICfwn46tJ10NCiAgICAgIGNvbnN0IHhpYW9ob25nc2h1U3RhcnRlcnMgPSBbDQogICAgICAgICflp5Dlprnku6zvvIHku4rlpKnopoHliIbkuqvkuIDkuKrlrp3ol48nLA0KICAgICAgICAn55yf55qE5LiN5piv5oiR5ZC5JywNCiAgICAgICAgJ+i/meS4quecn+eahOWkquWlveS6hicsDQogICAgICAgICfnu4jkuo7mib7liLDkuoYnLA0KICAgICAgICAn5aeQ5aa55Lus55yL6L+H5p2lJw0KICAgICAgXQ0KDQogICAgICBsZXQgY29udGVudCA9IGAke3hpYW9ob25nc2h1U3RhcnRlcnNbaW5kZXggJSB4aWFvaG9uZ3NodVN0YXJ0ZXJzLmxlbmd0aF19JHtlbW9qaXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZW1vamlzLmxlbmd0aCldfVxuXG5gDQogICAgICBjb250ZW50ICs9IGAke2xpYnJhcnkuc2hvcERldGFpbHMgfHwgJ+i/meS4quWcsOaWuSd955yf55qE6K6p5oiR5oOK5ZacJHtlbW9qaXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZW1vamlzLmxlbmd0aCldfVxuXG5gDQoNCiAgICAgIC8vIOa3u+WKoOWIhuauteWGheWuuQ0KICAgICAgY29uc3Qgc2VnbWVudHMgPSBbDQogICAgICAgIGDnjq/looPotoXnuqfmo5Ike2Vtb2ppc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBlbW9qaXMubGVuZ3RoKV19YCwNCiAgICAgICAgYOacjeWKoeaAgeW6puS5n+W+iOWlvSR7ZW1vamlzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGVtb2ppcy5sZW5ndGgpXX1gLA0KICAgICAgICBg5oCn5Lu35q+U55yf55qE5b6I6auYJHtlbW9qaXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZW1vamlzLmxlbmd0aCldfWAsDQogICAgICAgIGDlvLrng4jmjqjojZDnu5nlpKflrrYke2Vtb2ppc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBlbW9qaXMubGVuZ3RoKV19YA0KICAgICAgXQ0KDQogICAgICBzZWdtZW50cy5mb3JFYWNoKHNlZ21lbnQgPT4gew0KICAgICAgICBjb250ZW50ICs9IGAke3NlZ21lbnR9XG5gDQogICAgICB9KQ0KDQogICAgICBjb250ZW50ICs9IGBcbiR7bGlicmFyeS5wcm9tcHQgfHwgJ+ecn+eahOWAvOW+l+S4gOivlSd9JHtlbW9qaXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZW1vamlzLmxlbmd0aCldfWANCg0KICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlQ29udGVudE9iamVjdChsaWJyYXJ5LCBpbmRleCwgY29udGVudCwgJ+Wwj+e6ouS5puaWh+ahiCcpDQogICAgfSwNCg0KICAgIC8vIOeCueivhC/mnIvlj4vlnIjmlofmoYjvvIjmjqXlnLDmsJQr6YCC5b2T6ZSZ5Yir5a2X77yJDQogICAgZ2VuZXJhdGVSZXZpZXdDb250ZW50KGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpIHsNCiAgICAgIGNvbnN0IGNhc3VhbFdvcmRzID0gWyfmjLrkuI3plJnnmoQnLCAn6L+Y6KGMJywgJ+ibruWlveeahCcsICflj6/ku6XnmoQnLCAn5LiN6ZSZ5LiN6ZSZJ10NCiAgICAgIGNvbnN0IHR5cG9zID0gew0KICAgICAgICAn55qEJzogJ+a7tCcsDQogICAgICAgICfov5nkuKonOiAn6L+Z5LiqJywNCiAgICAgICAgJ+ecn+eahCc6ICfnnJ/mu7QnLA0KICAgICAgICAn5aW95ZCDJzogJ+WlveasoScsDQogICAgICAgICfllpzmrKInOiAn56iA6aWtJw0KICAgICAgfQ0KDQogICAgICBsZXQgY29udGVudCA9IGDku4rlpKnlkozmnIvlj4vljrvkuoYke2xpYnJhcnkuc2hvcERldGFpbHMgfHwgJ+i/meWutuW6lyd977yMJHtjYXN1YWxXb3Jkc1tpbmRleCAlIGNhc3VhbFdvcmRzLmxlbmd0aF1944CCYA0KICAgICAgY29udGVudCArPSBg546v5aKD6L+Y5Y+v5Lul77yM5pyN5Yqh5oCB5bqm5Lmf5oy65aW95ru044CCYA0KICAgICAgY29udGVudCArPSBgJHtsaWJyYXJ5LnByb21wdCB8fCAn5oC75L2T5p2l6K+06L+Y5piv5YC85b6X5o6o6I2Q5ru0J33vvIzkuIvmrKHov5jkvJrlho3mnaXjgIJgDQoNCiAgICAgIC8vIOmaj+acuua3u+WKoOS4gOS6m+mUmeWIq+Wtlw0KICAgICAgT2JqZWN0LmtleXModHlwb3MpLmZvckVhY2goa2V5ID0+IHsNCiAgICAgICAgaWYgKE1hdGgucmFuZG9tKCkgPCAwLjMpIHsgLy8gMzAl5qaC546H5pu/5o2iDQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZShrZXksIHR5cG9zW2tleV0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIHJldHVybiB0aGlzLmNyZWF0ZUNvbnRlbnRPYmplY3QobGlicmFyeSwgaW5kZXgsIGNvbnRlbnQsICfngrnor4TmlofmoYgnKQ0KICAgIH0sDQoNCiAgICAvLyDpgJrnlKjmlofmoYjnlJ/miJANCiAgICBnZW5lcmF0ZUdlbmVyYWxDb250ZW50KGxpYnJhcnksIGluZGV4LCB0YXJnZXRXb3JkQ291bnQpIHsNCiAgICAgIGNvbnN0IGJhc2VGcmFnbWVudHMgPSBbDQogICAgICAgIGDwn4yfICR7bGlicmFyeS5zaG9wRGV0YWlscyB8fCAn5oiR5Lus55qE5bqX6ZO6J33vvIzkuLrmgqjluKbmnaXni6znibnnmoTkvZPpqozvvIFgLA0KICAgICAgICBg8J+SqyDlj5HnjrDnvo7lpb3vvIzku47ov5nph4zlvIDlp4vvvIEke2xpYnJhcnkuc2hvcERldGFpbHMgfHwgJ+aIkeS7rOeahOW6l+mTuid977yM5pyf5b6F5oKo55qE5YWJ5Li077yBYCwNCiAgICAgICAgYOKcqCDlk4HotKjnlJ/mtLvvvIznsr7lvanmr4/kuIDlpKnvvIHmnaXkvZPpqozmiJHku6zkuLrmgqjnsr7lv4Plh4blpIfnmoTmnI3liqHlkKfvvIFgDQogICAgICBdDQoNCiAgICAgIGxldCBjb250ZW50ID0gYmFzZUZyYWdtZW50c1tpbmRleCAlIGJhc2VGcmFnbWVudHMubGVuZ3RoXQ0KDQogICAgICAvLyDmoLnmja7nm67moIflrZfmlbDmianlsZXlhoXlrrkNCiAgICAgIHdoaWxlIChjb250ZW50Lmxlbmd0aCA8IHRhcmdldFdvcmRDb3VudCAtIDMwKSB7DQogICAgICAgIGNvbnRlbnQgKz0gYOaIkeS7rOS4k+azqOS6jiR7bGlicmFyeS5wcm9tcHQgfHwgJ+S4uuaCqOaPkOS+m+S8mOi0qOacjeWKoSd977yM55So5b+D5YGa5aW95q+P5LiA5Liq57uG6IqC44CCYA0KICAgICAgICBpZiAoY29udGVudC5sZW5ndGggPiB0YXJnZXRXb3JkQ291bnQgKyAyMCkgYnJlYWsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlQ29udGVudE9iamVjdChsaWJyYXJ5LCBpbmRleCwgY29udGVudCwgJ+mAmueUqOaWh+ahiCcpDQogICAgfSwNCg0KICAgIC8vIOWIm+W7uuaWh+ahiOWGheWuueWvueixoQ0KICAgIGNyZWF0ZUNvbnRlbnRPYmplY3QobGlicmFyeSwgaW5kZXgsIGNvbnRlbnQsIHR5cGUpIHsNCiAgICAgIGNvbnN0IG5ld0NvbnRlbnQgPSB7DQogICAgICAgIGlkOiBEYXRlLm5vdygpICsgaW5kZXgsDQogICAgICAgIGNvbnRlbnRJZDogRGF0ZS5ub3coKSArIGluZGV4LA0KICAgICAgICBsaWJyYXJ5SWQ6IGxpYnJhcnkubGlicmFyeUlkIHx8IGxpYnJhcnkuaWQsDQogICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsDQogICAgICAgIHRpdGxlOiBgQUnnlJ/miJAtJHt0eXBlfS3nrKwke2luZGV4feadoWAsDQogICAgICAgIHdvcmRDb3VudDogY29udGVudC5sZW5ndGgsDQogICAgICAgIGlzQWlHZW5lcmF0ZWQ6IHRydWUsDQogICAgICAgIHN0YXR1czogJ2FjdGl2ZScsDQogICAgICAgIHF1YWxpdHlTY29yZTogODUgKyBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxNSksDQogICAgICAgIGNyZWF0ZVRpbWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZyhg55Sf5oiQ56ysJHtpbmRleH3mnaEke3R5cGV9ICjlrp7pmYUke2NvbnRlbnQubGVuZ3RofeWtlyk6YCwgbmV3Q29udGVudCkNCiAgICAgIHJldHVybiBuZXdDb250ZW50DQogICAgfSwNCg0KICAgIC8vIOS/neWtmOaWh+ahiOW6k+WGheWuueWIsGxvY2FsU3RvcmFnZQ0KICAgIHNhdmVMaWJyYXJ5Q29udGVudFRvU3RvcmFnZSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdsaWJyYXJ5Q29udGVudFN0b3JhZ2UnLCBKU09OLnN0cmluZ2lmeSh0aGlzLmxpYnJhcnlDb250ZW50U3RvcmFnZSB8fCB7fSkpDQogICAgICAgIGNvbnNvbGUubG9nKCfmlofmoYjlupPlhoXlrrnlt7Lkv53lrZjliLBsb2NhbFN0b3JhZ2UnKQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5paH5qGI5bqT5YaF5a655aSx6LSlOicsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDku45sb2NhbFN0b3JhZ2XliqDovb3mlofmoYjlupPlhoXlrrkNCiAgICBsb2FkTGlicmFyeUNvbnRlbnRGcm9tU3RvcmFnZSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdsaWJyYXJ5Q29udGVudFN0b3JhZ2UnKQ0KICAgICAgICBpZiAoc3RvcmVkKSB7DQogICAgICAgICAgdGhpcy5saWJyYXJ5Q29udGVudFN0b3JhZ2UgPSBKU09OLnBhcnNlKHN0b3JlZCkNCiAgICAgICAgICBjb25zb2xlLmxvZygn5LuObG9jYWxTdG9yYWdl5Yqg6L295paH5qGI5bqT5YaF5a65OicsIHRoaXMubGlicmFyeUNvbnRlbnRTdG9yYWdlKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMubGlicmFyeUNvbnRlbnRTdG9yYWdlID0ge30NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295paH5qGI5bqT5YaF5a655aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmxpYnJhcnlDb250ZW50U3RvcmFnZSA9IHt9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlofmoYjliLDmlofmoYjlupMNCiAgICBhZGRUb0xpYnJhcnkobGlicmFyeSkgew0KICAgICAgdGhpcy5jdXJyZW50TGlicmFyeSA9IGxpYnJhcnkNCiAgICAgIHRoaXMuYWRkQ29weXdyaXRpbmdGb3JtID0gew0KICAgICAgICB1c2VBSTogdHJ1ZSwNCiAgICAgICAgc2hvcERldGFpbHM6IGxpYnJhcnkuc2hvcERldGFpbHMgfHwgJycsDQogICAgICAgIHByb21wdDogbGlicmFyeS5wcm9tcHQgfHwgJycsDQogICAgICAgIGNvdW50OiA1LA0KICAgICAgICBjb250ZW50OiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5hZGRDb3B5d3JpdGluZ0RpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOaWh+ahiA0KICAgIGFkZENvcHl3cml0aW5nKCkgew0KICAgICAgdGhpcy4kcmVmcy5hZGRDb3B5d3JpdGluZ0Zvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuYWRkaW5nID0gdHJ1ZQ0KDQogICAgICAgICAgY29uc3QgY29udGVudERhdGEgPSB7DQogICAgICAgICAgICBsaWJyYXJ5SWQ6IHRoaXMuY3VycmVudExpYnJhcnkubGlicmFyeUlkLA0KICAgICAgICAgICAgdXNlQWk6IHRoaXMuYWRkQ29weXdyaXRpbmdGb3JtLnVzZUFJLA0KICAgICAgICAgICAgc2hvcERldGFpbHM6IHRoaXMuYWRkQ29weXdyaXRpbmdGb3JtLnNob3BEZXRhaWxzLA0KICAgICAgICAgICAgcHJvbXB0OiB0aGlzLmFkZENvcHl3cml0aW5nRm9ybS5wcm9tcHQsDQogICAgICAgICAgICBjb3VudDogdGhpcy5hZGRDb3B5d3JpdGluZ0Zvcm0uY291bnQsDQogICAgICAgICAgICBjb250ZW50OiB0aGlzLmFkZENvcHl3cml0aW5nRm9ybS5jb250ZW50DQogICAgICAgICAgfQ0KDQogICAgICAgICAgYWRkQ29udGVudChjb250ZW50RGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3ModGhpcy5hZGRDb3B5d3JpdGluZ0Zvcm0udXNlQUkgPw0KICAgICAgICAgICAgICBg5oiQ5Yqf55Sf5oiQJHt0aGlzLmFkZENvcHl3cml0aW5nRm9ybS5jb3VudH3mnaHmlofmoYhgIDogJ+aWh+ahiOa3u+WKoOaIkOWKnycpDQogICAgICAgICAgICB0aGlzLmFkZENvcHl3cml0aW5nRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLmxvYWRMaWJyYXJ5Q29udGVudHModGhpcy5jdXJyZW50TGlicmFyeS5saWJyYXJ5SWQpDQoNCiAgICAgICAgICAgIC8vIOabtOaWsOaWh+ahiOW6k+eahOeUn+aIkOiuoeaVsA0KICAgICAgICAgICAgdGhpcy5jdXJyZW50TGlicmFyeS5nZW5lcmF0ZWRDb3VudCArPSB0aGlzLmFkZENvcHl3cml0aW5nRm9ybS51c2VBSSA/DQogICAgICAgICAgICAgIHRoaXMuYWRkQ29weXdyaXRpbmdGb3JtLmNvdW50IDogMQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a3u+WKoOaWh+ahiOWksei0pScsIGVycm9yKQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5re75Yqg5paH5qGI5aSx6LSl77yaJyArIChlcnJvci5tc2cgfHwgZXJyb3IubWVzc2FnZSkpDQogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmFkZGluZyA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6YeN5paw55Sf5oiQ5paH5qGI5bqTDQogICAgcmVnZW5lcmF0ZUxpYnJhcnkobGlicmFyeSkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB6YeN5paw55Sf5oiQ6L+Z5Liq5paH5qGI5bqT5ZCX77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgcmVnZW5lcmF0ZUxpYnJhcnkobGlicmFyeS5saWJyYXJ5SWQpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGxpYnJhcnkuc3RhdHVzID0gJ2dlbmVyYXRpbmcnDQogICAgICAgICAgbGlicmFyeS5nZW5lcmF0ZWRDb3VudCA9IDANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W8gOWni+mHjeaWsOeUn+aIkOaWh+ahiOW6kycpDQogICAgICAgICAgdGhpcy5tb25pdG9yUHJvZ3Jlc3MobGlicmFyeS5saWJyYXJ5SWQpDQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfph43mlrDnlJ/miJDlpLHotKUnLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfph43mlrDnlJ/miJDlpLHotKXvvJonICsgKGVycm9yLm1zZyB8fCBlcnJvci5tZXNzYWdlKSkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWIoOmZpOaWh+ahiOW6kw0KICAgIGRlbGV0ZUxpYnJhcnkobGlicmFyeSkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq5paH5qGI5bqT5ZCX77yf5Yig6Zmk5ZCO5peg5rOV5oGi5aSN77yBJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgZGVsTGlicmFyeShbbGlicmFyeS5saWJyYXJ5SWRdKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgdGhpcy5sb2FkTGlicmFyeUxpc3QoKQ0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk5aSx6LSlJywgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSl77yaJyArIChlcnJvci5tc2cgfHwgZXJyb3IubWVzc2FnZSkpDQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5Y+W5raI5Yig6ZmkJykNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOafpeeci+aWh+ahiOWGheWuuQ0KICAgIHZpZXdDb250ZW50KGNvbnRlbnQpIHsNCiAgICAgIHRoaXMuJGFsZXJ0KGNvbnRlbnQuY29udGVudCwgJ+aWh+ahiOWGheWuuScsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICflhbPpl60nDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDlpI3liLbmlofmoYjlhoXlrrkNCiAgICBjb3B5Q29udGVudChjb250ZW50KSB7DQogICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dChjb250ZW50LmNvbnRlbnQpLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+ahiOW3suWkjeWItuWIsOWJqui0tOadvycpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WkjeWItuWksei0pe+8jOivt+aJi+WKqOWkjeWIticpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDnvJbovpHmlofmoYjlhoXlrrkNCiAgICBlZGl0Q29udGVudChjb250ZW50KSB7DQogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+e8lui+keaWh+ahiOWGheWuuScsICfnvJbovpHmlofmoYgnLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIGlucHV0VHlwZTogJ3RleHRhcmVhJywNCiAgICAgICAgaW5wdXRWYWx1ZTogY29udGVudC5jb250ZW50DQogICAgICB9KS50aGVuKCh7IHZhbHVlIH0pID0+IHsNCiAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgICBjb250ZW50SWQ6IGNvbnRlbnQuY29udGVudElkLA0KICAgICAgICAgIGNvbnRlbnQ6IHZhbHVlLA0KICAgICAgICAgIHdvcmRDb3VudDogdmFsdWUubGVuZ3RoDQogICAgICAgIH0NCg0KICAgICAgICB1cGRhdGVDb250ZW50KHVwZGF0ZURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGNvbnRlbnQuY29udGVudCA9IHZhbHVlDQogICAgICAgICAgY29udGVudC53b3JkQ291bnQgPSB2YWx1ZS5sZW5ndGgNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+e8lui+keaIkOWKnycpDQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfnvJbovpHlpLHotKUnLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvJbovpHlpLHotKXvvJonICsgKGVycm9yLm1zZyB8fCBlcnJvci5tZXNzYWdlKSkNCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojnvJbovpEnKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk5paH5qGI5YaF5a65DQogICAgZGVsZXRlQ29udGVudChjb250ZW50KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTov5nmnaHmlofmoYjlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBkZWxDb250ZW50KFtjb250ZW50LmNvbnRlbnRJZF0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmxvYWRMaWJyYXJ5Q29udGVudHModGhpcy5jdXJyZW50TGlicmFyeS5saWJyYXJ5SWQpDQogICAgICAgICAgdGhpcy5jdXJyZW50TGlicmFyeS5nZW5lcmF0ZWRDb3VudC0tDQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTlpLHotKUnLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKXvvJonICsgKGVycm9yLm1zZyB8fCBlcnJvci5tZXNzYWdlKSkNCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaQnKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5a+85Ye65paH5qGI5bqTDQogICAgZXhwb3J0TGlicmFyeShsaWJyYXJ5KSB7DQogICAgICBsZXQgY29udGVudCA9IGDmlofmoYjlupPvvJoke2xpYnJhcnkubmFtZX1cbmANCiAgICAgIGNvbnRlbnQgKz0gYOWIm+W7uuaXtumXtO+8miR7bGlicmFyeS5jcmVhdGVUaW1lfVxuYA0KICAgICAgY29udGVudCArPSBg5oC76K6h77yaJHt0aGlzLmxpYnJhcnlDb250ZW50cy5sZW5ndGh95p2h5paH5qGIXG5cbmANCg0KICAgICAgdGhpcy5saWJyYXJ5Q29udGVudHMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgY29udGVudCArPSBgJHtpbmRleCArIDF9LiAke2l0ZW0uY29udGVudH1cbmANCiAgICAgICAgY29udGVudCArPSBgICAg5Yib5bu65pe26Ze077yaJHtpdGVtLmNyZWF0ZVRpbWV9XG5cbmANCiAgICAgIH0pDQoNCiAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY29udGVudF0sIHsgdHlwZTogJ3RleHQvcGxhaW47Y2hhcnNldD11dGYtOCcgfSkNCiAgICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikNCiAgICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykNCiAgICAgIGEuaHJlZiA9IHVybA0KICAgICAgYS5kb3dubG9hZCA9IGAke2xpYnJhcnkubmFtZX0udHh0YA0KICAgICAgYS5jbGljaygpDQogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCkNCg0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofmoYjlupPlr7zlh7rmiJDlip8nKQ0KICAgIH0sDQogICAgLy8g6I635Y+W54q25oCB5ZCN56ewDQogICAgZ2V0U3RhdHVzTmFtZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgcGVuZGluZzogJ+acquW8gOWniycsDQogICAgICAgIGdlbmVyYXRpbmc6ICfnlJ/miJDkuK0nLA0KICAgICAgICBjb21wbGV0ZWQ6ICflt7LlrozmiJAnLA0KICAgICAgICBmYWlsZWQ6ICfnlJ/miJDlpLHotKUnDQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzDQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueKtuaAgeminOiJsg0KICAgIGdldFN0YXR1c0NvbG9yKHN0YXR1cykgew0KICAgICAgY29uc3QgY29sb3JNYXAgPSB7DQogICAgICAgIHBlbmRpbmc6ICdpbmZvJywNCiAgICAgICAgZ2VuZXJhdGluZzogJ3dhcm5pbmcnLA0KICAgICAgICBjb21wbGV0ZWQ6ICdzdWNjZXNzJywNCiAgICAgICAgZmFpbGVkOiAnZGFuZ2VyJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNvbG9yTWFwW3N0YXR1c10gfHwgJycNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["dou-backup.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dou-backup.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"shipin-container\">\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <h1 class=\"page-title\">\r\n          <i class=\"el-icon-magic-stick\"></i>\r\n          AI文案生成库\r\n        </h1>\r\n        <p class=\"page-description\">基于火山引擎Doubao，智能生成高质量文案内容</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- AI提示词推荐 -->\r\n    <div class=\"prompt-section\">\r\n      <h3>\r\n        <i class=\"el-icon-lightbulb\"></i>\r\n        AI提示词推荐\r\n      </h3>\r\n      <div class=\"prompt-grid\">\r\n        <div class=\"prompt-card\" @click=\"usePrompt(prompt)\" v-for=\"prompt in recommendPrompts\" :key=\"prompt.id\">\r\n          <div class=\"prompt-icon\">{{ prompt.icon }}</div>\r\n          <div class=\"prompt-title\">{{ prompt.title }}</div>\r\n          <div class=\"prompt-desc\">{{ prompt.desc }}</div>\r\n          <div class=\"prompt-preview\">{{ prompt.content.substring(0, 50) }}...</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文案库列表 -->\r\n    <div class=\"library-section\">\r\n      <div class=\"section-header\">\r\n        <h3>\r\n          <i class=\"el-icon-folder-opened\"></i>\r\n          我的文案库\r\n        </h3>\r\n        <div class=\"section-filters\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refreshData\"\r\n            style=\"margin-right: 12px;\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n          <el-select v-model=\"filterStatus\" placeholder=\"状态\" size=\"small\" style=\"width: 120px;\">\r\n            <el-option label=\"全部\" value=\"\"></el-option>\r\n            <el-option label=\"未开始\" value=\"pending\"></el-option>\r\n            <el-option label=\"生成中\" value=\"generating\"></el-option>\r\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\r\n            <el-option label=\"生成失败\" value=\"failed\"></el-option>\r\n          </el-select>\r\n          <el-input\r\n            v-model=\"searchKeyword\"\r\n            placeholder=\"搜索文案库...\"\r\n            size=\"small\"\r\n            clearable\r\n            style=\"width: 200px; margin-left: 12px;\"\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"library-list\">\r\n        <div v-for=\"library in filteredLibraryList\" :key=\"library.id\" class=\"library-item\">\r\n          <div class=\"item-header\">\r\n            <div class=\"item-title\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              {{ library.name }}\r\n            </div>\r\n            <div class=\"item-meta\">\r\n              <el-tag size=\"mini\" :type=\"getStatusColor(library.status)\">{{ getStatusName(library.status) }}</el-tag>\r\n              <el-tag size=\"mini\" type=\"info\" v-if=\"library.useAI\">AI生成</el-tag>\r\n              <span class=\"item-time\">{{ library.createTime }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-content\">\r\n            <div class=\"library-info\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">目标条数：</span>\r\n                <span class=\"value\">{{ library.targetCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">已生成：</span>\r\n                <span class=\"value\">{{ library.generatedCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">字数要求：</span>\r\n                <span class=\"value\">{{ library.wordCount }}字</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 生成进度 -->\r\n            <div class=\"progress-info\" v-if=\"library.status === 'generating'\">\r\n              <el-progress\r\n                :percentage=\"Math.round((library.generatedCount / library.targetCount) * 100)\"\r\n                status=\"success\"\r\n              ></el-progress>\r\n              <div class=\"progress-text\">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>\r\n            </div>\r\n\r\n            <!-- 店铺详情预览 -->\r\n            <div class=\"shop-info\" v-if=\"library.shopDetails\">\r\n              <span class=\"label\">店铺详情：</span>\r\n              <span class=\"preview\">{{ library.shopDetails.substring(0, 50) }}...</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-actions\">\r\n            <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewLibrary(library)\" :disabled=\"library.status === 'pending'\">\r\n              查看文案 ({{ library.generatedCount }})\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"success\" icon=\"el-icon-plus\" @click=\"addToLibrary(library)\" v-if=\"library.status === 'completed'\">\r\n              新增文案\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-refresh\" @click=\"regenerateLibrary(library)\" v-if=\"library.status === 'failed'\">\r\n              重新生成\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteLibrary(library)\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLibraryList.length === 0\" class=\"empty-state\">\r\n          <i class=\"el-icon-folder-add\"></i>\r\n          <h3>暂无文案库</h3>\r\n          <p>点击\"创建文案库\"按钮创建您的第一个AI文案库</p>\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 创建文案库对话框 -->\r\n    <el-dialog\r\n      title=\"创建AI文案库\"\r\n      :visible.sync=\"createLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"create-dialog\"\r\n    >\r\n      <el-form :model=\"createLibraryForm\" :rules=\"createLibraryRules\" ref=\"createLibraryForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库名称\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"createLibraryForm.name\"\r\n            placeholder=\"请输入文案库名称\"\r\n            maxlength=\"50\"\r\n            show-word-limit\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"createLibraryForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动创建\"\r\n          ></el-switch>\r\n          <div class=\"form-tip\">开启后将使用火山引擎Doubao生成文案</div>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"createLibraryForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.shopDetails\"\r\n              placeholder=\"请详细描述您的店铺信息、产品特色、目标客户等\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              maxlength=\"500\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">详细的店铺信息有助于AI生成更精准的文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.prompt\"\r\n              placeholder=\"请输入AI生成文案的提示词\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">\r\n              示例：生成吸引人的美食推广文案，要求语言生动、有食欲感\r\n              <el-button type=\"text\" @click=\"showPromptHelp\">查看提示词建议</el-button>\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"createLibraryForm.count\"\r\n              :min=\"1\"\r\n              :max=\"50\"\r\n              placeholder=\"请输入生成条数\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n            <div class=\"form-tip\">最多可生成50条文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"大约字数\" prop=\"wordCount\">\r\n            <el-select v-model=\"createLibraryForm.wordCount\" placeholder=\"请选择文案字数\">\r\n              <el-option label=\"50字以内\" value=\"50\"></el-option>\r\n              <el-option label=\"100字左右\" value=\"100\"></el-option>\r\n              <el-option label=\"200字左右\" value=\"200\"></el-option>\r\n              <el-option label=\"300字左右\" value=\"300\"></el-option>\r\n              <el-option label=\"500字左右\" value=\"500\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"createLibraryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createLibrary\" :loading=\"creating\">\r\n          {{ creating ? '创建中...' : '创建文案库' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新增文案对话框 -->\r\n    <el-dialog\r\n      title=\"新增文案\"\r\n      :visible.sync=\"addCopywritingDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"add-dialog\"\r\n    >\r\n      <el-form :model=\"addCopywritingForm\" :rules=\"addCopywritingRules\" ref=\"addCopywritingForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库\">\r\n          <el-input :value=\"currentLibrary ? currentLibrary.name : ''\" disabled></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"addCopywritingForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动输入\"\r\n          ></el-switch>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"addCopywritingForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.shopDetails\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"店铺详情（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.prompt\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"AI提示词（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"addCopywritingForm.count\"\r\n              :min=\"1\"\r\n              :max=\"20\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <template v-else>\r\n          <el-form-item label=\"文案内容\" prop=\"content\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.content\"\r\n              type=\"textarea\"\r\n              :rows=\"6\"\r\n              placeholder=\"请输入文案内容\"\r\n              maxlength=\"1000\"\r\n              show-word-limit\r\n            ></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"addCopywritingDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"addCopywriting\" :loading=\"adding\">\r\n          {{ adding ? (addCopywritingForm.useAI ? '生成中...' : '添加中...') : (addCopywritingForm.useAI ? '生成文案' : '添加文案') }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看文案库对话框 -->\r\n    <el-dialog\r\n      title=\"文案库详情\"\r\n      :visible.sync=\"viewLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '900px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"view-dialog\"\r\n    >\r\n      <div v-if=\"currentLibrary\" class=\"library-detail\">\r\n        <div class=\"detail-header\">\r\n          <h3>{{ currentLibrary.name }}</h3>\r\n          <div class=\"detail-meta\">\r\n            <el-tag :type=\"getStatusColor(currentLibrary.status)\">{{ getStatusName(currentLibrary.status) }}</el-tag>\r\n            <el-tag type=\"info\" v-if=\"currentLibrary.useAI\">AI生成</el-tag>\r\n            <span>创建时间：{{ currentLibrary.createTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"detail-info\">\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">目标条数：</span>\r\n              <span class=\"value\">{{ currentLibrary.targetCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">已生成：</span>\r\n              <span class=\"value\">{{ currentLibrary.generatedCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">字数要求：</span>\r\n              <span class=\"value\">{{ currentLibrary.wordCount }}字</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"shop-details\" v-if=\"currentLibrary.shopDetails\">\r\n            <h4>店铺详情：</h4>\r\n            <div class=\"details-text\">{{ currentLibrary.shopDetails }}</div>\r\n          </div>\r\n\r\n          <div class=\"prompt-info\" v-if=\"currentLibrary.prompt\">\r\n            <h4>AI提示词：</h4>\r\n            <div class=\"prompt-text\">{{ currentLibrary.prompt }}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"copywriting-list\">\r\n          <div class=\"list-header\">\r\n            <h4>文案列表 ({{ libraryContents.length }})</h4>\r\n            <div class=\"list-actions\">\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addToLibrary(currentLibrary)\">新增文案</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"loadLibraryContents(currentLibrary.id)\">刷新</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"content-list\">\r\n            <div v-for=\"(content, index) in libraryContents\" :key=\"content.id\" class=\"content-item\">\r\n              <div class=\"content-header\">\r\n                <span class=\"content-index\">{{ index + 1 }}</span>\r\n                <span class=\"content-time\">{{ content.createTime }}</span>\r\n                <div class=\"content-actions\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewContent(content)\">查看</el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-document-copy\" @click=\"copyContent(content)\">复制</el-button>\r\n                  <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-edit\" @click=\"editContent(content)\">编辑</el-button>\r\n                  <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteContent(content)\">删除</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"content-text\">{{ content.content }}</div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"libraryContents.length === 0\" class=\"empty-content\">\r\n              <i class=\"el-icon-document-add\"></i>\r\n              <p>暂无文案内容</p>\r\n              <el-button size=\"small\" type=\"primary\" @click=\"addToLibrary(currentLibrary)\">添加第一条文案</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"viewLibraryDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"success\" @click=\"exportLibrary(currentLibrary)\">导出文案库</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLibrary,\r\n  addLibrary,\r\n  delLibrary,\r\n  generateCopywriting,\r\n  listContent,\r\n  addContent,\r\n  updateContent,\r\n  delContent,\r\n  getProgress,\r\n  regenerateLibrary,\r\n  validateBaiduConfig,\r\n  getModelInfo\r\n} from '@/api/ai/copywriting'\r\n\r\n// 导入测试API作为备用\r\nimport {\r\n  listLibraryTest,\r\n  addLibraryTest,\r\n  testDeepSeekDirect,\r\n  healthCheck\r\n} from '@/api/ai/copywriting-test'\r\n\r\nexport default {\r\n  name: 'StorerShipin',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createLibraryDialogVisible: false,\r\n      addCopywritingDialogVisible: false,\r\n      viewLibraryDialogVisible: false,\r\n\r\n      // 加载状态\r\n      creating: false,\r\n      adding: false,\r\n\r\n      // 筛选和搜索\r\n      filterStatus: '',\r\n      searchKeyword: '',\r\n\r\n      // 当前数据\r\n      currentLibrary: null,\r\n      libraryContents: [],\r\n      isMobile: false,\r\n\r\n      // 创建文案库表单\r\n      createLibraryForm: {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '200' // AI剪辑文案默认200字\r\n      },\r\n      createLibraryRules: {\r\n        name: [\r\n          { required: true, message: '请输入文案库名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' },\r\n          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' },\r\n          { min: 5, max: 300, message: '长度在 5 到 300 个字符', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        wordCount: [\r\n          { required: true, message: '请选择文案字数', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 新增文案表单\r\n      addCopywritingForm: {\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 5,\r\n        content: ''\r\n      },\r\n      addCopywritingRules: {\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入文案内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // AI提示词推荐\r\n      recommendPrompts: [\r\n        {\r\n          id: 1,\r\n          icon: '🍔',\r\n          title: '美食推广',\r\n          desc: '适合餐饮店铺',\r\n          content: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围，能够激发顾客的购买欲望'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: '👗',\r\n          title: '服装时尚',\r\n          desc: '适合服装店铺',\r\n          content: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议，展现品牌调性和时尚态度'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: '💄',\r\n          title: '美妆护肤',\r\n          desc: '适合美妆店铺',\r\n          content: '编写专业的美妆护肤文案，强调产品功效、使用体验、适用肌肤类型，传递美丽自信的理念'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: '🏠',\r\n          title: '家居生活',\r\n          desc: '适合家居店铺',\r\n          content: '撰写温馨的家居生活文案，展现产品实用性、设计美感、生活品质提升，营造舒适家庭氛围'\r\n        },\r\n        {\r\n          id: 5,\r\n          icon: '📱',\r\n          title: '数码科技',\r\n          desc: '适合数码店铺',\r\n          content: '制作专业的数码产品文案，突出技术参数、功能特点、使用场景，体现科技感和实用价值'\r\n        },\r\n        {\r\n          id: 6,\r\n          icon: '🎓',\r\n          title: '教育培训',\r\n          desc: '适合教育机构',\r\n          content: '创建有说服力的教育培训文案，强调课程价值、师资力量、学习效果，激发学习兴趣和报名意愿'\r\n        }\r\n      ],\r\n\r\n      // 文案库列表\r\n      libraryList: [\r\n        {\r\n          id: 1,\r\n          name: '美食探店文案库',\r\n          useAI: true,\r\n          status: 'completed',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: '100',\r\n          shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',\r\n          prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围',\r\n          createTime: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '时尚服装推广库',\r\n          useAI: true,\r\n          status: 'generating',\r\n          targetCount: 30,\r\n          generatedCount: 15,\r\n          wordCount: '150',\r\n          shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',\r\n          prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',\r\n          createTime: '2024-01-15 10:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '手动创建文案库',\r\n          useAI: false,\r\n          status: 'completed',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: '200',\r\n          shopDetails: '',\r\n          prompt: '',\r\n          createTime: '2024-01-14 16:20:00'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredLibraryList() {\r\n      let list = this.libraryList\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        list = list.filter(item => item.status === this.filterStatus)\r\n      }\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        list = list.filter(item =>\r\n          item.name.toLowerCase().includes(keyword) ||\r\n          (item.shopDetails && item.shopDetails.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      return list\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化持久化存储\r\n    this.loadLibraryContentFromStorage()\r\n\r\n    this.loadLibraryList()\r\n\r\n    // 备用方案：如果3秒后还没有数据，直接加载模拟数据\r\n    setTimeout(() => {\r\n      if (this.libraryList.length === 0) {\r\n        console.log('3秒后仍无数据，强制加载模拟数据')\r\n        this.loadMockLibraryList()\r\n      }\r\n    }, 3000)\r\n  },\r\n  mounted() {\r\n    this.checkMobile()\r\n    window.addEventListener('resize', this.checkMobile)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkMobile)\r\n  },\r\n  methods: {\r\n    checkMobile() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n\r\n    loadLibraryList() {\r\n      listLibrary().then(response => {\r\n        this.libraryList = response.rows || response.data || []\r\n        if (this.libraryList.length === 0) {\r\n          // 如果返回空数据，也加载模拟数据\r\n          this.loadMockLibraryList()\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库列表失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式...')\r\n        }\r\n\r\n        // 使用模拟数据作为备用方案\r\n        this.loadMockLibraryList()\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案库数据\r\n    loadMockLibraryList() {\r\n      console.log('加载模拟文案库数据')\r\n\r\n      const mockLibraries = [\r\n        {\r\n          id: 1,\r\n          libraryId: 1,\r\n          name: '美食探店文案库',\r\n          libraryName: '美食探店文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '精选美食餐厅，提供各类特色菜品和优质服务',\r\n          prompt: '生成吸引人的美食探店文案，突出菜品特色和用餐体验',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: 150,\r\n          status: 'completed',\r\n          createTime: '2024-01-15 10:30:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 2,\r\n          libraryId: 2,\r\n          name: '时尚服装推广库',\r\n          libraryName: '时尚服装推广库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '时尚服装品牌，主营潮流服饰和配饰',\r\n          prompt: '生成时尚服装推广文案，强调款式新颖和品质优良',\r\n          targetCount: 15,\r\n          generatedCount: 15,\r\n          wordCount: 120,\r\n          status: 'completed',\r\n          createTime: '2024-01-10 14:20:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 3,\r\n          libraryId: 3,\r\n          name: '咖啡厅温馨文案库',\r\n          libraryName: '咖啡厅温馨文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段',\r\n          prompt: '生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: 100,\r\n          status: 'generating',\r\n          createTime: '2024-01-20 09:15:00',\r\n          createBy: 'user'\r\n        }\r\n      ]\r\n\r\n      // 添加用户创建的文案库（如果有的话）\r\n      const userLibraries = this.libraryList.filter(lib => lib.createBy === 'demo')\r\n\r\n      this.libraryList = [...mockLibraries, ...userLibraries]\r\n      this.$message.success('已加载模拟文案库数据（共' + this.libraryList.length + '个文案库）')\r\n    },\r\n    refreshData() {\r\n      console.log('手动刷新数据')\r\n      this.loadLibraryList()\r\n\r\n      // 如果1秒后还没有数据，直接加载模拟数据\r\n      setTimeout(() => {\r\n        if (this.libraryList.length === 0) {\r\n          console.log('刷新后仍无数据，加载模拟数据')\r\n          this.loadMockLibraryList()\r\n        } else {\r\n          this.$message.success('数据已刷新')\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 显示创建文案库对话框\r\n    showCreateLibraryDialog() {\r\n      this.createLibraryDialogVisible = true\r\n      this.createLibraryForm = {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '100'\r\n      }\r\n    },\r\n\r\n    // 使用推荐提示词\r\n    usePrompt(prompt) {\r\n      this.createLibraryForm.prompt = prompt.content\r\n      this.createLibraryDialogVisible = true\r\n      this.$message.success(`已应用${prompt.title}提示词`)\r\n    },\r\n\r\n    // 显示提示词帮助\r\n    showPromptHelp() {\r\n      this.$alert(`\r\n        <h4>AI剪辑文案提示词建议：</h4>\r\n        <p><strong>核心要求：</strong>适合口播，开头用疑问句吸引观众，语言顺口易读</p>\r\n        <br>\r\n        <h5>📝 推荐提示词模板：</h5>\r\n        <p><strong>1. 美食餐饮：</strong>生成适合口播的美食推广文案，开头用疑问句吸引观众，突出食材新鲜和口感层次，语言生动有食欲感，朋友推荐的语气</p>\r\n        <p><strong>2. 生活服务：</strong>生成温馨的生活服务推广文案，开头用疑问句引起共鸣，强调便民和贴心服务，语言亲切自然，像邻居朋友介绍</p>\r\n        <p><strong>3. 时尚美妆：</strong>生成时尚美妆种草文案，开头用疑问句抓住痛点，突出产品效果和使用体验，语言轻松活泼，姐妹分享的感觉</p>\r\n        <p><strong>4. 教育培训：</strong>生成教育培训推广文案，开头用疑问句引发思考，强调学习效果和成长价值，语言专业但不失亲和力</p>\r\n        <p><strong>5. 健康养生：</strong>生成健康养生科普文案，开头用疑问句引起关注，突出健康理念和实用方法，语言通俗易懂，专业可信</p>\r\n        <p><strong>6. 旅游出行：</strong>生成旅游景点推广文案，开头用疑问句激发向往，描述美景和独特体验，语言富有画面感和感染力</p>\r\n        <p><strong>7. 科技数码：</strong>生成数码产品介绍文案，开头用疑问句抓住需求，突出功能特点和使用便利，语言简洁明了，避免过于技术化</p>\r\n        <p><strong>8. 家居生活：</strong>生成家居用品推广文案，开头用疑问句触及生活痛点，强调实用性和生活品质提升，语言温馨贴近生活</p>\r\n        <br>\r\n        <h5>✍️ 编写技巧：</h5>\r\n        <p>• <strong>疑问开头：</strong>用\"你是否想要...\"、\"你知道吗...\"等疑问句开头</p>\r\n        <p>• <strong>顺口易读：</strong>避免拗口词汇，多用短句，适合朗读</p>\r\n        <p>• <strong>朋友语气：</strong>温和亲切，像朋友分享，营销感为0</p>\r\n        <p>• <strong>具体描述：</strong>结合您的店铺特色，越具体越好</p>\r\n      `, 'AI剪辑文案提示词指南', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '知道了',\r\n        customClass: 'prompt-help-dialog'\r\n      })\r\n    },\r\n    // 创建文案库\r\n    createLibrary() {\r\n      this.$refs.createLibraryForm.validate((valid) => {\r\n        if (valid) {\r\n          this.creating = true\r\n\r\n          const libraryData = {\r\n            libraryName: this.createLibraryForm.name,\r\n            useAi: this.createLibraryForm.useAI,\r\n            shopDetails: this.createLibraryForm.shopDetails,\r\n            prompt: this.createLibraryForm.prompt,\r\n            targetCount: this.createLibraryForm.useAI ? this.createLibraryForm.count : 0,\r\n            wordCount: parseInt(this.createLibraryForm.wordCount)\r\n          }\r\n\r\n          addLibrary(libraryData).then(response => {\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n            this.loadLibraryList()\r\n\r\n            // 如果使用AI生成，启动生成任务\r\n            if (this.createLibraryForm.useAI) {\r\n              this.startGeneration(response.data.libraryId)\r\n            }\r\n          }).catch(error => {\r\n            console.error('创建文案库失败，尝试使用测试API', error)\r\n\r\n            // 使用模拟创建方案\r\n            this.$message.warning('正在使用模拟方案创建文案库...')\r\n\r\n            // 模拟创建成功的响应\r\n            const mockLibrary = {\r\n              id: Date.now(),\r\n              libraryId: Date.now(),\r\n              name: libraryData.libraryName,\r\n              libraryName: libraryData.libraryName,\r\n              useAI: libraryData.useAi,\r\n              useAi: libraryData.useAi,\r\n              shopDetails: libraryData.shopDetails,\r\n              prompt: libraryData.prompt,\r\n              targetCount: libraryData.targetCount,\r\n              generatedCount: 0,\r\n              wordCount: libraryData.wordCount,\r\n              status: 'pending',\r\n              createTime: new Date().toLocaleString(),\r\n              createBy: 'demo'\r\n            }\r\n\r\n            // 将模拟数据添加到本地列表中\r\n            this.libraryList.unshift(mockLibrary)\r\n\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n\r\n            // 如果使用AI生成，启动真实的生成流程\r\n            if (this.createLibraryForm.useAI) {\r\n              this.$message.info('正在启动AI文案生成，请稍候...')\r\n\r\n              // 启动生成进度监控\r\n              this.startGenerationMonitoring(mockLibrary.libraryId)\r\n            }\r\n\r\n            this.creating = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 启动AI生成任务\r\n    startGeneration(libraryId) {\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n      if (library) {\r\n        generateCopywriting({\r\n          libraryId: libraryId,\r\n          shopDetails: library.shopDetails,\r\n          prompt: library.prompt,\r\n          count: library.targetCount,\r\n          wordCount: library.wordCount\r\n        }).then(() => {\r\n          this.$message.success('AI文案生成任务已启动')\r\n          this.monitorProgress(libraryId)\r\n        }).catch(error => {\r\n          console.error('启动生成任务失败', error)\r\n          this.$message.error('启动生成任务失败：' + (error.msg || error.message))\r\n        })\r\n      }\r\n    },\r\n\r\n    // 监控生成进度\r\n    monitorProgress(libraryId) {\r\n      const checkProgress = () => {\r\n        getProgress(libraryId).then(response => {\r\n          const progress = response.data\r\n          const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n          if (library) {\r\n            library.generatedCount = progress.generatedCount\r\n            library.status = progress.status\r\n\r\n            if (progress.status === 'generating') {\r\n              setTimeout(checkProgress, 2000) // 每2秒检查一次\r\n            } else if (progress.status === 'completed') {\r\n              this.$message.success(`${library.libraryName} 生成完成！`)\r\n            } else if (progress.status === 'failed') {\r\n              this.$message.error(`${library.libraryName} 生成失败`)\r\n            }\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取进度失败', error)\r\n        })\r\n      }\r\n      checkProgress()\r\n    },\r\n\r\n\r\n\r\n    // 查看文案库\r\n    viewLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.loadLibraryContents(library.id)\r\n      this.viewLibraryDialogVisible = true\r\n    },\r\n\r\n    // 加载文案库内容\r\n    loadLibraryContents(libraryId) {\r\n      // 首先尝试从持久化存储中加载\r\n      if (this.libraryContentStorage && this.libraryContentStorage[libraryId]) {\r\n        this.libraryContents = this.libraryContentStorage[libraryId]\r\n        this.$message.success(`已加载${this.libraryContents.length}条文案内容`)\r\n        console.log('从持久化存储加载文案内容:', this.libraryContents)\r\n        return\r\n      }\r\n\r\n      // 如果持久化存储中没有，尝试API\r\n      listContent(libraryId).then(response => {\r\n        this.libraryContents = response.rows || response.data || []\r\n        if (this.libraryContents.length === 0) {\r\n          // 如果没有内容，加载模拟内容\r\n          this.loadMockContents(libraryId)\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库内容失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式加载内容...')\r\n        }\r\n\r\n        // 加载模拟内容\r\n        this.loadMockContents(libraryId)\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案内容\r\n    loadMockContents(libraryId) {\r\n      console.log('加载模拟文案内容，libraryId:', libraryId)\r\n\r\n      const mockContents = {\r\n        1: [ // 美食探店文案库\r\n          {\r\n            id: 1,\r\n            contentId: 1,\r\n            libraryId: 1,\r\n            content: '🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高，强烈推荐给爱美食的朋友们！',\r\n            title: 'AI生成-美食探店文案1',\r\n            wordCount: 98,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-15 11:00:00'\r\n          },\r\n          {\r\n            id: 2,\r\n            contentId: 2,\r\n            libraryId: 1,\r\n            content: '🌟 又一家宝藏餐厅被我发现了！环境温馨雅致，服务贴心周到，最重要的是菜品真的太棒了！特色烤鱼鲜嫩多汁，秘制酱料层次丰富，每一口都是享受。和朋友聚餐的完美选择，记得提前预约哦！',\r\n            title: 'AI生成-美食探店文案2',\r\n            wordCount: 85,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-15 11:15:00'\r\n          }\r\n        ],\r\n        2: [ // 时尚服装推广库\r\n          {\r\n            id: 3,\r\n            contentId: 3,\r\n            libraryId: 2,\r\n            content: '✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭。现在购买还有限时优惠，不要错过哦！',\r\n            title: 'AI生成-时尚服装文案1',\r\n            wordCount: 92,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 90,\r\n            createTime: '2024-01-10 15:00:00'\r\n          }\r\n        ],\r\n        3: [ // 咖啡厅温馨文案库\r\n          {\r\n            id: 4,\r\n            contentId: 4,\r\n            libraryId: 3,\r\n            content: '☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间。快来体验我们的温馨服务吧！',\r\n            title: 'AI生成-咖啡厅文案1',\r\n            wordCount: 78,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-20 10:00:00'\r\n          },\r\n          {\r\n            id: 5,\r\n            contentId: 5,\r\n            libraryId: 3,\r\n            content: '🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱，每一刻都值得珍藏。来这里，让心灵得到片刻的宁静！',\r\n            title: 'AI生成-咖啡厅文案2',\r\n            wordCount: 71,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-20 10:30:00'\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.libraryContents = mockContents[libraryId] || []\r\n      this.$message.success(`已加载${this.libraryContents.length}条模拟文案内容`)\r\n    },\r\n\r\n    // 启动生成进度监控\r\n    startGenerationMonitoring(libraryId) {\r\n      console.log('启动生成进度监控，libraryId:', libraryId)\r\n\r\n      // 查找对应的文案库\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId || lib.id === libraryId)\r\n      if (!library) {\r\n        console.log('未找到文案库，停止监控')\r\n        return\r\n      }\r\n\r\n      // 初始化文案库的内容存储\r\n      if (!this.libraryContentStorage) {\r\n        this.libraryContentStorage = {}\r\n      }\r\n      if (!this.libraryContentStorage[libraryId]) {\r\n        this.libraryContentStorage[libraryId] = []\r\n      }\r\n\r\n      library.status = 'generating'\r\n\r\n      // 生成所有文案\r\n      const generateAllContent = () => {\r\n        for (let i = 1; i <= library.targetCount; i++) {\r\n          setTimeout(() => {\r\n            // 生成文案内容\r\n            const newContent = this.generateMockContent(library, i)\r\n\r\n            // 存储到持久化存储中\r\n            this.libraryContentStorage[libraryId].push(newContent)\r\n\r\n            // 更新生成数量\r\n            library.generatedCount = i\r\n\r\n            this.$message.success(`文案库\"${library.libraryName || library.name}\"已生成第${i}条文案`)\r\n\r\n            // 如果是最后一条，标记完成\r\n            if (i === library.targetCount) {\r\n              library.status = 'completed'\r\n              this.$message.success(`🎉 文案库\"${library.libraryName || library.name}\"生成完成！共生成${library.generatedCount}条文案`)\r\n\r\n              // 保存到localStorage\r\n              this.saveLibraryContentToStorage()\r\n            }\r\n          }, i * 2000) // 每2秒生成一条\r\n        }\r\n      }\r\n\r\n      // 开始生成\r\n      setTimeout(generateAllContent, 1000)\r\n    },\r\n\r\n    // 生成模拟文案内容（AI剪辑文案专用）\r\n    generateMockContent(library, index) {\r\n      const targetWordCount = library.wordCount || 200\r\n\r\n      // 固定使用AI剪辑文案（video）的生成策略\r\n      return this.generateVideoContent(library, index, targetWordCount)\r\n    },\r\n\r\n    // 根据平台生成专属文案\r\n    generatePlatformSpecificContent(platform, library, index, targetWordCount) {\r\n      switch (platform) {\r\n        case 'video': // AI剪辑文案（口播）\r\n          return this.generateVideoContent(library, index, targetWordCount)\r\n        case 'douyin': // 抖音/快手文案\r\n        case 'kuaishou':\r\n          return this.generateShortVideoContent(library, index, targetWordCount)\r\n        case 'xiaohongshu': // 小红书文案\r\n          return this.generateXiaohongshuContent(library, index, targetWordCount)\r\n        case 'review': // 点评/朋友圈文案\r\n        case 'moments':\r\n          return this.generateReviewContent(library, index, targetWordCount)\r\n        default: // 通用文案\r\n          return this.generateGeneralContent(library, index, targetWordCount)\r\n      }\r\n    },\r\n\r\n    // AI剪辑文案（口播专用）\r\n    generateVideoContent(library, index, targetWordCount) {\r\n      const questionStarters = [\r\n        '你是否想要',\r\n        '你有没有遇到过',\r\n        '你知道吗',\r\n        '你还在为',\r\n        '你想不想',\r\n        '你有没有发现',\r\n        '你是不是也',\r\n        '你有没有想过'\r\n      ]\r\n\r\n      const videoFragments = [\r\n        `${library.shopDetails || '我们'}专注于为您提供最优质的服务。`,\r\n        `这里不仅仅是一个地方，更是一种生活方式的体现。`,\r\n        `我们用心做好每一个细节，只为给您带来最好的体验。`,\r\n        `选择我们，就是选择品质和信赖。`,\r\n        `在这里，您会发现不一样的精彩。`\r\n      ]\r\n\r\n      const questionStart = questionStarters[Math.floor(Math.random() * questionStarters.length)]\r\n      let content = `${questionStart}${library.prompt || '体验不一样的服务'}？`\r\n\r\n      // 添加内容片段直到达到目标字数\r\n      while (content.length < targetWordCount - 30) {\r\n        const fragment = videoFragments[Math.floor(Math.random() * videoFragments.length)]\r\n        content += fragment\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '口播文案')\r\n    },\r\n\r\n    // 抖音/快手文案（简短有力）\r\n    generateShortVideoContent(library, index, targetWordCount) {\r\n      const hotTrends = ['yyds', '绝绝子', '太香了', '爱了爱了', '这谁顶得住', '直接拿下', '必须安排']\r\n      const shortFragments = [\r\n        `${library.shopDetails || '这家店'}真的${hotTrends[Math.floor(Math.random() * hotTrends.length)]}！`,\r\n        `姐妹们，这个必须冲！`,\r\n        `不是我吹，这个真的很棒！`,\r\n        `这个宝藏店铺终于被我发现了！`,\r\n        `朋友们，这波不亏！`\r\n      ]\r\n\r\n      let content = shortFragments[index % shortFragments.length]\r\n\r\n      // 保持简短，适合短视频\r\n      const maxLength = Math.min(targetWordCount, 80)\r\n      if (content.length < maxLength - 20) {\r\n        content += `${library.prompt || '真的值得一试'}，快去体验吧！`\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '短视频文案')\r\n    },\r\n\r\n    // 小红书文案（分段+emoji丰富）\r\n    generateXiaohongshuContent(library, index, targetWordCount) {\r\n      const emojis = ['✨', '💕', '🌟', '💖', '🎀', '🌸', '💫', '🦋', '🌺', '💐', '🎨', '🌈', '💎', '🎪', '🎭']\r\n      const xiaohongshuStarters = [\r\n        '姐妹们！今天要分享一个宝藏',\r\n        '真的不是我吹',\r\n        '这个真的太好了',\r\n        '终于找到了',\r\n        '姐妹们看过来'\r\n      ]\r\n\r\n      let content = `${xiaohongshuStarters[index % xiaohongshuStarters.length]}${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n      content += `${library.shopDetails || '这个地方'}真的让我惊喜${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n\r\n      // 添加分段内容\r\n      const segments = [\r\n        `环境超级棒${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `服务态度也很好${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `性价比真的很高${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `强烈推荐给大家${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n      ]\r\n\r\n      segments.forEach(segment => {\r\n        content += `${segment}\\n`\r\n      })\r\n\r\n      content += `\\n${library.prompt || '真的值得一试'}${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n\r\n      return this.createContentObject(library, index, content, '小红书文案')\r\n    },\r\n\r\n    // 点评/朋友圈文案（接地气+适当错别字）\r\n    generateReviewContent(library, index, targetWordCount) {\r\n      const casualWords = ['挺不错的', '还行', '蛮好的', '可以的', '不错不错']\r\n      const typos = {\r\n        '的': '滴',\r\n        '这个': '这个',\r\n        '真的': '真滴',\r\n        '好吃': '好次',\r\n        '喜欢': '稀饭'\r\n      }\r\n\r\n      let content = `今天和朋友去了${library.shopDetails || '这家店'}，${casualWords[index % casualWords.length]}。`\r\n      content += `环境还可以，服务态度也挺好滴。`\r\n      content += `${library.prompt || '总体来说还是值得推荐滴'}，下次还会再来。`\r\n\r\n      // 随机添加一些错别字\r\n      Object.keys(typos).forEach(key => {\r\n        if (Math.random() < 0.3) { // 30%概率替换\r\n          content = content.replace(key, typos[key])\r\n        }\r\n      })\r\n\r\n      return this.createContentObject(library, index, content, '点评文案')\r\n    },\r\n\r\n    // 通用文案生成\r\n    generateGeneralContent(library, index, targetWordCount) {\r\n      const baseFragments = [\r\n        `🌟 ${library.shopDetails || '我们的店铺'}，为您带来独特的体验！`,\r\n        `💫 发现美好，从这里开始！${library.shopDetails || '我们的店铺'}，期待您的光临！`,\r\n        `✨ 品质生活，精彩每一天！来体验我们为您精心准备的服务吧！`\r\n      ]\r\n\r\n      let content = baseFragments[index % baseFragments.length]\r\n\r\n      // 根据目标字数扩展内容\r\n      while (content.length < targetWordCount - 30) {\r\n        content += `我们专注于${library.prompt || '为您提供优质服务'}，用心做好每一个细节。`\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '通用文案')\r\n    },\r\n\r\n    // 创建文案内容对象\r\n    createContentObject(library, index, content, type) {\r\n      const newContent = {\r\n        id: Date.now() + index,\r\n        contentId: Date.now() + index,\r\n        libraryId: library.libraryId || library.id,\r\n        content: content,\r\n        title: `AI生成-${type}-第${index}条`,\r\n        wordCount: content.length,\r\n        isAiGenerated: true,\r\n        status: 'active',\r\n        qualityScore: 85 + Math.floor(Math.random() * 15),\r\n        createTime: new Date().toLocaleString()\r\n      }\r\n\r\n      console.log(`生成第${index}条${type} (实际${content.length}字):`, newContent)\r\n      return newContent\r\n    },\r\n\r\n    // 保存文案库内容到localStorage\r\n    saveLibraryContentToStorage() {\r\n      try {\r\n        localStorage.setItem('libraryContentStorage', JSON.stringify(this.libraryContentStorage || {}))\r\n        console.log('文案库内容已保存到localStorage')\r\n      } catch (error) {\r\n        console.error('保存文案库内容失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从localStorage加载文案库内容\r\n    loadLibraryContentFromStorage() {\r\n      try {\r\n        const stored = localStorage.getItem('libraryContentStorage')\r\n        if (stored) {\r\n          this.libraryContentStorage = JSON.parse(stored)\r\n          console.log('从localStorage加载文案库内容:', this.libraryContentStorage)\r\n        } else {\r\n          this.libraryContentStorage = {}\r\n        }\r\n      } catch (error) {\r\n        console.error('加载文案库内容失败:', error)\r\n        this.libraryContentStorage = {}\r\n      }\r\n    },\r\n    // 新增文案到文案库\r\n    addToLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.addCopywritingForm = {\r\n        useAI: true,\r\n        shopDetails: library.shopDetails || '',\r\n        prompt: library.prompt || '',\r\n        count: 5,\r\n        content: ''\r\n      }\r\n      this.addCopywritingDialogVisible = true\r\n    },\r\n\r\n    // 添加文案\r\n    addCopywriting() {\r\n      this.$refs.addCopywritingForm.validate((valid) => {\r\n        if (valid) {\r\n          this.adding = true\r\n\r\n          const contentData = {\r\n            libraryId: this.currentLibrary.libraryId,\r\n            useAi: this.addCopywritingForm.useAI,\r\n            shopDetails: this.addCopywritingForm.shopDetails,\r\n            prompt: this.addCopywritingForm.prompt,\r\n            count: this.addCopywritingForm.count,\r\n            content: this.addCopywritingForm.content\r\n          }\r\n\r\n          addContent(contentData).then(response => {\r\n            this.$message.success(this.addCopywritingForm.useAI ?\r\n              `成功生成${this.addCopywritingForm.count}条文案` : '文案添加成功')\r\n            this.addCopywritingDialogVisible = false\r\n            this.loadLibraryContents(this.currentLibrary.libraryId)\r\n\r\n            // 更新文案库的生成计数\r\n            this.currentLibrary.generatedCount += this.addCopywritingForm.useAI ?\r\n              this.addCopywritingForm.count : 1\r\n          }).catch(error => {\r\n            console.error('添加文案失败', error)\r\n            this.$message.error('添加文案失败：' + (error.msg || error.message))\r\n          }).finally(() => {\r\n            this.adding = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新生成文案库\r\n    regenerateLibrary(library) {\r\n      this.$confirm('确定要重新生成这个文案库吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        regenerateLibrary(library.libraryId).then(() => {\r\n          library.status = 'generating'\r\n          library.generatedCount = 0\r\n          this.$message.success('开始重新生成文案库')\r\n          this.monitorProgress(library.libraryId)\r\n        }).catch(error => {\r\n          console.error('重新生成失败', error)\r\n          this.$message.error('重新生成失败：' + (error.msg || error.message))\r\n        })\r\n      })\r\n    },\r\n\r\n    // 删除文案库\r\n    deleteLibrary(library) {\r\n      this.$confirm('确定要删除这个文案库吗？删除后无法恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delLibrary([library.libraryId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryList()\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 查看文案内容\r\n    viewContent(content) {\r\n      this.$alert(content.content, '文案内容', {\r\n        confirmButtonText: '关闭'\r\n      })\r\n    },\r\n\r\n    // 复制文案内容\r\n    copyContent(content) {\r\n      navigator.clipboard.writeText(content.content).then(() => {\r\n        this.$message.success('文案已复制到剪贴板')\r\n      }).catch(() => {\r\n        this.$message.error('复制失败，请手动复制')\r\n      })\r\n    },\r\n\r\n    // 编辑文案内容\r\n    editContent(content) {\r\n      this.$prompt('请编辑文案内容', '编辑文案', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputValue: content.content\r\n      }).then(({ value }) => {\r\n        const updateData = {\r\n          contentId: content.contentId,\r\n          content: value,\r\n          wordCount: value.length\r\n        }\r\n\r\n        updateContent(updateData).then(() => {\r\n          content.content = value\r\n          content.wordCount = value.length\r\n          this.$message.success('编辑成功')\r\n        }).catch(error => {\r\n          console.error('编辑失败', error)\r\n          this.$message.error('编辑失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消编辑')\r\n      })\r\n    },\r\n\r\n    // 删除文案内容\r\n    deleteContent(content) {\r\n      this.$confirm('确定要删除这条文案吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delContent([content.contentId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryContents(this.currentLibrary.libraryId)\r\n          this.currentLibrary.generatedCount--\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 导出文案库\r\n    exportLibrary(library) {\r\n      let content = `文案库：${library.name}\\n`\r\n      content += `创建时间：${library.createTime}\\n`\r\n      content += `总计：${this.libraryContents.length}条文案\\n\\n`\r\n\r\n      this.libraryContents.forEach((item, index) => {\r\n        content += `${index + 1}. ${item.content}\\n`\r\n        content += `   创建时间：${item.createTime}\\n\\n`\r\n      })\r\n\r\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `${library.name}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n\r\n      this.$message.success('文案库导出成功')\r\n    },\r\n    // 获取状态名称\r\n    getStatusName(status) {\r\n      const statusMap = {\r\n        pending: '未开始',\r\n        generating: '生成中',\r\n        completed: '已完成',\r\n        failed: '生成失败'\r\n      }\r\n      return statusMap[status] || status\r\n    },\r\n\r\n    // 获取状态颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        pending: 'info',\r\n        generating: 'warning',\r\n        completed: 'success',\r\n        failed: 'danger'\r\n      }\r\n      return colorMap[status] || ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.shipin-container {\r\n  padding: 24px;\r\n  background: #f5f5f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .header-content {\r\n    .page-title {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 8px 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .page-description {\r\n      color: #7f8c8d;\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.prompt-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #e6a23c;\r\n    }\r\n  }\r\n\r\n  .prompt-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 16px;\r\n\r\n    .prompt-card {\r\n      padding: 20px;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .prompt-icon {\r\n        font-size: 32px;\r\n        margin-bottom: 12px;\r\n        text-align: center;\r\n      }\r\n\r\n      .prompt-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .prompt-desc {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .prompt-preview {\r\n        font-size: 12px;\r\n        color: #95a5a6;\r\n        line-height: 1.4;\r\n        background: #f8f9fa;\r\n        padding: 8px;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.template-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #409eff;\r\n    }\r\n  }\r\n\r\n  .template-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 16px;\r\n\r\n    .template-card {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .template-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .template-type {\r\n          background: #f0f0f0;\r\n          color: #666;\r\n          padding: 4px 12px;\r\n          border-radius: 16px;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .template-hot {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .template-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .template-preview {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        line-height: 1.5;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .template-stats {\r\n        display: flex;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .section-filters {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .library-list {\r\n    .library-item {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin-bottom: 16px;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .item-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .item-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          i {\r\n            margin-right: 8px;\r\n            color: #409eff;\r\n          }\r\n        }\r\n\r\n        .item-meta {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 12px;\r\n\r\n          .item-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-content {\r\n        margin-bottom: 16px;\r\n\r\n        .library-info {\r\n          display: flex;\r\n          gap: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .info-item {\r\n            .label {\r\n              font-size: 12px;\r\n              color: #7f8c8d;\r\n            }\r\n\r\n            .value {\r\n              font-size: 14px;\r\n              color: #2c3e50;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n\r\n        .progress-info {\r\n          margin-bottom: 12px;\r\n\r\n          .progress-text {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n            margin-top: 8px;\r\n          }\r\n        }\r\n\r\n        .shop-info {\r\n          font-size: 14px;\r\n          color: #7f8c8d;\r\n\r\n          .label {\r\n            font-weight: 600;\r\n          }\r\n\r\n          .preview {\r\n            color: #95a5a6;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n        flex-wrap: wrap;\r\n      }\r\n    }\r\n\r\n    .empty-state {\r\n      text-align: center;\r\n      padding: 60px 20px;\r\n\r\n      i {\r\n        font-size: 64px;\r\n        color: #ddd;\r\n        margin-bottom: 16px;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 18px;\r\n        color: #7f8c8d;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      p {\r\n        color: #95a5a6;\r\n        margin: 0 0 20px 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-detail {\r\n  .detail-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 12px 0;\r\n    }\r\n\r\n    .detail-meta {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      font-size: 14px;\r\n      color: #7f8c8d;\r\n    }\r\n  }\r\n\r\n  .detail-info {\r\n    margin-bottom: 24px;\r\n\r\n    .info-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\r\n      gap: 16px;\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        .label {\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n          display: block;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #2c3e50;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n\r\n    .shop-details,\r\n    .prompt-info {\r\n      margin-bottom: 16px;\r\n\r\n      h4 {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      .details-text,\r\n      .prompt-text {\r\n        line-height: 1.6;\r\n        color: #2c3e50;\r\n        background: #f8f9fa;\r\n        padding: 12px;\r\n        border-radius: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .copywriting-list {\r\n    .list-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      padding-bottom: 12px;\r\n      border-bottom: 1px solid #e9ecef;\r\n\r\n      h4 {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0;\r\n      }\r\n\r\n      .list-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .content-list {\r\n      max-height: 400px;\r\n      overflow-y: auto;\r\n\r\n      .content-item {\r\n        border: 1px solid #e9ecef;\r\n        border-radius: 6px;\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #409eff;\r\n          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);\r\n        }\r\n\r\n        .content-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .content-index {\r\n            background: #409eff;\r\n            color: #fff;\r\n            padding: 2px 8px;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            min-width: 30px;\r\n            text-align: center;\r\n          }\r\n\r\n          .content-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n\r\n          .content-actions {\r\n            display: flex;\r\n            gap: 4px;\r\n          }\r\n        }\r\n\r\n        .content-text {\r\n          line-height: 1.6;\r\n          color: #2c3e50;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n\r\n      .empty-content {\r\n        text-align: center;\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          color: #ddd;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        p {\r\n          color: #7f8c8d;\r\n          margin: 0 0 16px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n  line-height: 1.4;\r\n}\r\n\r\n// 移动端优化样式\r\n@media (max-width: 768px) {\r\n  .shipin-container {\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    .header-content {\r\n      width: 100%;\r\n      margin-bottom: 12px;\r\n\r\n      .page-title {\r\n        font-size: 20px;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .page-description {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      width: 100%;\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .el-button {\r\n        flex: 1;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .prompt-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .prompt-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .prompt-card {\r\n        padding: 16px;\r\n\r\n        .prompt-icon {\r\n          font-size: 24px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-title {\r\n          font-size: 14px;\r\n          margin-bottom: 6px;\r\n        }\r\n\r\n        .prompt-desc {\r\n          font-size: 12px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-preview {\r\n          font-size: 11px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .template-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .template-grid {\r\n      grid-template-columns: 1fr;\r\n      gap: 12px;\r\n\r\n      .template-card {\r\n        padding: 16px;\r\n\r\n        .template-title {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .template-preview {\r\n          font-size: 13px;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 2;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-section {\r\n    padding: 16px;\r\n\r\n    .section-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 16px;\r\n\r\n      h3 {\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .section-filters {\r\n        width: 100%;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n\r\n        .el-select {\r\n          width: 100% !important;\r\n        }\r\n\r\n        .el-input {\r\n          width: 100% !important;\r\n          margin-left: 0 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .library-list {\r\n      .library-item {\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n\r\n        .item-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          margin-bottom: 12px;\r\n\r\n          .item-title {\r\n            font-size: 15px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .item-meta {\r\n            width: 100%;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .item-time {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-content {\r\n          .library-info {\r\n            flex-direction: column;\r\n            gap: 8px;\r\n\r\n            .info-item {\r\n              display: flex;\r\n              justify-content: space-between;\r\n\r\n              .label {\r\n                font-size: 12px;\r\n              }\r\n\r\n              .value {\r\n                font-size: 13px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .shop-info {\r\n            font-size: 13px;\r\n\r\n            .preview {\r\n              display: block;\r\n              margin-top: 4px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-actions {\r\n          gap: 6px;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n            font-size: 12px;\r\n            padding: 6px 8px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .empty-state {\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n        }\r\n\r\n        h3 {\r\n          font-size: 16px;\r\n        }\r\n\r\n        p {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 对话框移动端优化\r\n  .create-dialog,\r\n  .view-dialog {\r\n    .el-dialog__body {\r\n      padding: 16px;\r\n      max-height: calc(100vh - 120px);\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .el-form {\r\n      .el-form-item {\r\n        margin-bottom: 16px;\r\n\r\n        .el-form-item__label {\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-textarea {\r\n          font-size: 14px;\r\n        }\r\n\r\n        .el-checkbox-group {\r\n          .el-checkbox {\r\n            margin-bottom: 8px;\r\n            margin-right: 16px;\r\n\r\n            .el-checkbox__label {\r\n              font-size: 14px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-dialog__footer {\r\n      padding: 12px 16px;\r\n\r\n      .el-button {\r\n        margin-left: 8px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-header {\r\n      h3 {\r\n        font-size: 18px;\r\n      }\r\n\r\n      .detail-meta {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .detail-info {\r\n      .info-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 12px;\r\n\r\n        .info-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 8px 12px;\r\n          background: #f8f9fa;\r\n          border-radius: 4px;\r\n\r\n          .label {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .value {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .shop-details,\r\n      .prompt-info {\r\n        .details-text,\r\n        .prompt-text {\r\n          font-size: 13px;\r\n          padding: 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .list-header {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n\r\n        h4 {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .list-actions {\r\n          width: 100%;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n\r\n      .content-list {\r\n        max-height: 300px;\r\n\r\n        .content-item {\r\n          padding: 12px;\r\n\r\n          .content-header {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n\r\n            .content-actions {\r\n              width: 100%;\r\n              justify-content: space-between;\r\n\r\n              .el-button {\r\n                flex: 1;\r\n                margin: 0 2px;\r\n                font-size: 11px;\r\n                padding: 4px 6px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .content-text {\r\n            font-size: 13px;\r\n            line-height: 1.5;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 超小屏幕优化 (小于480px)\r\n@media (max-width: 480px) {\r\n  .shipin-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .prompt-section {\r\n    .prompt-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-info {\r\n      .info-grid {\r\n        .info-item {\r\n          padding: 6px 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .content-list {\r\n        .content-item {\r\n          padding: 10px;\r\n\r\n          .content-header {\r\n            .content-actions {\r\n              .el-button {\r\n                font-size: 10px;\r\n                padding: 3px 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 提示词帮助对话框样式\r\n::v-deep .prompt-help-dialog {\r\n  .el-message-box {\r\n    width: 600px;\r\n    max-width: 90vw;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  h4, h5 {\r\n    color: #409EFF;\r\n    margin: 15px 0 10px 0;\r\n  }\r\n\r\n  p {\r\n    margin: 8px 0;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  strong {\r\n    color: #303133;\r\n  }\r\n}\r\n</style>"]}]}