{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue?vue&type=template&id=320cffa6&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue", "mtime": 1754628577485}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}