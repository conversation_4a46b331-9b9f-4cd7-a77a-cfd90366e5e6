package com.ruoyi.web.controller.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 火山引擎Doubao直接调用服务 (使用OkHttp，兼容Java 8)
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DoubaoDirectService
{
    private static final Logger log = LoggerFactory.getLogger(DoubaoDirectService.class);

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final OkHttpClient httpClient = new OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build();

    /**
     * 生成文案
     */
    public String generateCopywriting(String prompt, String shopDetails, int maxTokens) {
        log.info("=== 火山引擎Doubao直接服务开始生成文案 ===");
        log.info("提示词: {}", prompt);
        log.info("店铺详情: {}", shopDetails);
        log.info("最大令牌数: {}", maxTokens);
        
        try {
            // 构建完整提示词
            String fullPrompt = buildFullPrompt(prompt, shopDetails);
            log.info("完整提示词: {}", fullPrompt);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "doubao-seed-1-6-flash-250715");
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("stream", false);
            
            // 构建消息
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", fullPrompt);
            requestBody.put("messages", new Object[]{message});
            
            // 转换为JSON
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            log.info("请求体: {}", jsonBody);
            
            // API配置
            String apiUrl = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
            String apiKeyValue = "5ad57720-913c-410e-b75f-debd2fe836a4";
            
            log.info("请求URL: {}", apiUrl);
            log.info("API Key前缀: {}", apiKeyValue.substring(0, Math.min(20, apiKeyValue.length())) + "...");
            
            // 创建请求体
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));
            
            // 创建请求
            Request request = new Request.Builder()
                .url(apiUrl)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKeyValue)
                .addHeader("User-Agent", "Java/1.8")
                .post(body)
                .build();
            
            log.info("发送HTTP请求...");
            
            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                log.info("收到响应，状态码: {}", response.code());
                
                String responseBody = response.body().string();
                log.info("响应内容: {}", responseBody);

                if (response.isSuccessful()) {
                    // 解析响应
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);
                    
                    if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                        jsonResponse.get("choices").size() > 0) {
                        
                        JsonNode firstChoice = jsonResponse.get("choices").get(0);
                        if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                            String generatedContent = firstChoice.get("message").get("content").asText();
                            log.info("=== 文案生成成功 ===");
                            log.info("生成的文案: {}", generatedContent);
                            return generatedContent.trim();
                        }
                    }
                    
                    log.warn("响应格式不正确，无法提取生成的内容");
                    return "抱歉，无法生成文案，请稍后重试。";
                } else {
                    log.error("API调用失败，状态码: {}, 响应: {}", response.code(), responseBody);
                    throw new RuntimeException("文案生成失败: " + response.code() + " " + responseBody);
                }
            }

        } catch (IOException e) {
            log.error("=== 火山引擎Doubao网络请求失败 ===", e);
            throw new RuntimeException("网络请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("=== 火山引擎Doubao文案生成失败 ===", e);
            throw new RuntimeException("文案生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建完整的提示词 - 专门为足疗SPA行业优化
     */
    private String buildFullPrompt(String prompt, String shopDetails) {
        if (shopDetails == null || shopDetails.trim().isEmpty()) {
            return prompt;
        }

        // 分析店铺类型
        String shopType = analyzeShopType(shopDetails);

        if ("spa".equals(shopType)) {
            // 足疗SPA专用prompt - 整合用户自定义提示词
            StringBuilder sb = new StringBuilder();
            sb.append("你是专业的文案创作专家，为足疗SPA店铺生成营销文案。");
            sb.append("店铺信息：").append(shopDetails.trim().replace("\n", " ").replace("\r", " "));

            // 整合用户的自定义提示词
            if (prompt != null && !prompt.trim().isEmpty()) {
                sb.append("。特别要求：").append(prompt.trim().replace("\n", " ").replace("\r", " "));
            }

            sb.append("。请根据店铺信息和特别要求生成符合要求的营销文案，语言自然生动，突出特色服务。");
            return sb.toString();
        } else {
            // 通用文案生成 - 修复提示词直接输出问题
            StringBuilder sb = new StringBuilder();
            sb.append("你是专业的文案创作专家。");
            sb.append("店铺信息：").append(shopDetails.trim().replace("\n", " "));
            sb.append("。创作要求：根据店铺信息生成营销文案，要生动有趣，突出特色。");

            // 用户提示词作为创作指导，不直接输出
            if (prompt != null && !prompt.trim().isEmpty()) {
                sb.append("特别指导：").append(prompt.trim().replace("\n", " "));
            }

            sb.append("。请直接输出文案内容（不要包含指导内容本身）。");
            return sb.toString();
        }
    }

    /**
     * 分析店铺类型
     */
    private String analyzeShopType(String shopDetails) {
        if (shopDetails == null) return "general";

        String text = shopDetails.toLowerCase();

        if (text.contains("足道") || text.contains("spa") || text.contains("按摩") ||
            text.contains("理疗") || text.contains("技师") || text.contains("艾灸")) {
            return "spa";
        } else if (text.contains("餐厅") || text.contains("菜") || text.contains("食")) {
            return "restaurant";
        } else if (text.contains("美发") || text.contains("理发") || text.contains("造型")) {
            return "beauty";
        }

        return "general";
    }

    /**
     * 批量生成文案 - 确保每条都不同
     */
    public String[] batchGenerateCopywriting(String prompt, String shopDetails, int count, int maxTokens) {
        log.info("=== 火山引擎Doubao批量生成文案开始 ===");
        log.info("数量: {}", count);

        String[] results = new String[count];
        String shopType = analyzeShopType(shopDetails);

        for (int i = 0; i < count; i++) {
            try {
                // 为每条文案创建独特的prompt
                String uniquePrompt = buildUniquePrompt(prompt, shopDetails, shopType, i + 1, count);

                log.info("第{}条文案生成，prompt长度: {}", i + 1, uniquePrompt.length());

                results[i] = generateWithUniquePrompt(uniquePrompt, maxTokens);

                // 避免请求过于频繁
                if (i < count - 1) {
                    Thread.sleep(1500);
                }
            } catch (Exception e) {
                log.error("批量生成第{}条文案失败", i + 1, e);
                results[i] = "生成失败: " + e.getMessage();
            }
        }

        return results;
    }

    /**
     * 构建独特的prompt
     */
    private String buildUniquePrompt(String originalPrompt, String shopDetails, String shopType, int index, int total) {
        if ("spa".equals(shopType)) {
            return buildUniqueSpaPrompt(originalPrompt, shopDetails, index, total);
        } else {
            return String.format(
                "你是专业文案专家，生成第%d条文案（共%d条）。店铺：%s。要求：%s。必须与其他文案完全不同。",
                index, total, shopDetails.trim().replace("\n", " "), originalPrompt.trim()
            );
        }
    }

    /**
     * 为足疗SPA构建独特的prompt
     */
    private String buildUniqueSpaPrompt(String originalPrompt, String shopDetails, int index, int total) {
        String[] scenarios = {
            "和朋友聚餐后顺路", "加班疲劳后放松", "周末和闺蜜一起", "同事推荐来试",
            "路过看到进来", "朋友生日一起", "工作压力大来放松", "听说不错专门来"
        };

        String[] focuses = {
            "技师手法", "环境氛围", "小食茶点", "性价比", "放松效果", "设施服务", "专业程度", "整体体验"
        };

        String scenario = scenarios[(index - 1) % scenarios.length];
        String focus = focuses[(index - 1) % focuses.length];

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("你是专业文案专家，为足疗SPA生成第%d条营销文案（共%d条）。", index, total));
        sb.append("店铺信息：").append(shopDetails.trim().replace("\n", " ").replace("\r", " "));

        // 整合用户的自定义提示词
        if (originalPrompt != null && !originalPrompt.trim().isEmpty()) {
            sb.append("。特别要求：").append(originalPrompt.trim().replace("\n", " ").replace("\r", " "));
        }

        sb.append(String.format("。场景：%s。重点：%s。", scenario, focus));
        sb.append("要求：每条文案都必须完全不同，语言生动自然。请生成符合要求的营销文案。");

        return sb.toString();
    }

    /**
     * 使用独特prompt生成文案
     */
    private String generateWithUniquePrompt(String uniquePrompt, int maxTokens) throws Exception {
        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "doubao-seed-1-6-flash-250715");
        requestBody.put("max_tokens", maxTokens);
        requestBody.put("stream", false);

        // 构建消息
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", uniquePrompt);
        requestBody.put("messages", new Object[]{message});

        // 转换为JSON
        String jsonBody = objectMapper.writeValueAsString(requestBody);

        // API配置
        String apiUrl = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
        String apiKeyValue = "5ad57720-913c-410e-b75f-debd2fe836a4";

        // 创建请求体
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));

        // 创建请求
        Request request = new Request.Builder()
            .url(apiUrl)
            .addHeader("Content-Type", "application/json")
            .addHeader("Authorization", "Bearer " + apiKeyValue)
            .addHeader("User-Agent", "Java/1.8")
            .post(body)
            .build();

        // 发送请求
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body().string();

            if (response.isSuccessful()) {
                // 解析响应
                JsonNode jsonResponse = objectMapper.readTree(responseBody);

                if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                    jsonResponse.get("choices").size() > 0) {

                    JsonNode firstChoice = jsonResponse.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        String generatedContent = firstChoice.get("message").get("content").asText();
                        return generatedContent.trim();
                    }
                }

                throw new RuntimeException("响应格式不正确");
            } else {
                throw new RuntimeException("API调用失败: " + response.code());
            }
        }
    }
}
