<template>
  <div class="icon-display" :class="['icon-display', iconType]">
    <!-- Emoji 图标 -->
    <span v-if="isEmoji" class="emoji-icon" :style="iconStyles">
      {{ icon }}
    </span>
    
    <!-- 图片图标 -->
    <img v-else-if="isImage" :src="icon" :alt="alt" class="image-icon" :style="iconStyles">
    
    <!-- 字体图标 -->
    <i v-else-if="isIconFont" :class="icon" class="font-icon" :style="iconStyles"></i>
    
    <!-- 默认图标 -->
    <span v-else class="default-icon" :style="iconStyles">
      {{ icon || '📷' }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'IconDisplay',
  props: {
    icon: {
      type: String,
      default: ''
    },
    size: {
      type: [String, Number],
      default: '20px'
    },
    color: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    borderRadius: {
      type: String,
      default: '4px'
    },
    alt: {
      type: String,
      default: '图标'
    }
  },
  computed: {
    isEmoji() {
      if (!this.icon) return false
      // 检测是否为emoji
      return /[\u{1f300}-\u{1f5ff}\u{1f900}-\u{1f9ff}\u{1f600}-\u{1f64f}\u{1f680}-\u{1f6ff}\u{2600}-\u{26ff}\u{2700}-\u{27bf}]/u.test(this.icon)
    },
    
    isImage() {
      if (!this.icon) return false
      // 检测是否为图片URL或webpack处理后的路径
      return this.icon.startsWith('http') || 
             this.icon.startsWith('data:image') || 
             this.icon.startsWith('blob:') || 
             this.icon.startsWith('/static/') || 
             this.icon.includes('.png') || 
             this.icon.includes('.jpg') || 
             this.icon.includes('.jpeg') || 
             this.icon.includes('.gif') || 
             this.icon.includes('.svg') ||
             this.icon.includes('.webp') ||
             // webpack处理后的路径通常包含这些特征
             /^[a-f0-9]+\.(png|jpg|jpeg|gif|svg|webp)$/i.test(this.icon.split('/').pop())
    },
    
    isIconFont() {
      if (!this.icon) return false
      // 检测是否为字体图标类名
      return this.icon.startsWith('el-icon-') || this.icon.startsWith('fa-') || this.icon.startsWith('iconfont')
    },
    
    iconType() {
      if (this.isEmoji) return 'emoji'
      if (this.isImage) return 'image'
      if (this.isIconFont) return 'font'
      return 'default'
    },
    
    iconStyles() {
      const styles = {}
      
      if (this.size) {
        if (this.isImage || this.isIconFont) {
          styles.width = typeof this.size === 'number' ? `${this.size}px` : this.size
          styles.height = typeof this.size === 'number' ? `${this.size}px` : this.size
        } else {
          styles.fontSize = typeof this.size === 'number' ? `${this.size}px` : this.size
        }
      }
      
      if (this.color && !this.isImage) {
        styles.color = this.color
      }
      
      if (this.backgroundColor) {
        styles.backgroundColor = this.backgroundColor
        styles.padding = '4px'
        styles.borderRadius = this.borderRadius
      }
      
      return styles
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-display {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &.emoji {
    .emoji-icon {
      line-height: 1;
    }
  }
  
  &.image {
    .image-icon {
      object-fit: cover;
      border-radius: inherit;
    }
  }
  
  &.font {
    .font-icon {
      line-height: 1;
    }
  }
  
  &.default {
    .default-icon {
      line-height: 1;
    }
  }
}
</style>
