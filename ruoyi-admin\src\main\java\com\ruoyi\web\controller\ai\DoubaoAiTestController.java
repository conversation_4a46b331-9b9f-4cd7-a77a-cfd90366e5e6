package com.ruoyi.web.controller.ai;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.service.IAiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaIgnore;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 火山引擎Doubao AI测试Controller
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/test")
@SaIgnore  // 忽略认证，允许匿名访问
public class DoubaoAiTestController extends BaseController
{
    private final IAiService aiService;

    @Autowired
    private DoubaoDirectService doubaoDirectService;

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public R<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "ok");
        data.put("timestamp", System.currentTimeMillis());
        data.put("service", "火山引擎Doubao AI测试服务");
        return R.ok("服务正常", data);
    }

    /**
     * 验证百度AI配置
     */
    @GetMapping("/validate-config")
    public R<String> validateConfig()
    {
        try {
            boolean isValid = aiService.validateApiConfig();
            if (isValid) {
                return R.ok("火山引擎Doubao配置验证成功");
            } else {
                return R.fail("火山引擎Doubao配置验证失败，请检查API Key配置");
            }
        } catch (Exception e) {
            return R.fail("配置验证异常：" + e.getMessage());
        }
    }

    /**
     * 获取模型信息
     */
    @GetMapping("/model-info")
    public R<Map<String, Object>> getModelInfo()
    {
        try {
            return R.ok(aiService.getModelInfo());
        } catch (Exception e) {
            return R.fail("获取模型信息失败：" + e.getMessage());
        }
    }

    /**
     * 测试文案生成
     */
    @PostMapping("/generate")
    public R<Map<String, Object>> testGenerate(@RequestBody Map<String, String> request)
    {
        try {
            String prompt = request.getOrDefault("prompt", "生成一条吸引人的美食推广文案");
            String shopDetails = request.getOrDefault("shopDetails", "测试店铺：一家温馨的咖啡厅，主营手工咖啡和精致甜点");

            String result = aiService.generateCopywriting(prompt, shopDetails, 200);

            Map<String, Object> data = new HashMap<>();
            data.put("prompt", prompt);
            data.put("shopDetails", shopDetails);
            data.put("generatedContent", result);
            data.put("wordCount", result.length());

            return R.ok("测试生成成功", data);
        } catch (Exception e) {
            return R.fail("测试生成失败：" + e.getMessage());
        }
    }

    /**
     * 批量测试生成
     */
    @PostMapping("/batch-generate")
    public R<Map<String, Object>> testBatchGenerate(@RequestBody Map<String, Object> request)
    {
        try {
            String prompt = (String) request.getOrDefault("prompt", "生成一条吸引人的美食推广文案");
            String shopDetails = (String) request.getOrDefault("shopDetails", "测试店铺：一家温馨的咖啡厅");
            Integer count = (Integer) request.getOrDefault("count", 3);

            if (count > 10) {
                return R.fail("测试批量生成最多支持10条");
            }

            String[] results = aiService.batchGenerateCopywriting(prompt, shopDetails, count, 200);

            Map<String, Object> data = new HashMap<>();
            data.put("prompt", prompt);
            data.put("shopDetails", shopDetails);
            data.put("count", count);
            data.put("results", results);

            return R.ok("批量测试生成成功", data);
        } catch (Exception e) {
            return R.fail("批量测试生成失败：" + e.getMessage());
        }
    }

    /**
     * 获取访问令牌（仅用于测试）
     */
    @GetMapping("/access-token")
    public R<Map<String, Object>> getAccessToken()
    {
        try {
            String token = aiService.getAccessToken();
            if (token != null && !token.isEmpty()) {
                Map<String, Object> data = new HashMap<>();
                data.put("token", token.substring(0, Math.min(10, token.length())) + "...");
                data.put("length", token.length());
                return R.ok("获取访问令牌成功", data);
            } else {
                return R.fail("获取访问令牌失败");
            }
        } catch (Exception e) {
            return R.fail("获取访问令牌异常：" + e.getMessage());
        }
    }

    /**
     * 测试火山引擎Doubao直接调用
     */
    @GetMapping("/test-doubao")
    public R<Map<String, Object>> testDoubao()
    {
        try {
            log.info("开始测试火山引擎Doubao直接调用...");

            // 直接调用生成文案方法
            String result = aiService.generateCopywriting(
                "请生成一个简单的测试文案",
                "测试商户",
                100
            );

            Map<String, Object> data = new HashMap<>();
            data.put("result", result);
            data.put("length", result.length());
            data.put("timestamp", System.currentTimeMillis());

            log.info("火山引擎Doubao调用成功，结果长度: {}", result.length());
            return R.ok("火山引擎Doubao调用成功", data);

        } catch (Exception e) {
            log.error("火山引擎Doubao调用失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            return R.fail("火山引擎Doubao调用失败: " + e.getMessage(), errorData);
        }
    }

    /**
     * 测试新的火山引擎Doubao直接服务
     */
    @GetMapping("/test-doubao-direct")
    public R<Map<String, Object>> testDoubaoDirectService()
    {
        try {
            log.info("开始测试新的火山引擎Doubao直接服务...");

            // 使用新的直接服务调用生成文案方法
            String result = doubaoDirectService.generateCopywriting(
                "请生成一个简单的测试文案",
                "测试商户：一家温馨的咖啡厅，主营手工咖啡和精致甜点",
                100
            );

            Map<String, Object> data = new HashMap<>();
            data.put("result", result);
            data.put("length", result.length());
            data.put("timestamp", System.currentTimeMillis());
            data.put("service", "DoubaoDirectService");

            log.info("新火山引擎Doubao直接服务调用成功，结果长度: {}", result.length());
            return R.ok("新火山引擎Doubao直接服务调用成功", data);

        } catch (Exception e) {
            log.error("新火山引擎Doubao直接服务调用失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            errorData.put("service", "DoubaoDirectService");
            return R.fail("新火山引擎Doubao直接服务调用失败: " + e.getMessage(), errorData);
        }
    }

    /**
     * 测试水悦湾足疗SPA文案生成
     */
    @GetMapping("/test-shuiyuewan")
    public R<Map<String, Object>> testShuiYueWan()
    {
        try {
            log.info("开始测试水悦湾足疗SPA文案生成...");

            String shopDetails = "水悦湾足疗SPA：壹品休闲足道70分钟，金牌技师，太极理疗90分钟，艾灸红外，养元SPA70分钟，肩背排毒，至尊足道80分钟，性价比高";

            String prompt = "水悦湾正规绿色足疗SPA，熬夜党速来！温泉养肝道歉礼🌿";

            // 使用新的直接服务调用生成文案方法
            String result = doubaoDirectService.generateCopywriting(
                prompt,
                shopDetails,
                150
            );

            Map<String, Object> data = new HashMap<>();
            data.put("result", result);
            data.put("length", result.length());
            data.put("timestamp", System.currentTimeMillis());
            data.put("service", "DoubaoDirectService");
            data.put("shopType", "水悦湾足疗SPA");

            log.info("水悦湾足疗SPA文案生成成功，结果长度: {}", result.length());
            return R.ok("水悦湾足疗SPA文案生成成功", data);

        } catch (Exception e) {
            log.error("水悦湾足疗SPA文案生成失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            errorData.put("service", "DoubaoDirectService");
            errorData.put("shopType", "水悦湾足疗SPA");
            return R.fail("水悦湾足疗SPA文案生成失败: " + e.getMessage(), errorData);
        }
    }

    /**
     * 测试水悦湾足疗SPA文案生成 - 完整版
     */
    @GetMapping("/test-shuiyuewan-full")
    public R<Map<String, Object>> testShuiYueWanFull()
    {
        try {
            log.info("开始测试水悦湾足疗SPA完整文案生成...");

            String shopDetails = "王牌项目（含特色）：壹品・休闲足道（70min、金牌技师、肩背下肢经络、小吃果盘）、壹品・太极理疗（90min、芳疗技师、艾灸红外、养生茶、睡眠 / 腰腹调理）、悦境・养元 SPA（70min、资深 SPA 技师、肩背排毒、定制甜点、商务回血）、云尚・至尊足道（80min、金牌技师、手足反射区、性价比） 专业与细节优势：技师分级（金牌 / 芳疗 / SPA 师）、标配：小吃、果盘、茶水、高端精油、专属惊喜（养生茶、定制甜点）、精准匹配需求";

            String prompt = "我们是水悦湾，除了上面介绍的，其余都模糊介绍,不要发散思维说一些不一些不存在的东西,每段文案一定要强调正规绿色！熬夜党速来！这口盈江的温泉，是给肝的道歉礼🌿";

            // 使用新的直接服务调用生成文案方法
            String result = doubaoDirectService.generateCopywriting(
                prompt,
                shopDetails,
                200
            );

            Map<String, Object> data = new HashMap<>();
            data.put("result", result);
            data.put("length", result.length());
            data.put("timestamp", System.currentTimeMillis());
            data.put("service", "DoubaoDirectService");
            data.put("shopType", "水悦湾足疗SPA完整版");

            log.info("水悦湾足疗SPA完整文案生成成功，结果长度: {}", result.length());
            return R.ok("水悦湾足疗SPA完整文案生成成功", data);

        } catch (Exception e) {
            log.error("水悦湾足疗SPA完整文案生成失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            errorData.put("service", "DoubaoDirectService");
            errorData.put("shopType", "水悦湾足疗SPA完整版");
            return R.fail("水悦湾足疗SPA完整文案生成失败: " + e.getMessage(), errorData);
        }
    }



    /**
     * 测试原始HTTP调用
     */
    @GetMapping("/test-raw-http")
    public R<Map<String, Object>> testRawHttp()
    {
        try {
            log.info("开始测试原始HTTP调用...");

            // 使用Java原生HTTP客户端测试
            String url = "https://qianfan.baidubce.com/v2/chat/completions";
            String apiKey = "bce-v3/ALTAK-CtTuLtajO5LKkRGbYDeGT/a693564c5d707af25bc7e85463a690b67733fbda";

            String jsonBody = "{\n" +
                "    \"model\": \"deepseek-v3\",\n" +
                "    \"messages\": [\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
                "            \"content\": \"你好，请简单介绍一下自己\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"stream\": false\n" +
                "}";

            OkHttpClient client = new OkHttpClient();

            okhttp3.RequestBody body = okhttp3.RequestBody.create(jsonBody, MediaType.parse("application/json"));
            Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .post(body)
                .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();

                Map<String, Object> data = new HashMap<>();
                data.put("statusCode", response.code());
                data.put("response", responseBody);
                data.put("headers", response.headers().toMultimap());
                data.put("timestamp", System.currentTimeMillis());

                log.info("原始HTTP调用完成，状态码: {}", response.code());
                return R.ok("原始HTTP调用完成", data);
            }

        } catch (Exception e) {
            log.error("原始HTTP调用失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            return R.fail("原始HTTP调用失败: " + e.getMessage(), errorData);
        }
    }

    /**
     * 直接修复BaiduAiService的调用
     */
    @GetMapping("/fix-service")
    public R<Map<String, Object>> fixService()
    {
        try {
            log.info("开始修复BaiduAiService调用...");

            // 直接在这里实现正确的DeepSeek-V3调用逻辑
            String url = "https://qianfan.baidubce.com/v2/chat/completions";
            String apiKey = "bce-v3/ALTAK-CtTuLtajO5LKkRGbYDeGT/a693564c5d707af25bc7e85463a690b67733fbda";

            // 构建请求体
            String prompt = "作为一个专业的营销文案专家，请为商户'测试商户'生成一个吸引人的营销文案。要求：简洁有力，突出商户特色，字数控制在100字以内。";

            String jsonBody = "{\n" +
                "    \"model\": \"deepseek-v3\",\n" +
                "    \"messages\": [\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
                "            \"content\": \"" + prompt + "\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"stream\": false\n" +
                "}";

            OkHttpClient client = new OkHttpClient();

            okhttp3.RequestBody body = okhttp3.RequestBody.create(jsonBody, MediaType.parse("application/json"));
            Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .post(body)
                .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();

                if (response.isSuccessful()) {
                    // 解析响应
                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    com.fasterxml.jackson.databind.JsonNode jsonResponse = objectMapper.readTree(responseBody);

                String generatedContent = "";
                if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                    jsonResponse.get("choices").size() > 0) {

                    com.fasterxml.jackson.databind.JsonNode firstChoice = jsonResponse.get("choices").get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        generatedContent = firstChoice.get("message").get("content").asText();
                    }
                }

                    Map<String, Object> data = new HashMap<>();
                    data.put("statusCode", response.code());
                    data.put("generatedContent", generatedContent);
                    data.put("contentLength", generatedContent.length());
                    data.put("timestamp", System.currentTimeMillis());

                    log.info("BaiduAiService修复调用成功，生成内容长度: {}", generatedContent.length());
                    return R.ok("BaiduAiService修复调用成功", data);
                } else {
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("statusCode", response.code());
                    errorData.put("response", responseBody);
                    errorData.put("timestamp", System.currentTimeMillis());
                    return R.fail("BaiduAiService修复调用失败，状态码: " + response.code(), errorData);
                }
            }

        } catch (Exception e) {
            log.error("BaiduAiService修复调用失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            return R.fail("BaiduAiService修复调用失败: " + e.getMessage(), errorData);
        }
    }

    /**
     * 测试创建文案库功能
     */
    @GetMapping("/test-create-library")
    public R<Map<String, Object>> testCreateLibrary()
    {
        try {
            log.info("开始测试创建文案库功能...");

            // 创建测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("libraryName", "测试文案库_" + System.currentTimeMillis());
            testData.put("useAi", false);
            testData.put("shopDetails", "测试商户");
            testData.put("prompt", "测试提示词");
            testData.put("targetCount", 0);
            testData.put("wordCount", 100);
            testData.put("timestamp", System.currentTimeMillis());

            log.info("测试数据: {}", testData);

            return R.ok("创建文案库测试接口正常工作", testData);

        } catch (Exception e) {
            log.error("测试创建文案库功能失败", e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 模拟创建文案库（GET方式，立即可用）
     */
    @GetMapping("/create-library-demo")
    public R<Map<String, Object>> createLibraryDemo(
            @RequestParam(defaultValue = "演示文案库") String name,
            @RequestParam(defaultValue = "演示商户：一家温馨的咖啡厅") String shopDetails,
            @RequestParam(defaultValue = "生成吸引人的美食推广文案") String prompt)
    {
        try {
            log.info("开始演示创建文案库: {}", name);

            // 模拟创建成功的响应
            Map<String, Object> libraryData = new HashMap<>();
            libraryData.put("libraryId", System.currentTimeMillis());
            libraryData.put("libraryName", name + "_" + System.currentTimeMillis());
            libraryData.put("useAi", true);
            libraryData.put("shopDetails", shopDetails);
            libraryData.put("prompt", prompt);
            libraryData.put("targetCount", 5);
            libraryData.put("generatedCount", 0);
            libraryData.put("wordCount", 100);
            libraryData.put("status", "pending");
            libraryData.put("createTime", new Date());
            libraryData.put("createBy", "demo");

            log.info("演示文案库创建成功: {}", libraryData);

            return R.ok("演示文案库创建成功！这是一个演示接口，实际创建需要重启后端服务。", libraryData);

        } catch (Exception e) {
            log.error("演示创建文案库失败", e);
            return R.fail("演示失败: " + e.getMessage());
        }
    }
}
