<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多平台AI文案生成演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .content {
            padding: 30px;
        }
        
        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .platform-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .platform-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .platform-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .platform-icon {
            font-size: 2em;
        }
        
        .platform-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .platform-subtitle {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .features {
            margin: 15px 0;
        }
        
        .features h4 {
            color: #495057;
            margin: 0 0 10px 0;
            font-size: 1em;
        }
        
        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .features li {
            margin: 5px 0;
        }
        
        .sample-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
            font-size: 0.9em;
            line-height: 1.6;
        }
        
        .sample-content.video {
            border-left-color: #28a745;
        }
        
        .sample-content.douyin {
            border-left-color: #dc3545;
        }
        
        .sample-content.xiaohongshu {
            border-left-color: #fd7e14;
        }
        
        .sample-content.review {
            border-left-color: #6f42c1;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: background 0.2s;
            margin: 10px 10px 10px 0;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .highlight-box h3 {
            color: #0056b3;
            margin-top: 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .comparison-table tr:last-child td {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .platform-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 多平台AI文案生成</h1>
            <p>基于各平台特点，生成专属风格的营销文案</p>
        </div>
        
        <div class="content">
            <div class="highlight-box">
                <h3>🚀 核心特色</h3>
                <p><strong>平台定制：</strong>根据抖音、小红书、朋友圈等不同平台特点，生成专属风格文案</p>
                <p><strong>智能适配：</strong>自动调整字数、语气、格式，确保文案符合平台调性</p>
                <p><strong>营销感0：</strong>温和语气，朋友分享感，避免被平台识别为营销内容</p>
                <p><strong>用户优先：</strong>如果用户提供了自定义提示词，优先使用用户的要求</p>
            </div>
            
            <div class="platform-grid">
                <!-- AI剪辑文案 -->
                <div class="platform-card">
                    <div class="platform-header">
                        <div class="platform-icon">🎬</div>
                        <div>
                            <div class="platform-title">AI剪辑文案</div>
                            <div class="platform-subtitle">口播专用，顺口易读</div>
                        </div>
                    </div>
                    
                    <div class="features">
                        <h4>特点：</h4>
                        <ul>
                            <li>开头多用疑问句吸引观众</li>
                            <li>语言顺口，适合口播</li>
                            <li>话术不固定，易于朗读</li>
                            <li>温和语气，朋友分享感</li>
                        </ul>
                    </div>
                    
                    <div class="sample-content video">
                        <strong>示例文案：</strong><br>
                        你是否想要找一家真正用心的咖啡厅？这家温馨的小店不仅有手工调制的咖啡，更有让人感动的服务态度。每一杯咖啡都承载着店主的用心，每一块甜点都饱含着温情...
                    </div>
                </div>
                
                <!-- 抖音/快手文案 -->
                <div class="platform-card">
                    <div class="platform-header">
                        <div class="platform-icon">📱</div>
                        <div>
                            <div class="platform-title">抖音/快手文案</div>
                            <div class="platform-subtitle">简短有力，热门梗</div>
                        </div>
                    </div>
                    
                    <div class="features">
                        <h4>特点：</h4>
                        <ul>
                            <li>简短有力，字数可控</li>
                            <li>融入热门梗和网络用语</li>
                            <li>营销感为0，朋友推荐语气</li>
                            <li>适合短视频配文</li>
                        </ul>
                    </div>
                    
                    <div class="sample-content douyin">
                        <strong>示例文案：</strong><br>
                        这家咖啡厅真的yyds！姐妹们，这个必须冲！不是我吹，这个真的很棒！朋友们，这波不亏！
                    </div>
                </div>
                
                <!-- 小红书文案 -->
                <div class="platform-card">
                    <div class="platform-header">
                        <div class="platform-icon">📖</div>
                        <div>
                            <div class="platform-title">小红书文案</div>
                            <div class="platform-subtitle">分段清晰，emoji丰富</div>
                        </div>
                    </div>
                    
                    <div class="features">
                        <h4>特点：</h4>
                        <ul>
                            <li>分段清晰，易于阅读</li>
                            <li>emoji表情丰富多样</li>
                            <li>种草语气，姐妹分享感</li>
                            <li>内容详细，图文并茂</li>
                        </ul>
                    </div>
                    
                    <div class="sample-content xiaohongshu">
                        <strong>示例文案：</strong><br>
                        姐妹们！今天要分享一个宝藏✨<br><br>
                        这个咖啡厅真的让我惊喜💕<br><br>
                        环境超级棒🌟<br>
                        服务态度也很好💖<br>
                        性价比真的很高🎀<br>
                        强烈推荐给大家🌸
                    </div>
                </div>
                
                <!-- 点评/朋友圈文案 -->
                <div class="platform-card">
                    <div class="platform-header">
                        <div class="platform-icon">💬</div>
                        <div>
                            <div class="platform-title">点评/朋友圈文案</div>
                            <div class="platform-subtitle">接地气，不被识别</div>
                        </div>
                    </div>
                    
                    <div class="features">
                        <h4>特点：</h4>
                        <ul>
                            <li>通俗接地气，不会太专业</li>
                            <li>适当出现同音错别字</li>
                            <li>不被平台识别为刷评论</li>
                            <li>真实用户体验感</li>
                        </ul>
                    </div>
                    
                    <div class="sample-content review">
                        <strong>示例文案：</strong><br>
                        今天和朋友去了这家咖啡厅，挺不错滴。环境还可以，服务态度也挺好滴。总体来说还是值得推荐滴，下次还会再来。
                    </div>
                </div>
            </div>
            
            <table class="comparison-table">
                <tr>
                    <th>平台</th>
                    <th>字数建议</th>
                    <th>语言风格</th>
                    <th>核心特点</th>
                </tr>
                <tr>
                    <td>AI剪辑文案</td>
                    <td>200字左右</td>
                    <td>疑问句开头，顺口易读</td>
                    <td>适合口播，话术灵活</td>
                </tr>
                <tr>
                    <td>抖音/快手</td>
                    <td>50字以内</td>
                    <td>网络用语，热门梗</td>
                    <td>简短有力，朋友推荐</td>
                </tr>
                <tr>
                    <td>小红书</td>
                    <td>150字左右</td>
                    <td>分段+emoji，种草语气</td>
                    <td>姐妹分享，详细丰富</td>
                </tr>
                <tr>
                    <td>点评/朋友圈</td>
                    <td>100字左右</td>
                    <td>接地气，适当错别字</td>
                    <td>真实体验，不被识别</td>
                </tr>
            </table>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="http://localhost:8080/storer/shipin" class="btn success">
                    🚀 立即体验多平台文案生成
                </a>
            </div>
            
            <div class="highlight-box">
                <h3>💡 使用建议</h3>
                <p><strong>1. 选择合适平台：</strong>根据您的推广渠道选择对应的文案类型</p>
                <p><strong>2. 详细描述店铺：</strong>提供准确的店铺信息，AI会生成更精准的文案</p>
                <p><strong>3. 自定义提示词：</strong>如有特殊要求，可以自定义提示词，系统会优先使用</p>
                <p><strong>4. 适当调整：</strong>生成后可以根据实际需要进行微调</p>
            </div>
        </div>
    </div>
</body>
</html>
