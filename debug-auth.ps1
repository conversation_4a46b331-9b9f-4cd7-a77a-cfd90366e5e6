Write-Host "=== Doubao API Authentication Debug ===" -ForegroundColor Cyan

# Test different API key formats
$apiKeys = @(
    "5ad57720-913c-410e-b75f-debd2fe836a4z",
    "5ad57720-913c-410e-b75f-debd2fe836a4"
)

$headers_base = @{
    'Content-Type' = 'application/json'
}

$body = '{
    "model": "doubao-seed-1-6-flash-250715",
    "messages": [
        {
            "role": "user",
            "content": "Hello"
        }
    ],
    "max_tokens": 10
}'

foreach ($apiKey in $apiKeys) {
    Write-Host "`nTesting API Key: $($apiKey.Substring(0,10))..." -ForegroundColor Yellow
    
    $headers = $headers_base.Clone()
    $headers['Authorization'] = "Bearer $apiKey"
    
    try {
        $response = Invoke-RestMethod -Uri "https://ark.cn-beijing.volces.com/api/v3/chat/completions" -Method POST -Headers $headers -Body $body -TimeoutSec 10
        Write-Host "SUCCESS with key: $($apiKey.Substring(0,10))..." -ForegroundColor Green
        break
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "FAILED with status: $statusCode" -ForegroundColor Red
        
        if ($statusCode -eq 401) {
            Write-Host "  -> Authentication failed" -ForegroundColor Red
        } elseif ($statusCode -eq 403) {
            Write-Host "  -> Access forbidden" -ForegroundColor Red
        } elseif ($statusCode -eq 400) {
            Write-Host "  -> Bad request" -ForegroundColor Red
        } else {
            Write-Host "  -> Other error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Cyan
Write-Host "`nPossible issues:" -ForegroundColor Yellow
Write-Host "1. API key format incorrect" -ForegroundColor White
Write-Host "2. API key expired or invalid" -ForegroundColor White
Write-Host "3. Model name incorrect" -ForegroundColor White
Write-Host "4. Account not activated or no credits" -ForegroundColor White
