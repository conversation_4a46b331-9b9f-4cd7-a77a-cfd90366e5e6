<!-- 产品信息设置 - 这是专业版DIY编辑器的第二部分 -->
<template>
  <!-- 产品信息设置 -->
  <el-tab-pane label="🛍️ 产品展示" name="products">
    <div class="setting-section">
      <div class="section-header">
        <h4>产品展示设置</h4>
        <div class="header-controls">
          <el-switch v-model="config.products.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">添加产品</el-button>
        </div>
      </div>
      
      <div v-if="config.products.enabled">
        <!-- 全局产品设置 -->
        <el-collapse v-model="productsActiveNames">
          <el-collapse-item title="⚙️ 全局设置" name="global">
            <el-form label-width="120px" size="small">
              <el-form-item label="显示模式">
                <el-radio-group v-model="config.products.displayMode">
                  <el-radio label="carousel">轮播模式</el-radio>
                  <el-radio label="grid">网格模式</el-radio>
                  <el-radio label="list">列表模式</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item v-if="config.products.displayMode === 'carousel'" label="自动轮播">
                <el-switch v-model="config.products.autoPlay"></el-switch>
              </el-form-item>
              
              <el-form-item v-if="config.products.autoPlay" label="轮播间隔">
                <el-input-number v-model="config.products.interval" :min="2" :max="10" controls-position="right"></el-input-number>
                <span class="unit">秒</span>
              </el-form-item>
              
              <el-form-item v-if="config.products.displayMode === 'grid'" label="每行显示">
                <el-input-number v-model="config.products.columns" :min="1" :max="3" controls-position="right"></el-input-number>
                <span class="unit">个</span>
              </el-form-item>
              
              <el-form-item label="背景设置">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-color-picker v-model="config.products.backgroundColor" show-alpha></el-color-picker>
                  </el-col>
                  <el-col :span="12">
                    <el-input-number v-model="config.products.borderRadius" :min="0" :max="20" controls-position="right"></el-input-number>
                    <span class="unit">px</span>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          
          <!-- 单个产品设置 -->
          <el-collapse-item 
            v-for="(product, index) in config.products.items" 
            :key="'product-' + index"
            :title="`🛍️ 产品 ${index + 1}: ${product.name}`" 
            :name="'product-' + index"
          >
            <div class="product-editor">
              <div class="product-header">
                <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeProduct(index)" circle></el-button>
              </div>
              
              <el-form label-width="100px" size="small">
                <!-- 基本信息 -->
                <div class="form-group">
                  <h5>📝 基本信息</h5>
                  <el-form-item label="产品图片">
                    <div class="image-upload-section">
                      <el-upload
                        class="product-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="(file) => handleProductImageUpload(file, index)"
                        accept="image/*"
                      >
                        <img v-if="product.image" :src="product.image" class="product-preview">
                        <i v-else class="el-icon-plus product-uploader-icon"></i>
                      </el-upload>
                      <div class="upload-tips">
                        <p>📐 建议尺寸：300x300px (正方形)</p>
                        <p>📁 支持格式：JPG、PNG、WebP</p>
                        <p>📦 文件大小：≤1MB</p>
                      </div>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="产品名称">
                    <el-input v-model="product.name" placeholder="请输入产品名称" maxlength="30" show-word-limit></el-input>
                  </el-form-item>
                  
                  <el-form-item label="产品描述">
                    <el-input v-model="product.description" type="textarea" :rows="3" placeholder="请输入产品描述" maxlength="100" show-word-limit></el-input>
                  </el-form-item>
                  
                  <el-form-item label="产品配置">
                    <el-input v-model="product.specifications" type="textarea" :rows="2" placeholder="如：容量500ml，保质期12个月" maxlength="80" show-word-limit></el-input>
                  </el-form-item>
                </div>
                
                <!-- 价格设置 -->
                <div class="form-group">
                  <h5>💰 价格设置</h5>
                  <el-form-item label="原价">
                    <el-input v-model="product.originalPrice" placeholder="￥99.00">
                      <template slot="prepend">￥</template>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="现价">
                    <el-input v-model="product.currentPrice" placeholder="￥79.00">
                      <template slot="prepend">￥</template>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="折扣信息">
                    <el-input v-model="product.discount" placeholder="如：8折优惠，限时特价" maxlength="20" show-word-limit></el-input>
                  </el-form-item>
                  
                  <el-form-item label="团购链接">
                    <el-input v-model="product.buyUrl" placeholder="https://..." type="url"></el-input>
                  </el-form-item>
                </div>
                
                <!-- 标签设置 -->
                <div class="form-group">
                  <h5>🏷️ 产品标签</h5>
                  <el-form-item label="爆款图标">
                    <el-switch v-model="product.isHot"></el-switch>
                    <span class="form-tip">显示"🔥爆款"标签</span>
                  </el-form-item>
                  
                  <el-form-item label="热卖图标">
                    <el-switch v-model="product.isBestSeller"></el-switch>
                    <span class="form-tip">显示"⭐热卖"标签</span>
                  </el-form-item>
                  
                  <el-form-item label="新品图标">
                    <el-switch v-model="product.isNew"></el-switch>
                    <span class="form-tip">显示"🆕新品"标签</span>
                  </el-form-item>
                  
                  <el-form-item label="限时图标">
                    <el-switch v-model="product.isLimited"></el-switch>
                    <span class="form-tip">显示"⏰限时"标签</span>
                  </el-form-item>
                  
                  <el-form-item label="自定义标签">
                    <el-input v-model="product.customTag" placeholder="如：包邮，满减" maxlength="10" show-word-limit></el-input>
                  </el-form-item>
                </div>
                
                <!-- 样式设置 -->
                <div class="form-group">
                  <h5>🎨 样式设置</h5>
                  <el-form-item label="名称字体">
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <el-input-number v-model="product.nameFontSize" :min="12" :max="20" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-col>
                      <el-col :span="8">
                        <el-select v-model="product.nameFontWeight">
                          <el-option label="正常" value="400"></el-option>
                          <el-option label="中等" value="500"></el-option>
                          <el-option label="粗体" value="600"></el-option>
                          <el-option label="特粗" value="700"></el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="8">
                        <el-color-picker v-model="product.nameColor"></el-color-picker>
                      </el-col>
                    </el-row>
                  </el-form-item>
                  
                  <el-form-item label="描述字体">
                    <el-row :gutter="10">
                      <el-col :span="12">
                        <el-input-number v-model="product.descriptionFontSize" :min="10" :max="16" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-col>
                      <el-col :span="12">
                        <el-color-picker v-model="product.descriptionColor"></el-color-picker>
                      </el-col>
                    </el-row>
                  </el-form-item>
                  
                  <el-form-item label="价格字体">
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <el-input-number v-model="product.priceFontSize" :min="14" :max="24" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-col>
                      <el-col :span="8">
                        <el-select v-model="product.priceFontWeight">
                          <el-option label="中等" value="500"></el-option>
                          <el-option label="粗体" value="600"></el-option>
                          <el-option label="特粗" value="700"></el-option>
                          <el-option label="超粗" value="800"></el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="8">
                        <el-color-picker v-model="product.priceColor"></el-color-picker>
                      </el-col>
                    </el-row>
                  </el-form-item>
                  
                  <el-form-item label="按钮设置">
                    <el-row :gutter="10">
                      <el-col :span="12">
                        <el-input v-model="product.buttonText" placeholder="立即购买" maxlength="8" show-word-limit></el-input>
                      </el-col>
                      <el-col :span="12">
                        <el-color-picker v-model="product.buttonColor"></el-color-picker>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-tab-pane>
</template>

<script>
// 这是专业版DIY编辑器的产品设置部分
export default {
  data() {
    return {
      productsActiveNames: ['global'],
      
      // 产品配置的默认数据结构
      defaultProductConfig: {
        enabled: true,
        displayMode: 'carousel', // carousel, grid, list
        autoPlay: true,
        interval: 3,
        columns: 2,
        backgroundColor: 'rgba(248,249,250,0.95)',
        borderRadius: 12,
        items: [
          {
            image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop',
            name: '经典美式咖啡',
            description: '精选优质咖啡豆，香醇浓郁，回味悠长',
            specifications: '容量：350ml，温度：65-75℃',
            originalPrice: '35.00',
            currentPrice: '28.00',
            discount: '限时8折',
            buyUrl: 'https://example.com/coffee',
            isHot: true,
            isBestSeller: false,
            isNew: false,
            isLimited: true,
            customTag: '包邮',
            nameFontSize: 16,
            nameFontWeight: '600',
            nameColor: '#333333',
            descriptionFontSize: 12,
            descriptionColor: '#666666',
            priceFontSize: 18,
            priceFontWeight: '700',
            priceColor: '#ff4757',
            buttonText: '立即购买',
            buttonColor: '#ff4757'
          }
        ]
      }
    }
  },
  
  methods: {
    // 添加产品
    addProduct() {
      const newProduct = {
        image: '',
        name: '新产品',
        description: '请输入产品描述',
        specifications: '请输入产品配置信息',
        originalPrice: '99.00',
        currentPrice: '79.00',
        discount: '',
        buyUrl: '',
        isHot: false,
        isBestSeller: false,
        isNew: true,
        isLimited: false,
        customTag: '',
        nameFontSize: 16,
        nameFontWeight: '600',
        nameColor: '#333333',
        descriptionFontSize: 12,
        descriptionColor: '#666666',
        priceFontSize: 18,
        priceFontWeight: '700',
        priceColor: '#ff4757',
        buttonText: '立即购买',
        buttonColor: '#ff4757'
      }
      
      this.config.products.items.push(newProduct)
      // 自动展开新添加的产品
      const newIndex = this.config.products.items.length - 1
      this.productsActiveNames.push('product-' + newIndex)
      this.$message.success('产品添加成功')
    },
    
    // 删除产品
    removeProduct(index) {
      if (this.config.products.items.length <= 1) {
        this.$message.warning('至少需要保留一个产品')
        return
      }
      
      this.$confirm('确定要删除这个产品吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.config.products.items.splice(index, 1)
        // 移除对应的折叠面板
        const panelName = 'product-' + index
        const panelIndex = this.productsActiveNames.indexOf(panelName)
        if (panelIndex > -1) {
          this.productsActiveNames.splice(panelIndex, 1)
        }
        this.$message.success('产品删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 处理产品图片上传
    handleProductImageUpload(file, index) {
      if (file.size > 1024 * 1024) {
        this.$message.error('图片大小不能超过1MB')
        return false
      }
      
      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.products.items[index].image = e.target.result
        this.saveToHistory()
      }
      reader.readAsDataURL(file)
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.form-group {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  
  h5 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }
}

.product-editor {
  .product-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-uploader {
  ::v-deep .el-upload {
    width: 120px;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }
  }
}

.product-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
