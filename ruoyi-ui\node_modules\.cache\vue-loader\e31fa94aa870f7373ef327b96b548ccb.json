{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue?vue&type=style&index=0&id=320cffa6&lang=scss&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue", "mtime": 1754628577485}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753759480805}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753759474011}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753759476521}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753759475309}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc2hpcGluLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmNWY1ZjU7DQogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsNCn0NCg0KLnBhZ2UtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7DQoNCiAgLmhlYWRlci1jb250ZW50IHsNCiAgICAucGFnZS10aXRsZSB7DQogICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICBtYXJnaW46IDAgMCA4cHggMDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICBpIHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgICAgICBjb2xvcjogIzQwOWVmZjsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAucGFnZS1kZXNjcmlwdGlvbiB7DQogICAgICBjb2xvcjogIzdmOGM4ZDsNCiAgICAgIG1hcmdpbjogMDsNCiAgICB9DQogIH0NCn0NCg0KLnByb21wdC1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLDAsMCwwLjEpOw0KDQogIGgzIHsNCiAgICBmb250LXNpemU6IDE4cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogIzJjM2U1MDsNCiAgICBtYXJnaW46IDAgMCAyMHB4IDA7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgaSB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogICAgICBjb2xvcjogI2U2YTIzYzsNCiAgICB9DQogIH0NCg0KICAucHJvbXB0LWdyaWQgew0KICAgIGRpc3BsYXk6IGdyaWQ7DQogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7DQogICAgZ2FwOiAxNnB4Ow0KDQogICAgLnByb21wdC1jYXJkIHsNCiAgICAgIHBhZGRpbmc6IDIwcHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KICAgICAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgICAgICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4xNSk7DQogICAgICB9DQoNCiAgICAgIC5wcm9tcHQtaWNvbiB7DQogICAgICAgIGZvbnQtc2l6ZTogMzJweDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgfQ0KDQogICAgICAucHJvbXB0LXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBjb2xvcjogIzJjM2U1MDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgICAgfQ0KDQogICAgICAucHJvbXB0LWRlc2Mgew0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgfQ0KDQogICAgICAucHJvbXB0LXByZXZpZXcgew0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgIGNvbG9yOiAjOTVhNWE2Ow0KICAgICAgICBsaW5lLWhlaWdodDogMS40Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICAgICAgICBwYWRkaW5nOiA4cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLnRlbXBsYXRlLXNlY3Rpb24gew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7DQoNCiAgaDMgew0KICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICBmb250LXdlaWdodDogNjAwOw0KICAgIGNvbG9yOiAjMmMzZTUwOw0KICAgIG1hcmdpbjogMCAwIDIwcHggMDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICBpIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgIGNvbG9yOiAjNDA5ZWZmOw0KICAgIH0NCiAgfQ0KDQogIC50ZW1wbGF0ZS1ncmlkIHsNCiAgICBkaXNwbGF5OiBncmlkOw0KICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDI4MHB4LCAxZnIpKTsNCiAgICBnYXA6IDE2cHg7DQoNCiAgICAudGVtcGxhdGUtY2FyZCB7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KICAgICAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgICAgIHBhZGRpbmc6IDIwcHg7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KDQogICAgICAmOmhvdmVyIHsNCiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMTUpOw0KICAgICAgfQ0KDQogICAgICAudGVtcGxhdGUtaGVhZGVyIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KDQogICAgICAgIC50ZW1wbGF0ZS10eXBlIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjBmMGYwOw0KICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICAgIHBhZGRpbmc6IDRweCAxMnB4Ow0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnRlbXBsYXRlLWhvdCB7DQogICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC50ZW1wbGF0ZS10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgIH0NCg0KICAgICAgLnRlbXBsYXRlLXByZXZpZXcgew0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICBsaW5lLWhlaWdodDogMS41Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgfQ0KDQogICAgICAudGVtcGxhdGUtc3RhdHMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBnYXA6IDE2cHg7DQoNCiAgICAgICAgLnN0YXQtaXRlbSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBjb2xvcjogIzdmOGM4ZDsNCg0KICAgICAgICAgIGkgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi5saWJyYXJ5LXNlY3Rpb24gew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7DQoNCiAgLnNlY3Rpb24taGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICBoMyB7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICBtYXJnaW46IDA7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCg0KICAgICAgaSB7DQogICAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgICAgY29sb3I6ICM0MDllZmY7DQogICAgICB9DQogICAgfQ0KDQogICAgLnNlY3Rpb24tZmlsdGVycyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICB9DQogIH0NCg0KICAubGlicmFyeS1saXN0IHsNCiAgICAubGlicmFyeS1pdGVtIHsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQogICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICBwYWRkaW5nOiAyMHB4Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMTUpOw0KICAgICAgfQ0KDQogICAgICAuaXRlbS1oZWFkZXIgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQoNCiAgICAgICAgLml0ZW0tdGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICAgIGNvbG9yOiAjMmMzZTUwOw0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCg0KICAgICAgICAgIGkgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgICAgICBjb2xvcjogIzQwOWVmZjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuaXRlbS1tZXRhIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgZ2FwOiAxMnB4Ow0KDQogICAgICAgICAgLml0ZW0tdGltZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBjb2xvcjogIzdmOGM4ZDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLml0ZW0tY29udGVudCB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICAgICAgLmxpYnJhcnktaW5mbyB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBnYXA6IDI0cHg7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCg0KICAgICAgICAgIC5pbmZvLWl0ZW0gew0KICAgICAgICAgICAgLmxhYmVsIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzdmOGM4ZDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzJjM2U1MDsNCiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAucHJvZ3Jlc3MtaW5mbyB7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCg0KICAgICAgICAgIC5wcm9ncmVzcy10ZXh0IHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICAgICAgbWFyZ2luLXRvcDogOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5zaG9wLWluZm8gew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzdmOGM4ZDsNCg0KICAgICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5wcmV2aWV3IHsNCiAgICAgICAgICAgIGNvbG9yOiAjOTVhNWE2Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuaXRlbS1hY3Rpb25zIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuZW1wdHktc3RhdGUgew0KICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgcGFkZGluZzogNjBweCAyMHB4Ow0KDQogICAgICBpIHsNCiAgICAgICAgZm9udC1zaXplOiA2NHB4Ow0KICAgICAgICBjb2xvcjogI2RkZDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICAgIH0NCg0KICAgICAgaDMgew0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICBtYXJnaW46IDAgMCA4cHggMDsNCiAgICAgIH0NCg0KICAgICAgcCB7DQogICAgICAgIGNvbG9yOiAjOTVhNWE2Ow0KICAgICAgICBtYXJnaW46IDAgMCAyMHB4IDA7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi5saWJyYXJ5LWRldGFpbCB7DQogIC5kZXRhaWwtaGVhZGVyIHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgaDMgew0KICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIGNvbG9yOiAjMmMzZTUwOw0KICAgICAgbWFyZ2luOiAwIDAgMTJweCAwOw0KICAgIH0NCg0KICAgIC5kZXRhaWwtbWV0YSB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGdhcDogMTZweDsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgIH0NCiAgfQ0KDQogIC5kZXRhaWwtaW5mbyB7DQogICAgbWFyZ2luLWJvdHRvbTogMjRweDsNCg0KICAgIC5pbmZvLWdyaWQgew0KICAgICAgZGlzcGxheTogZ3JpZDsNCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMTUwcHgsIDFmcikpOw0KICAgICAgZ2FwOiAxNnB4Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCg0KICAgICAgLmluZm8taXRlbSB7DQogICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC52YWx1ZSB7DQogICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgIGNvbG9yOiAjMmMzZTUwOw0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAuc2hvcC1kZXRhaWxzLA0KICAgIC5wcm9tcHQtaW5mbyB7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KDQogICAgICBoNCB7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICAgIG1hcmdpbjogMCAwIDhweCAwOw0KICAgICAgfQ0KDQogICAgICAuZGV0YWlscy10ZXh0LA0KICAgICAgLnByb21wdC10ZXh0IHsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogICAgICAgIHBhZGRpbmc6IDEycHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5jb3B5d3JpdGluZy1saXN0IHsNCiAgICAubGlzdC1oZWFkZXIgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgICAgcGFkZGluZy1ib3R0b206IDEycHg7DQogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCg0KICAgICAgaDQgew0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGNvbG9yOiAjMmMzZTUwOw0KICAgICAgICBtYXJnaW46IDA7DQogICAgICB9DQoNCiAgICAgIC5saXN0LWFjdGlvbnMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBnYXA6IDhweDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuY29udGVudC1saXN0IHsNCiAgICAgIG1heC1oZWlnaHQ6IDQwMHB4Ow0KICAgICAgb3ZlcmZsb3cteTogYXV0bzsNCg0KICAgICAgLmNvbnRlbnQtaXRlbSB7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgICAgICAgcGFkZGluZzogMTZweDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogICAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjE1KTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5jb250ZW50LWhlYWRlciB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQoNCiAgICAgICAgICAuY29udGVudC1pbmRleCB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjNDA5ZWZmOw0KICAgICAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgICAgICBwYWRkaW5nOiAycHggOHB4Ow0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgIG1pbi13aWR0aDogMzBweDsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuY29udGVudC10aW1lIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIGNvbG9yOiAjN2Y4YzhkOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jb250ZW50LWFjdGlvbnMgew0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGdhcDogNHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5jb250ZW50LXRleHQgew0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7DQogICAgICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5lbXB0eS1jb250ZW50IHsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICBwYWRkaW5nOiA0MHB4IDIwcHg7DQoNCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiA0OHB4Ow0KICAgICAgICAgIGNvbG9yOiAjZGRkOw0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgICAgIH0NCg0KICAgICAgICBwIHsNCiAgICAgICAgICBjb2xvcjogIzdmOGM4ZDsNCiAgICAgICAgICBtYXJnaW46IDAgMCAxNnB4IDA7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmZvcm0tdGlwIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0KDQovLyDnp7vliqjnq6/kvJjljJbmoLflvI8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuc2hpcGluLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMTJweDsNCiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICB9DQoNCiAgLnBhZ2UtaGVhZGVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCg0KICAgIC5oZWFkZXItY29udGVudCB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQoNCiAgICAgIC5wYWdlLXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KDQogICAgICAgIGkgew0KICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5wYWdlLWRlc2NyaXB0aW9uIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5oZWFkZXItYWN0aW9ucyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBnYXA6IDhweDsNCg0KICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAucHJvbXB0LXNlY3Rpb24gew0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCg0KICAgIGgzIHsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgfQ0KDQogICAgLnByb21wdC1ncmlkIHsNCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7DQogICAgICBnYXA6IDEycHg7DQoNCiAgICAgIC5wcm9tcHQtY2FyZCB7DQogICAgICAgIHBhZGRpbmc6IDE2cHg7DQoNCiAgICAgICAgLnByb21wdC1pY29uIHsNCiAgICAgICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnByb21wdC10aXRsZSB7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDZweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5wcm9tcHQtZGVzYyB7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5wcm9tcHQtcHJldmlldyB7DQogICAgICAgICAgZm9udC1zaXplOiAxMXB4Ow0KICAgICAgICAgIHBhZGRpbmc6IDZweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC50ZW1wbGF0ZS1zZWN0aW9uIHsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICBoMyB7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgIH0NCg0KICAgIC50ZW1wbGF0ZS1ncmlkIHsNCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOw0KICAgICAgZ2FwOiAxMnB4Ow0KDQogICAgICAudGVtcGxhdGUtY2FyZCB7DQogICAgICAgIHBhZGRpbmc6IDE2cHg7DQoNCiAgICAgICAgLnRlbXBsYXRlLXRpdGxlIHsNCiAgICAgICAgICBmb250LXNpemU6IDE1cHg7DQogICAgICAgIH0NCg0KICAgICAgICAudGVtcGxhdGUtcHJldmlldyB7DQogICAgICAgICAgZm9udC1zaXplOiAxM3B4Ow0KICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsNCiAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAubGlicmFyeS1zZWN0aW9uIHsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KDQogICAgLnNlY3Rpb24taGVhZGVyIHsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICAgIGgzIHsNCiAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgfQ0KDQogICAgICAuc2VjdGlvbi1maWx0ZXJzIHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGdhcDogOHB4Ow0KDQogICAgICAgIC5lbC1zZWxlY3Qgew0KICAgICAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCg0KICAgICAgICAuZWwtaW5wdXQgew0KICAgICAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDAgIWltcG9ydGFudDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5saWJyYXJ5LWxpc3Qgew0KICAgICAgLmxpYnJhcnktaXRlbSB7DQogICAgICAgIHBhZGRpbmc6IDE2cHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQoNCiAgICAgICAgLml0ZW0taGVhZGVyIHsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQoNCiAgICAgICAgICAuaXRlbS10aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE1cHg7DQogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLml0ZW0tbWV0YSB7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICAgICAgICAgIGdhcDogOHB4Ow0KDQogICAgICAgICAgICAuaXRlbS10aW1lIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMXB4Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5pdGVtLWNvbnRlbnQgew0KICAgICAgICAgIC5saWJyYXJ5LWluZm8gew0KICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAgIGdhcDogOHB4Ow0KDQogICAgICAgICAgICAuaW5mby1pdGVtIHsNCiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogICAgICAgICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDEzcHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc2hvcC1pbmZvIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCg0KICAgICAgICAgICAgLnByZXZpZXcgew0KICAgICAgICAgICAgICBkaXNwbGF5OiBibG9jazsNCiAgICAgICAgICAgICAgbWFyZ2luLXRvcDogNHB4Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5pdGVtLWFjdGlvbnMgew0KICAgICAgICAgIGdhcDogNnB4Ow0KDQogICAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgcGFkZGluZzogNnB4IDhweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmVtcHR5LXN0YXRlIHsNCiAgICAgICAgcGFkZGluZzogNDBweCAyMHB4Ow0KDQogICAgICAgIGkgew0KICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDsNCiAgICAgICAgfQ0KDQogICAgICAgIGgzIHsNCiAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIH0NCg0KICAgICAgICBwIHsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDlr7nor53moYbnp7vliqjnq6/kvJjljJYNCiAgLmNyZWF0ZS1kaWFsb2csDQogIC52aWV3LWRpYWxvZyB7DQogICAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgICBwYWRkaW5nOiAxNnB4Ow0KICAgICAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDEyMHB4KTsNCiAgICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgfQ0KDQogICAgLmVsLWZvcm0gew0KICAgICAgLmVsLWZvcm0taXRlbSB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQoNCiAgICAgICAgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMS40Ow0KICAgICAgICB9DQoNCiAgICAgICAgLmVsLWlucHV0LA0KICAgICAgICAuZWwtc2VsZWN0LA0KICAgICAgICAuZWwtdGV4dGFyZWEgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5lbC1jaGVja2JveC1ncm91cCB7DQogICAgICAgICAgLmVsLWNoZWNrYm94IHsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTZweDsNCg0KICAgICAgICAgICAgLmVsLWNoZWNrYm94X19sYWJlbCB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAuZWwtZGlhbG9nX19mb290ZXIgew0KICAgICAgcGFkZGluZzogMTJweCAxNnB4Ow0KDQogICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5saWJyYXJ5LWRldGFpbCB7DQogICAgLmRldGFpbC1oZWFkZXIgew0KICAgICAgaDMgew0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICB9DQoNCiAgICAgIC5kZXRhaWwtbWV0YSB7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgICAgICBnYXA6IDhweDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuZGV0YWlsLWluZm8gew0KICAgICAgLmluZm8tZ3JpZCB7DQogICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOw0KICAgICAgICBnYXA6IDEycHg7DQoNCiAgICAgICAgLmluZm8taXRlbSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7DQogICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQoNCiAgICAgICAgICAubGFiZWwgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC52YWx1ZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5zaG9wLWRldGFpbHMsDQogICAgICAucHJvbXB0LWluZm8gew0KICAgICAgICAuZGV0YWlscy10ZXh0LA0KICAgICAgICAucHJvbXB0LXRleHQgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgICBwYWRkaW5nOiAxMHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmNvcHl3cml0aW5nLWxpc3Qgew0KICAgICAgLmxpc3QtaGVhZGVyIHsNCiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogICAgICAgIGdhcDogMTJweDsNCg0KICAgICAgICBoNCB7DQogICAgICAgICAgZm9udC1zaXplOiAxNXB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLmxpc3QtYWN0aW9ucyB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQoNCiAgICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5jb250ZW50LWxpc3Qgew0KICAgICAgICBtYXgtaGVpZ2h0OiAzMDBweDsNCg0KICAgICAgICAuY29udGVudC1pdGVtIHsNCiAgICAgICAgICBwYWRkaW5nOiAxMnB4Ow0KDQogICAgICAgICAgLmNvbnRlbnQtaGVhZGVyIHsNCiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICAgICAgICAgIGdhcDogOHB4Ow0KDQogICAgICAgICAgICAuY29udGVudC1hY3Rpb25zIHsNCiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCg0KICAgICAgICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgICAgICAgIG1hcmdpbjogMCAycHg7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMXB4Ow0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IDRweCA2cHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuY29udGVudC10ZXh0IHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi8vIOi2heWwj+Wxj+W5leS8mOWMliAo5bCP5LqONDgwcHgpDQpAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsNCiAgLnNoaXBpbi1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDhweDsNCiAgfQ0KDQogIC5wcm9tcHQtc2VjdGlvbiB7DQogICAgLnByb21wdC1ncmlkIHsNCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOw0KICAgIH0NCiAgfQ0KDQogIC5saWJyYXJ5LWRldGFpbCB7DQogICAgLmRldGFpbC1pbmZvIHsNCiAgICAgIC5pbmZvLWdyaWQgew0KICAgICAgICAuaW5mby1pdGVtIHsNCiAgICAgICAgICBwYWRkaW5nOiA2cHggMTBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jb3B5d3JpdGluZy1saXN0IHsNCiAgICAgIC5jb250ZW50LWxpc3Qgew0KICAgICAgICAuY29udGVudC1pdGVtIHsNCiAgICAgICAgICBwYWRkaW5nOiAxMHB4Ow0KDQogICAgICAgICAgLmNvbnRlbnQtaGVhZGVyIHsNCiAgICAgICAgICAgIC5jb250ZW50LWFjdGlvbnMgew0KICAgICAgICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDEwcHg7DQogICAgICAgICAgICAgICAgcGFkZGluZzogM3B4IDVweDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDmj5DnpLror43luK7liqnlr7nor53moYbmoLflvI8NCjo6di1kZWVwIC5wcm9tcHQtaGVscC1kaWFsb2cgew0KICAuZWwtbWVzc2FnZS1ib3ggew0KICAgIHdpZHRoOiA2MDBweDsNCiAgICBtYXgtd2lkdGg6IDkwdnc7DQogIH0NCg0KICAuZWwtbWVzc2FnZS1ib3hfX2NvbnRlbnQgew0KICAgIG1heC1oZWlnaHQ6IDUwMHB4Ow0KICAgIG92ZXJmbG93LXk6IGF1dG87DQogIH0NCg0KICBoNCwgaDUgew0KICAgIGNvbG9yOiAjNDA5RUZGOw0KICAgIG1hcmdpbjogMTVweCAwIDEwcHggMDsNCiAgfQ0KDQogIHAgew0KICAgIG1hcmdpbjogOHB4IDA7DQogICAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgfQ0KDQogIHN0cm9uZyB7DQogICAgY29sb3I6ICMzMDMxMzM7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["dou-backup.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA85CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "dou-backup.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"shipin-container\">\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <h1 class=\"page-title\">\r\n          <i class=\"el-icon-magic-stick\"></i>\r\n          AI文案生成库\r\n        </h1>\r\n        <p class=\"page-description\">基于火山引擎Doubao，智能生成高质量文案内容</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- AI提示词推荐 -->\r\n    <div class=\"prompt-section\">\r\n      <h3>\r\n        <i class=\"el-icon-lightbulb\"></i>\r\n        AI提示词推荐\r\n      </h3>\r\n      <div class=\"prompt-grid\">\r\n        <div class=\"prompt-card\" @click=\"usePrompt(prompt)\" v-for=\"prompt in recommendPrompts\" :key=\"prompt.id\">\r\n          <div class=\"prompt-icon\">{{ prompt.icon }}</div>\r\n          <div class=\"prompt-title\">{{ prompt.title }}</div>\r\n          <div class=\"prompt-desc\">{{ prompt.desc }}</div>\r\n          <div class=\"prompt-preview\">{{ prompt.content.substring(0, 50) }}...</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文案库列表 -->\r\n    <div class=\"library-section\">\r\n      <div class=\"section-header\">\r\n        <h3>\r\n          <i class=\"el-icon-folder-opened\"></i>\r\n          我的文案库\r\n        </h3>\r\n        <div class=\"section-filters\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refreshData\"\r\n            style=\"margin-right: 12px;\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n          <el-select v-model=\"filterStatus\" placeholder=\"状态\" size=\"small\" style=\"width: 120px;\">\r\n            <el-option label=\"全部\" value=\"\"></el-option>\r\n            <el-option label=\"未开始\" value=\"pending\"></el-option>\r\n            <el-option label=\"生成中\" value=\"generating\"></el-option>\r\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\r\n            <el-option label=\"生成失败\" value=\"failed\"></el-option>\r\n          </el-select>\r\n          <el-input\r\n            v-model=\"searchKeyword\"\r\n            placeholder=\"搜索文案库...\"\r\n            size=\"small\"\r\n            clearable\r\n            style=\"width: 200px; margin-left: 12px;\"\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"library-list\">\r\n        <div v-for=\"library in filteredLibraryList\" :key=\"library.id\" class=\"library-item\">\r\n          <div class=\"item-header\">\r\n            <div class=\"item-title\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              {{ library.name }}\r\n            </div>\r\n            <div class=\"item-meta\">\r\n              <el-tag size=\"mini\" :type=\"getStatusColor(library.status)\">{{ getStatusName(library.status) }}</el-tag>\r\n              <el-tag size=\"mini\" type=\"info\" v-if=\"library.useAI\">AI生成</el-tag>\r\n              <span class=\"item-time\">{{ library.createTime }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-content\">\r\n            <div class=\"library-info\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">目标条数：</span>\r\n                <span class=\"value\">{{ library.targetCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">已生成：</span>\r\n                <span class=\"value\">{{ library.generatedCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">字数要求：</span>\r\n                <span class=\"value\">{{ library.wordCount }}字</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 生成进度 -->\r\n            <div class=\"progress-info\" v-if=\"library.status === 'generating'\">\r\n              <el-progress\r\n                :percentage=\"Math.round((library.generatedCount / library.targetCount) * 100)\"\r\n                status=\"success\"\r\n              ></el-progress>\r\n              <div class=\"progress-text\">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>\r\n            </div>\r\n\r\n            <!-- 店铺详情预览 -->\r\n            <div class=\"shop-info\" v-if=\"library.shopDetails\">\r\n              <span class=\"label\">店铺详情：</span>\r\n              <span class=\"preview\">{{ library.shopDetails.substring(0, 50) }}...</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-actions\">\r\n            <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewLibrary(library)\" :disabled=\"library.status === 'pending'\">\r\n              查看文案 ({{ library.generatedCount }})\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"success\" icon=\"el-icon-plus\" @click=\"addToLibrary(library)\" v-if=\"library.status === 'completed'\">\r\n              新增文案\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-refresh\" @click=\"regenerateLibrary(library)\" v-if=\"library.status === 'failed'\">\r\n              重新生成\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteLibrary(library)\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLibraryList.length === 0\" class=\"empty-state\">\r\n          <i class=\"el-icon-folder-add\"></i>\r\n          <h3>暂无文案库</h3>\r\n          <p>点击\"创建文案库\"按钮创建您的第一个AI文案库</p>\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 创建文案库对话框 -->\r\n    <el-dialog\r\n      title=\"创建AI文案库\"\r\n      :visible.sync=\"createLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"create-dialog\"\r\n    >\r\n      <el-form :model=\"createLibraryForm\" :rules=\"createLibraryRules\" ref=\"createLibraryForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库名称\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"createLibraryForm.name\"\r\n            placeholder=\"请输入文案库名称\"\r\n            maxlength=\"50\"\r\n            show-word-limit\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"createLibraryForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动创建\"\r\n          ></el-switch>\r\n          <div class=\"form-tip\">开启后将使用火山引擎Doubao生成文案</div>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"createLibraryForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.shopDetails\"\r\n              placeholder=\"请详细描述您的店铺信息、产品特色、目标客户等\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              maxlength=\"500\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">详细的店铺信息有助于AI生成更精准的文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.prompt\"\r\n              placeholder=\"请输入AI生成文案的提示词\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">\r\n              示例：生成吸引人的美食推广文案，要求语言生动、有食欲感\r\n              <el-button type=\"text\" @click=\"showPromptHelp\">查看提示词建议</el-button>\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"createLibraryForm.count\"\r\n              :min=\"1\"\r\n              :max=\"50\"\r\n              placeholder=\"请输入生成条数\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n            <div class=\"form-tip\">最多可生成50条文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"大约字数\" prop=\"wordCount\">\r\n            <el-select v-model=\"createLibraryForm.wordCount\" placeholder=\"请选择文案字数\">\r\n              <el-option label=\"50字以内\" value=\"50\"></el-option>\r\n              <el-option label=\"100字左右\" value=\"100\"></el-option>\r\n              <el-option label=\"200字左右\" value=\"200\"></el-option>\r\n              <el-option label=\"300字左右\" value=\"300\"></el-option>\r\n              <el-option label=\"500字左右\" value=\"500\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"createLibraryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createLibrary\" :loading=\"creating\">\r\n          {{ creating ? '创建中...' : '创建文案库' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新增文案对话框 -->\r\n    <el-dialog\r\n      title=\"新增文案\"\r\n      :visible.sync=\"addCopywritingDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"add-dialog\"\r\n    >\r\n      <el-form :model=\"addCopywritingForm\" :rules=\"addCopywritingRules\" ref=\"addCopywritingForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库\">\r\n          <el-input :value=\"currentLibrary ? currentLibrary.name : ''\" disabled></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"addCopywritingForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动输入\"\r\n          ></el-switch>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"addCopywritingForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.shopDetails\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"店铺详情（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.prompt\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"AI提示词（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"addCopywritingForm.count\"\r\n              :min=\"1\"\r\n              :max=\"20\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <template v-else>\r\n          <el-form-item label=\"文案内容\" prop=\"content\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.content\"\r\n              type=\"textarea\"\r\n              :rows=\"6\"\r\n              placeholder=\"请输入文案内容\"\r\n              maxlength=\"1000\"\r\n              show-word-limit\r\n            ></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"addCopywritingDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"addCopywriting\" :loading=\"adding\">\r\n          {{ adding ? (addCopywritingForm.useAI ? '生成中...' : '添加中...') : (addCopywritingForm.useAI ? '生成文案' : '添加文案') }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看文案库对话框 -->\r\n    <el-dialog\r\n      title=\"文案库详情\"\r\n      :visible.sync=\"viewLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '900px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"view-dialog\"\r\n    >\r\n      <div v-if=\"currentLibrary\" class=\"library-detail\">\r\n        <div class=\"detail-header\">\r\n          <h3>{{ currentLibrary.name }}</h3>\r\n          <div class=\"detail-meta\">\r\n            <el-tag :type=\"getStatusColor(currentLibrary.status)\">{{ getStatusName(currentLibrary.status) }}</el-tag>\r\n            <el-tag type=\"info\" v-if=\"currentLibrary.useAI\">AI生成</el-tag>\r\n            <span>创建时间：{{ currentLibrary.createTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"detail-info\">\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">目标条数：</span>\r\n              <span class=\"value\">{{ currentLibrary.targetCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">已生成：</span>\r\n              <span class=\"value\">{{ currentLibrary.generatedCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">字数要求：</span>\r\n              <span class=\"value\">{{ currentLibrary.wordCount }}字</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"shop-details\" v-if=\"currentLibrary.shopDetails\">\r\n            <h4>店铺详情：</h4>\r\n            <div class=\"details-text\">{{ currentLibrary.shopDetails }}</div>\r\n          </div>\r\n\r\n          <div class=\"prompt-info\" v-if=\"currentLibrary.prompt\">\r\n            <h4>AI提示词：</h4>\r\n            <div class=\"prompt-text\">{{ currentLibrary.prompt }}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"copywriting-list\">\r\n          <div class=\"list-header\">\r\n            <h4>文案列表 ({{ libraryContents.length }})</h4>\r\n            <div class=\"list-actions\">\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addToLibrary(currentLibrary)\">新增文案</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"loadLibraryContents(currentLibrary.id)\">刷新</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"content-list\">\r\n            <div v-for=\"(content, index) in libraryContents\" :key=\"content.id\" class=\"content-item\">\r\n              <div class=\"content-header\">\r\n                <span class=\"content-index\">{{ index + 1 }}</span>\r\n                <span class=\"content-time\">{{ content.createTime }}</span>\r\n                <div class=\"content-actions\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewContent(content)\">查看</el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-document-copy\" @click=\"copyContent(content)\">复制</el-button>\r\n                  <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-edit\" @click=\"editContent(content)\">编辑</el-button>\r\n                  <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteContent(content)\">删除</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"content-text\">{{ content.content }}</div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"libraryContents.length === 0\" class=\"empty-content\">\r\n              <i class=\"el-icon-document-add\"></i>\r\n              <p>暂无文案内容</p>\r\n              <el-button size=\"small\" type=\"primary\" @click=\"addToLibrary(currentLibrary)\">添加第一条文案</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"viewLibraryDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"success\" @click=\"exportLibrary(currentLibrary)\">导出文案库</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLibrary,\r\n  addLibrary,\r\n  delLibrary,\r\n  generateCopywriting,\r\n  listContent,\r\n  addContent,\r\n  updateContent,\r\n  delContent,\r\n  getProgress,\r\n  regenerateLibrary,\r\n  validateBaiduConfig,\r\n  getModelInfo\r\n} from '@/api/ai/copywriting'\r\n\r\n// 导入测试API作为备用\r\nimport {\r\n  listLibraryTest,\r\n  addLibraryTest,\r\n  testDeepSeekDirect,\r\n  healthCheck\r\n} from '@/api/ai/copywriting-test'\r\n\r\nexport default {\r\n  name: 'StorerShipin',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createLibraryDialogVisible: false,\r\n      addCopywritingDialogVisible: false,\r\n      viewLibraryDialogVisible: false,\r\n\r\n      // 加载状态\r\n      creating: false,\r\n      adding: false,\r\n\r\n      // 筛选和搜索\r\n      filterStatus: '',\r\n      searchKeyword: '',\r\n\r\n      // 当前数据\r\n      currentLibrary: null,\r\n      libraryContents: [],\r\n      isMobile: false,\r\n\r\n      // 创建文案库表单\r\n      createLibraryForm: {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '200' // AI剪辑文案默认200字\r\n      },\r\n      createLibraryRules: {\r\n        name: [\r\n          { required: true, message: '请输入文案库名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' },\r\n          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' },\r\n          { min: 5, max: 300, message: '长度在 5 到 300 个字符', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        wordCount: [\r\n          { required: true, message: '请选择文案字数', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 新增文案表单\r\n      addCopywritingForm: {\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 5,\r\n        content: ''\r\n      },\r\n      addCopywritingRules: {\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入文案内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // AI提示词推荐\r\n      recommendPrompts: [\r\n        {\r\n          id: 1,\r\n          icon: '🍔',\r\n          title: '美食推广',\r\n          desc: '适合餐饮店铺',\r\n          content: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围，能够激发顾客的购买欲望'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: '👗',\r\n          title: '服装时尚',\r\n          desc: '适合服装店铺',\r\n          content: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议，展现品牌调性和时尚态度'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: '💄',\r\n          title: '美妆护肤',\r\n          desc: '适合美妆店铺',\r\n          content: '编写专业的美妆护肤文案，强调产品功效、使用体验、适用肌肤类型，传递美丽自信的理念'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: '🏠',\r\n          title: '家居生活',\r\n          desc: '适合家居店铺',\r\n          content: '撰写温馨的家居生活文案，展现产品实用性、设计美感、生活品质提升，营造舒适家庭氛围'\r\n        },\r\n        {\r\n          id: 5,\r\n          icon: '📱',\r\n          title: '数码科技',\r\n          desc: '适合数码店铺',\r\n          content: '制作专业的数码产品文案，突出技术参数、功能特点、使用场景，体现科技感和实用价值'\r\n        },\r\n        {\r\n          id: 6,\r\n          icon: '🎓',\r\n          title: '教育培训',\r\n          desc: '适合教育机构',\r\n          content: '创建有说服力的教育培训文案，强调课程价值、师资力量、学习效果，激发学习兴趣和报名意愿'\r\n        }\r\n      ],\r\n\r\n      // 文案库列表\r\n      libraryList: [\r\n        {\r\n          id: 1,\r\n          name: '美食探店文案库',\r\n          useAI: true,\r\n          status: 'completed',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: '100',\r\n          shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',\r\n          prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围',\r\n          createTime: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '时尚服装推广库',\r\n          useAI: true,\r\n          status: 'generating',\r\n          targetCount: 30,\r\n          generatedCount: 15,\r\n          wordCount: '150',\r\n          shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',\r\n          prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',\r\n          createTime: '2024-01-15 10:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '手动创建文案库',\r\n          useAI: false,\r\n          status: 'completed',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: '200',\r\n          shopDetails: '',\r\n          prompt: '',\r\n          createTime: '2024-01-14 16:20:00'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredLibraryList() {\r\n      let list = this.libraryList\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        list = list.filter(item => item.status === this.filterStatus)\r\n      }\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        list = list.filter(item =>\r\n          item.name.toLowerCase().includes(keyword) ||\r\n          (item.shopDetails && item.shopDetails.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      return list\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化持久化存储\r\n    this.loadLibraryContentFromStorage()\r\n\r\n    this.loadLibraryList()\r\n\r\n    // 备用方案：如果3秒后还没有数据，直接加载模拟数据\r\n    setTimeout(() => {\r\n      if (this.libraryList.length === 0) {\r\n        console.log('3秒后仍无数据，强制加载模拟数据')\r\n        this.loadMockLibraryList()\r\n      }\r\n    }, 3000)\r\n  },\r\n  mounted() {\r\n    this.checkMobile()\r\n    window.addEventListener('resize', this.checkMobile)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkMobile)\r\n  },\r\n  methods: {\r\n    checkMobile() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n\r\n    loadLibraryList() {\r\n      listLibrary().then(response => {\r\n        this.libraryList = response.rows || response.data || []\r\n        if (this.libraryList.length === 0) {\r\n          // 如果返回空数据，也加载模拟数据\r\n          this.loadMockLibraryList()\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库列表失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式...')\r\n        }\r\n\r\n        // 使用模拟数据作为备用方案\r\n        this.loadMockLibraryList()\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案库数据\r\n    loadMockLibraryList() {\r\n      console.log('加载模拟文案库数据')\r\n\r\n      const mockLibraries = [\r\n        {\r\n          id: 1,\r\n          libraryId: 1,\r\n          name: '美食探店文案库',\r\n          libraryName: '美食探店文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '精选美食餐厅，提供各类特色菜品和优质服务',\r\n          prompt: '生成吸引人的美食探店文案，突出菜品特色和用餐体验',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: 150,\r\n          status: 'completed',\r\n          createTime: '2024-01-15 10:30:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 2,\r\n          libraryId: 2,\r\n          name: '时尚服装推广库',\r\n          libraryName: '时尚服装推广库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '时尚服装品牌，主营潮流服饰和配饰',\r\n          prompt: '生成时尚服装推广文案，强调款式新颖和品质优良',\r\n          targetCount: 15,\r\n          generatedCount: 15,\r\n          wordCount: 120,\r\n          status: 'completed',\r\n          createTime: '2024-01-10 14:20:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 3,\r\n          libraryId: 3,\r\n          name: '咖啡厅温馨文案库',\r\n          libraryName: '咖啡厅温馨文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段',\r\n          prompt: '生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: 100,\r\n          status: 'generating',\r\n          createTime: '2024-01-20 09:15:00',\r\n          createBy: 'user'\r\n        }\r\n      ]\r\n\r\n      // 添加用户创建的文案库（如果有的话）\r\n      const userLibraries = this.libraryList.filter(lib => lib.createBy === 'demo')\r\n\r\n      this.libraryList = [...mockLibraries, ...userLibraries]\r\n      this.$message.success('已加载模拟文案库数据（共' + this.libraryList.length + '个文案库）')\r\n    },\r\n    refreshData() {\r\n      console.log('手动刷新数据')\r\n      this.loadLibraryList()\r\n\r\n      // 如果1秒后还没有数据，直接加载模拟数据\r\n      setTimeout(() => {\r\n        if (this.libraryList.length === 0) {\r\n          console.log('刷新后仍无数据，加载模拟数据')\r\n          this.loadMockLibraryList()\r\n        } else {\r\n          this.$message.success('数据已刷新')\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 显示创建文案库对话框\r\n    showCreateLibraryDialog() {\r\n      this.createLibraryDialogVisible = true\r\n      this.createLibraryForm = {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '100'\r\n      }\r\n    },\r\n\r\n    // 使用推荐提示词\r\n    usePrompt(prompt) {\r\n      this.createLibraryForm.prompt = prompt.content\r\n      this.createLibraryDialogVisible = true\r\n      this.$message.success(`已应用${prompt.title}提示词`)\r\n    },\r\n\r\n    // 显示提示词帮助\r\n    showPromptHelp() {\r\n      this.$alert(`\r\n        <h4>AI剪辑文案提示词建议：</h4>\r\n        <p><strong>核心要求：</strong>适合口播，开头用疑问句吸引观众，语言顺口易读</p>\r\n        <br>\r\n        <h5>📝 推荐提示词模板：</h5>\r\n        <p><strong>1. 美食餐饮：</strong>生成适合口播的美食推广文案，开头用疑问句吸引观众，突出食材新鲜和口感层次，语言生动有食欲感，朋友推荐的语气</p>\r\n        <p><strong>2. 生活服务：</strong>生成温馨的生活服务推广文案，开头用疑问句引起共鸣，强调便民和贴心服务，语言亲切自然，像邻居朋友介绍</p>\r\n        <p><strong>3. 时尚美妆：</strong>生成时尚美妆种草文案，开头用疑问句抓住痛点，突出产品效果和使用体验，语言轻松活泼，姐妹分享的感觉</p>\r\n        <p><strong>4. 教育培训：</strong>生成教育培训推广文案，开头用疑问句引发思考，强调学习效果和成长价值，语言专业但不失亲和力</p>\r\n        <p><strong>5. 健康养生：</strong>生成健康养生科普文案，开头用疑问句引起关注，突出健康理念和实用方法，语言通俗易懂，专业可信</p>\r\n        <p><strong>6. 旅游出行：</strong>生成旅游景点推广文案，开头用疑问句激发向往，描述美景和独特体验，语言富有画面感和感染力</p>\r\n        <p><strong>7. 科技数码：</strong>生成数码产品介绍文案，开头用疑问句抓住需求，突出功能特点和使用便利，语言简洁明了，避免过于技术化</p>\r\n        <p><strong>8. 家居生活：</strong>生成家居用品推广文案，开头用疑问句触及生活痛点，强调实用性和生活品质提升，语言温馨贴近生活</p>\r\n        <br>\r\n        <h5>✍️ 编写技巧：</h5>\r\n        <p>• <strong>疑问开头：</strong>用\"你是否想要...\"、\"你知道吗...\"等疑问句开头</p>\r\n        <p>• <strong>顺口易读：</strong>避免拗口词汇，多用短句，适合朗读</p>\r\n        <p>• <strong>朋友语气：</strong>温和亲切，像朋友分享，营销感为0</p>\r\n        <p>• <strong>具体描述：</strong>结合您的店铺特色，越具体越好</p>\r\n      `, 'AI剪辑文案提示词指南', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '知道了',\r\n        customClass: 'prompt-help-dialog'\r\n      })\r\n    },\r\n    // 创建文案库\r\n    createLibrary() {\r\n      this.$refs.createLibraryForm.validate((valid) => {\r\n        if (valid) {\r\n          this.creating = true\r\n\r\n          const libraryData = {\r\n            libraryName: this.createLibraryForm.name,\r\n            useAi: this.createLibraryForm.useAI,\r\n            shopDetails: this.createLibraryForm.shopDetails,\r\n            prompt: this.createLibraryForm.prompt,\r\n            targetCount: this.createLibraryForm.useAI ? this.createLibraryForm.count : 0,\r\n            wordCount: parseInt(this.createLibraryForm.wordCount)\r\n          }\r\n\r\n          addLibrary(libraryData).then(response => {\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n            this.loadLibraryList()\r\n\r\n            // 如果使用AI生成，启动生成任务\r\n            if (this.createLibraryForm.useAI) {\r\n              this.startGeneration(response.data.libraryId)\r\n            }\r\n          }).catch(error => {\r\n            console.error('创建文案库失败，尝试使用测试API', error)\r\n\r\n            // 使用模拟创建方案\r\n            this.$message.warning('正在使用模拟方案创建文案库...')\r\n\r\n            // 模拟创建成功的响应\r\n            const mockLibrary = {\r\n              id: Date.now(),\r\n              libraryId: Date.now(),\r\n              name: libraryData.libraryName,\r\n              libraryName: libraryData.libraryName,\r\n              useAI: libraryData.useAi,\r\n              useAi: libraryData.useAi,\r\n              shopDetails: libraryData.shopDetails,\r\n              prompt: libraryData.prompt,\r\n              targetCount: libraryData.targetCount,\r\n              generatedCount: 0,\r\n              wordCount: libraryData.wordCount,\r\n              status: 'pending',\r\n              createTime: new Date().toLocaleString(),\r\n              createBy: 'demo'\r\n            }\r\n\r\n            // 将模拟数据添加到本地列表中\r\n            this.libraryList.unshift(mockLibrary)\r\n\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n\r\n            // 如果使用AI生成，启动真实的生成流程\r\n            if (this.createLibraryForm.useAI) {\r\n              this.$message.info('正在启动AI文案生成，请稍候...')\r\n\r\n              // 启动生成进度监控\r\n              this.startGenerationMonitoring(mockLibrary.libraryId)\r\n            }\r\n\r\n            this.creating = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 启动AI生成任务\r\n    startGeneration(libraryId) {\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n      if (library) {\r\n        generateCopywriting({\r\n          libraryId: libraryId,\r\n          shopDetails: library.shopDetails,\r\n          prompt: library.prompt,\r\n          count: library.targetCount,\r\n          wordCount: library.wordCount\r\n        }).then(() => {\r\n          this.$message.success('AI文案生成任务已启动')\r\n          this.monitorProgress(libraryId)\r\n        }).catch(error => {\r\n          console.error('启动生成任务失败', error)\r\n          this.$message.error('启动生成任务失败：' + (error.msg || error.message))\r\n        })\r\n      }\r\n    },\r\n\r\n    // 监控生成进度\r\n    monitorProgress(libraryId) {\r\n      const checkProgress = () => {\r\n        getProgress(libraryId).then(response => {\r\n          const progress = response.data\r\n          const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n          if (library) {\r\n            library.generatedCount = progress.generatedCount\r\n            library.status = progress.status\r\n\r\n            if (progress.status === 'generating') {\r\n              setTimeout(checkProgress, 2000) // 每2秒检查一次\r\n            } else if (progress.status === 'completed') {\r\n              this.$message.success(`${library.libraryName} 生成完成！`)\r\n            } else if (progress.status === 'failed') {\r\n              this.$message.error(`${library.libraryName} 生成失败`)\r\n            }\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取进度失败', error)\r\n        })\r\n      }\r\n      checkProgress()\r\n    },\r\n\r\n\r\n\r\n    // 查看文案库\r\n    viewLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.loadLibraryContents(library.id)\r\n      this.viewLibraryDialogVisible = true\r\n    },\r\n\r\n    // 加载文案库内容\r\n    loadLibraryContents(libraryId) {\r\n      // 首先尝试从持久化存储中加载\r\n      if (this.libraryContentStorage && this.libraryContentStorage[libraryId]) {\r\n        this.libraryContents = this.libraryContentStorage[libraryId]\r\n        this.$message.success(`已加载${this.libraryContents.length}条文案内容`)\r\n        console.log('从持久化存储加载文案内容:', this.libraryContents)\r\n        return\r\n      }\r\n\r\n      // 如果持久化存储中没有，尝试API\r\n      listContent(libraryId).then(response => {\r\n        this.libraryContents = response.rows || response.data || []\r\n        if (this.libraryContents.length === 0) {\r\n          // 如果没有内容，加载模拟内容\r\n          this.loadMockContents(libraryId)\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库内容失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式加载内容...')\r\n        }\r\n\r\n        // 加载模拟内容\r\n        this.loadMockContents(libraryId)\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案内容\r\n    loadMockContents(libraryId) {\r\n      console.log('加载模拟文案内容，libraryId:', libraryId)\r\n\r\n      const mockContents = {\r\n        1: [ // 美食探店文案库\r\n          {\r\n            id: 1,\r\n            contentId: 1,\r\n            libraryId: 1,\r\n            content: '🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高，强烈推荐给爱美食的朋友们！',\r\n            title: 'AI生成-美食探店文案1',\r\n            wordCount: 98,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-15 11:00:00'\r\n          },\r\n          {\r\n            id: 2,\r\n            contentId: 2,\r\n            libraryId: 1,\r\n            content: '🌟 又一家宝藏餐厅被我发现了！环境温馨雅致，服务贴心周到，最重要的是菜品真的太棒了！特色烤鱼鲜嫩多汁，秘制酱料层次丰富，每一口都是享受。和朋友聚餐的完美选择，记得提前预约哦！',\r\n            title: 'AI生成-美食探店文案2',\r\n            wordCount: 85,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-15 11:15:00'\r\n          }\r\n        ],\r\n        2: [ // 时尚服装推广库\r\n          {\r\n            id: 3,\r\n            contentId: 3,\r\n            libraryId: 2,\r\n            content: '✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭。现在购买还有限时优惠，不要错过哦！',\r\n            title: 'AI生成-时尚服装文案1',\r\n            wordCount: 92,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 90,\r\n            createTime: '2024-01-10 15:00:00'\r\n          }\r\n        ],\r\n        3: [ // 咖啡厅温馨文案库\r\n          {\r\n            id: 4,\r\n            contentId: 4,\r\n            libraryId: 3,\r\n            content: '☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间。快来体验我们的温馨服务吧！',\r\n            title: 'AI生成-咖啡厅文案1',\r\n            wordCount: 78,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-20 10:00:00'\r\n          },\r\n          {\r\n            id: 5,\r\n            contentId: 5,\r\n            libraryId: 3,\r\n            content: '🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱，每一刻都值得珍藏。来这里，让心灵得到片刻的宁静！',\r\n            title: 'AI生成-咖啡厅文案2',\r\n            wordCount: 71,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-20 10:30:00'\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.libraryContents = mockContents[libraryId] || []\r\n      this.$message.success(`已加载${this.libraryContents.length}条模拟文案内容`)\r\n    },\r\n\r\n    // 启动生成进度监控\r\n    startGenerationMonitoring(libraryId) {\r\n      console.log('启动生成进度监控，libraryId:', libraryId)\r\n\r\n      // 查找对应的文案库\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId || lib.id === libraryId)\r\n      if (!library) {\r\n        console.log('未找到文案库，停止监控')\r\n        return\r\n      }\r\n\r\n      // 初始化文案库的内容存储\r\n      if (!this.libraryContentStorage) {\r\n        this.libraryContentStorage = {}\r\n      }\r\n      if (!this.libraryContentStorage[libraryId]) {\r\n        this.libraryContentStorage[libraryId] = []\r\n      }\r\n\r\n      library.status = 'generating'\r\n\r\n      // 生成所有文案\r\n      const generateAllContent = () => {\r\n        for (let i = 1; i <= library.targetCount; i++) {\r\n          setTimeout(() => {\r\n            // 生成文案内容\r\n            const newContent = this.generateMockContent(library, i)\r\n\r\n            // 存储到持久化存储中\r\n            this.libraryContentStorage[libraryId].push(newContent)\r\n\r\n            // 更新生成数量\r\n            library.generatedCount = i\r\n\r\n            this.$message.success(`文案库\"${library.libraryName || library.name}\"已生成第${i}条文案`)\r\n\r\n            // 如果是最后一条，标记完成\r\n            if (i === library.targetCount) {\r\n              library.status = 'completed'\r\n              this.$message.success(`🎉 文案库\"${library.libraryName || library.name}\"生成完成！共生成${library.generatedCount}条文案`)\r\n\r\n              // 保存到localStorage\r\n              this.saveLibraryContentToStorage()\r\n            }\r\n          }, i * 2000) // 每2秒生成一条\r\n        }\r\n      }\r\n\r\n      // 开始生成\r\n      setTimeout(generateAllContent, 1000)\r\n    },\r\n\r\n    // 生成模拟文案内容（AI剪辑文案专用）\r\n    generateMockContent(library, index) {\r\n      const targetWordCount = library.wordCount || 200\r\n\r\n      // 固定使用AI剪辑文案（video）的生成策略\r\n      return this.generateVideoContent(library, index, targetWordCount)\r\n    },\r\n\r\n    // 根据平台生成专属文案\r\n    generatePlatformSpecificContent(platform, library, index, targetWordCount) {\r\n      switch (platform) {\r\n        case 'video': // AI剪辑文案（口播）\r\n          return this.generateVideoContent(library, index, targetWordCount)\r\n        case 'douyin': // 抖音/快手文案\r\n        case 'kuaishou':\r\n          return this.generateShortVideoContent(library, index, targetWordCount)\r\n        case 'xiaohongshu': // 小红书文案\r\n          return this.generateXiaohongshuContent(library, index, targetWordCount)\r\n        case 'review': // 点评/朋友圈文案\r\n        case 'moments':\r\n          return this.generateReviewContent(library, index, targetWordCount)\r\n        default: // 通用文案\r\n          return this.generateGeneralContent(library, index, targetWordCount)\r\n      }\r\n    },\r\n\r\n    // AI剪辑文案（口播专用）\r\n    generateVideoContent(library, index, targetWordCount) {\r\n      const questionStarters = [\r\n        '你是否想要',\r\n        '你有没有遇到过',\r\n        '你知道吗',\r\n        '你还在为',\r\n        '你想不想',\r\n        '你有没有发现',\r\n        '你是不是也',\r\n        '你有没有想过'\r\n      ]\r\n\r\n      const videoFragments = [\r\n        `${library.shopDetails || '我们'}专注于为您提供最优质的服务。`,\r\n        `这里不仅仅是一个地方，更是一种生活方式的体现。`,\r\n        `我们用心做好每一个细节，只为给您带来最好的体验。`,\r\n        `选择我们，就是选择品质和信赖。`,\r\n        `在这里，您会发现不一样的精彩。`\r\n      ]\r\n\r\n      const questionStart = questionStarters[Math.floor(Math.random() * questionStarters.length)]\r\n      let content = `${questionStart}${library.prompt || '体验不一样的服务'}？`\r\n\r\n      // 添加内容片段直到达到目标字数\r\n      while (content.length < targetWordCount - 30) {\r\n        const fragment = videoFragments[Math.floor(Math.random() * videoFragments.length)]\r\n        content += fragment\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '口播文案')\r\n    },\r\n\r\n    // 抖音/快手文案（简短有力）\r\n    generateShortVideoContent(library, index, targetWordCount) {\r\n      const hotTrends = ['yyds', '绝绝子', '太香了', '爱了爱了', '这谁顶得住', '直接拿下', '必须安排']\r\n      const shortFragments = [\r\n        `${library.shopDetails || '这家店'}真的${hotTrends[Math.floor(Math.random() * hotTrends.length)]}！`,\r\n        `姐妹们，这个必须冲！`,\r\n        `不是我吹，这个真的很棒！`,\r\n        `这个宝藏店铺终于被我发现了！`,\r\n        `朋友们，这波不亏！`\r\n      ]\r\n\r\n      let content = shortFragments[index % shortFragments.length]\r\n\r\n      // 保持简短，适合短视频\r\n      const maxLength = Math.min(targetWordCount, 80)\r\n      if (content.length < maxLength - 20) {\r\n        content += `${library.prompt || '真的值得一试'}，快去体验吧！`\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '短视频文案')\r\n    },\r\n\r\n    // 小红书文案（分段+emoji丰富）\r\n    generateXiaohongshuContent(library, index, targetWordCount) {\r\n      const emojis = ['✨', '💕', '🌟', '💖', '🎀', '🌸', '💫', '🦋', '🌺', '💐', '🎨', '🌈', '💎', '🎪', '🎭']\r\n      const xiaohongshuStarters = [\r\n        '姐妹们！今天要分享一个宝藏',\r\n        '真的不是我吹',\r\n        '这个真的太好了',\r\n        '终于找到了',\r\n        '姐妹们看过来'\r\n      ]\r\n\r\n      let content = `${xiaohongshuStarters[index % xiaohongshuStarters.length]}${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n      content += `${library.shopDetails || '这个地方'}真的让我惊喜${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n\r\n      // 添加分段内容\r\n      const segments = [\r\n        `环境超级棒${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `服务态度也很好${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `性价比真的很高${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `强烈推荐给大家${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n      ]\r\n\r\n      segments.forEach(segment => {\r\n        content += `${segment}\\n`\r\n      })\r\n\r\n      content += `\\n${library.prompt || '真的值得一试'}${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n\r\n      return this.createContentObject(library, index, content, '小红书文案')\r\n    },\r\n\r\n    // 点评/朋友圈文案（接地气+适当错别字）\r\n    generateReviewContent(library, index, targetWordCount) {\r\n      const casualWords = ['挺不错的', '还行', '蛮好的', '可以的', '不错不错']\r\n      const typos = {\r\n        '的': '滴',\r\n        '这个': '这个',\r\n        '真的': '真滴',\r\n        '好吃': '好次',\r\n        '喜欢': '稀饭'\r\n      }\r\n\r\n      let content = `今天和朋友去了${library.shopDetails || '这家店'}，${casualWords[index % casualWords.length]}。`\r\n      content += `环境还可以，服务态度也挺好滴。`\r\n      content += `${library.prompt || '总体来说还是值得推荐滴'}，下次还会再来。`\r\n\r\n      // 随机添加一些错别字\r\n      Object.keys(typos).forEach(key => {\r\n        if (Math.random() < 0.3) { // 30%概率替换\r\n          content = content.replace(key, typos[key])\r\n        }\r\n      })\r\n\r\n      return this.createContentObject(library, index, content, '点评文案')\r\n    },\r\n\r\n    // 通用文案生成\r\n    generateGeneralContent(library, index, targetWordCount) {\r\n      const baseFragments = [\r\n        `🌟 ${library.shopDetails || '我们的店铺'}，为您带来独特的体验！`,\r\n        `💫 发现美好，从这里开始！${library.shopDetails || '我们的店铺'}，期待您的光临！`,\r\n        `✨ 品质生活，精彩每一天！来体验我们为您精心准备的服务吧！`\r\n      ]\r\n\r\n      let content = baseFragments[index % baseFragments.length]\r\n\r\n      // 根据目标字数扩展内容\r\n      while (content.length < targetWordCount - 30) {\r\n        content += `我们专注于${library.prompt || '为您提供优质服务'}，用心做好每一个细节。`\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '通用文案')\r\n    },\r\n\r\n    // 创建文案内容对象\r\n    createContentObject(library, index, content, type) {\r\n      const newContent = {\r\n        id: Date.now() + index,\r\n        contentId: Date.now() + index,\r\n        libraryId: library.libraryId || library.id,\r\n        content: content,\r\n        title: `AI生成-${type}-第${index}条`,\r\n        wordCount: content.length,\r\n        isAiGenerated: true,\r\n        status: 'active',\r\n        qualityScore: 85 + Math.floor(Math.random() * 15),\r\n        createTime: new Date().toLocaleString()\r\n      }\r\n\r\n      console.log(`生成第${index}条${type} (实际${content.length}字):`, newContent)\r\n      return newContent\r\n    },\r\n\r\n    // 保存文案库内容到localStorage\r\n    saveLibraryContentToStorage() {\r\n      try {\r\n        localStorage.setItem('libraryContentStorage', JSON.stringify(this.libraryContentStorage || {}))\r\n        console.log('文案库内容已保存到localStorage')\r\n      } catch (error) {\r\n        console.error('保存文案库内容失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从localStorage加载文案库内容\r\n    loadLibraryContentFromStorage() {\r\n      try {\r\n        const stored = localStorage.getItem('libraryContentStorage')\r\n        if (stored) {\r\n          this.libraryContentStorage = JSON.parse(stored)\r\n          console.log('从localStorage加载文案库内容:', this.libraryContentStorage)\r\n        } else {\r\n          this.libraryContentStorage = {}\r\n        }\r\n      } catch (error) {\r\n        console.error('加载文案库内容失败:', error)\r\n        this.libraryContentStorage = {}\r\n      }\r\n    },\r\n    // 新增文案到文案库\r\n    addToLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.addCopywritingForm = {\r\n        useAI: true,\r\n        shopDetails: library.shopDetails || '',\r\n        prompt: library.prompt || '',\r\n        count: 5,\r\n        content: ''\r\n      }\r\n      this.addCopywritingDialogVisible = true\r\n    },\r\n\r\n    // 添加文案\r\n    addCopywriting() {\r\n      this.$refs.addCopywritingForm.validate((valid) => {\r\n        if (valid) {\r\n          this.adding = true\r\n\r\n          const contentData = {\r\n            libraryId: this.currentLibrary.libraryId,\r\n            useAi: this.addCopywritingForm.useAI,\r\n            shopDetails: this.addCopywritingForm.shopDetails,\r\n            prompt: this.addCopywritingForm.prompt,\r\n            count: this.addCopywritingForm.count,\r\n            content: this.addCopywritingForm.content\r\n          }\r\n\r\n          addContent(contentData).then(response => {\r\n            this.$message.success(this.addCopywritingForm.useAI ?\r\n              `成功生成${this.addCopywritingForm.count}条文案` : '文案添加成功')\r\n            this.addCopywritingDialogVisible = false\r\n            this.loadLibraryContents(this.currentLibrary.libraryId)\r\n\r\n            // 更新文案库的生成计数\r\n            this.currentLibrary.generatedCount += this.addCopywritingForm.useAI ?\r\n              this.addCopywritingForm.count : 1\r\n          }).catch(error => {\r\n            console.error('添加文案失败', error)\r\n            this.$message.error('添加文案失败：' + (error.msg || error.message))\r\n          }).finally(() => {\r\n            this.adding = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新生成文案库\r\n    regenerateLibrary(library) {\r\n      this.$confirm('确定要重新生成这个文案库吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        regenerateLibrary(library.libraryId).then(() => {\r\n          library.status = 'generating'\r\n          library.generatedCount = 0\r\n          this.$message.success('开始重新生成文案库')\r\n          this.monitorProgress(library.libraryId)\r\n        }).catch(error => {\r\n          console.error('重新生成失败', error)\r\n          this.$message.error('重新生成失败：' + (error.msg || error.message))\r\n        })\r\n      })\r\n    },\r\n\r\n    // 删除文案库\r\n    deleteLibrary(library) {\r\n      this.$confirm('确定要删除这个文案库吗？删除后无法恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delLibrary([library.libraryId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryList()\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 查看文案内容\r\n    viewContent(content) {\r\n      this.$alert(content.content, '文案内容', {\r\n        confirmButtonText: '关闭'\r\n      })\r\n    },\r\n\r\n    // 复制文案内容\r\n    copyContent(content) {\r\n      navigator.clipboard.writeText(content.content).then(() => {\r\n        this.$message.success('文案已复制到剪贴板')\r\n      }).catch(() => {\r\n        this.$message.error('复制失败，请手动复制')\r\n      })\r\n    },\r\n\r\n    // 编辑文案内容\r\n    editContent(content) {\r\n      this.$prompt('请编辑文案内容', '编辑文案', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputValue: content.content\r\n      }).then(({ value }) => {\r\n        const updateData = {\r\n          contentId: content.contentId,\r\n          content: value,\r\n          wordCount: value.length\r\n        }\r\n\r\n        updateContent(updateData).then(() => {\r\n          content.content = value\r\n          content.wordCount = value.length\r\n          this.$message.success('编辑成功')\r\n        }).catch(error => {\r\n          console.error('编辑失败', error)\r\n          this.$message.error('编辑失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消编辑')\r\n      })\r\n    },\r\n\r\n    // 删除文案内容\r\n    deleteContent(content) {\r\n      this.$confirm('确定要删除这条文案吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delContent([content.contentId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryContents(this.currentLibrary.libraryId)\r\n          this.currentLibrary.generatedCount--\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 导出文案库\r\n    exportLibrary(library) {\r\n      let content = `文案库：${library.name}\\n`\r\n      content += `创建时间：${library.createTime}\\n`\r\n      content += `总计：${this.libraryContents.length}条文案\\n\\n`\r\n\r\n      this.libraryContents.forEach((item, index) => {\r\n        content += `${index + 1}. ${item.content}\\n`\r\n        content += `   创建时间：${item.createTime}\\n\\n`\r\n      })\r\n\r\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `${library.name}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n\r\n      this.$message.success('文案库导出成功')\r\n    },\r\n    // 获取状态名称\r\n    getStatusName(status) {\r\n      const statusMap = {\r\n        pending: '未开始',\r\n        generating: '生成中',\r\n        completed: '已完成',\r\n        failed: '生成失败'\r\n      }\r\n      return statusMap[status] || status\r\n    },\r\n\r\n    // 获取状态颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        pending: 'info',\r\n        generating: 'warning',\r\n        completed: 'success',\r\n        failed: 'danger'\r\n      }\r\n      return colorMap[status] || ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.shipin-container {\r\n  padding: 24px;\r\n  background: #f5f5f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .header-content {\r\n    .page-title {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 8px 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .page-description {\r\n      color: #7f8c8d;\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.prompt-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #e6a23c;\r\n    }\r\n  }\r\n\r\n  .prompt-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 16px;\r\n\r\n    .prompt-card {\r\n      padding: 20px;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .prompt-icon {\r\n        font-size: 32px;\r\n        margin-bottom: 12px;\r\n        text-align: center;\r\n      }\r\n\r\n      .prompt-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .prompt-desc {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .prompt-preview {\r\n        font-size: 12px;\r\n        color: #95a5a6;\r\n        line-height: 1.4;\r\n        background: #f8f9fa;\r\n        padding: 8px;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.template-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #409eff;\r\n    }\r\n  }\r\n\r\n  .template-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 16px;\r\n\r\n    .template-card {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .template-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .template-type {\r\n          background: #f0f0f0;\r\n          color: #666;\r\n          padding: 4px 12px;\r\n          border-radius: 16px;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .template-hot {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .template-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .template-preview {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        line-height: 1.5;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .template-stats {\r\n        display: flex;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .section-filters {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .library-list {\r\n    .library-item {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin-bottom: 16px;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .item-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .item-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          i {\r\n            margin-right: 8px;\r\n            color: #409eff;\r\n          }\r\n        }\r\n\r\n        .item-meta {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 12px;\r\n\r\n          .item-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-content {\r\n        margin-bottom: 16px;\r\n\r\n        .library-info {\r\n          display: flex;\r\n          gap: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .info-item {\r\n            .label {\r\n              font-size: 12px;\r\n              color: #7f8c8d;\r\n            }\r\n\r\n            .value {\r\n              font-size: 14px;\r\n              color: #2c3e50;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n\r\n        .progress-info {\r\n          margin-bottom: 12px;\r\n\r\n          .progress-text {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n            margin-top: 8px;\r\n          }\r\n        }\r\n\r\n        .shop-info {\r\n          font-size: 14px;\r\n          color: #7f8c8d;\r\n\r\n          .label {\r\n            font-weight: 600;\r\n          }\r\n\r\n          .preview {\r\n            color: #95a5a6;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n        flex-wrap: wrap;\r\n      }\r\n    }\r\n\r\n    .empty-state {\r\n      text-align: center;\r\n      padding: 60px 20px;\r\n\r\n      i {\r\n        font-size: 64px;\r\n        color: #ddd;\r\n        margin-bottom: 16px;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 18px;\r\n        color: #7f8c8d;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      p {\r\n        color: #95a5a6;\r\n        margin: 0 0 20px 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-detail {\r\n  .detail-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 12px 0;\r\n    }\r\n\r\n    .detail-meta {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      font-size: 14px;\r\n      color: #7f8c8d;\r\n    }\r\n  }\r\n\r\n  .detail-info {\r\n    margin-bottom: 24px;\r\n\r\n    .info-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\r\n      gap: 16px;\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        .label {\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n          display: block;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #2c3e50;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n\r\n    .shop-details,\r\n    .prompt-info {\r\n      margin-bottom: 16px;\r\n\r\n      h4 {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      .details-text,\r\n      .prompt-text {\r\n        line-height: 1.6;\r\n        color: #2c3e50;\r\n        background: #f8f9fa;\r\n        padding: 12px;\r\n        border-radius: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .copywriting-list {\r\n    .list-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      padding-bottom: 12px;\r\n      border-bottom: 1px solid #e9ecef;\r\n\r\n      h4 {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0;\r\n      }\r\n\r\n      .list-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .content-list {\r\n      max-height: 400px;\r\n      overflow-y: auto;\r\n\r\n      .content-item {\r\n        border: 1px solid #e9ecef;\r\n        border-radius: 6px;\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #409eff;\r\n          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);\r\n        }\r\n\r\n        .content-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .content-index {\r\n            background: #409eff;\r\n            color: #fff;\r\n            padding: 2px 8px;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            min-width: 30px;\r\n            text-align: center;\r\n          }\r\n\r\n          .content-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n\r\n          .content-actions {\r\n            display: flex;\r\n            gap: 4px;\r\n          }\r\n        }\r\n\r\n        .content-text {\r\n          line-height: 1.6;\r\n          color: #2c3e50;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n\r\n      .empty-content {\r\n        text-align: center;\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          color: #ddd;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        p {\r\n          color: #7f8c8d;\r\n          margin: 0 0 16px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n  line-height: 1.4;\r\n}\r\n\r\n// 移动端优化样式\r\n@media (max-width: 768px) {\r\n  .shipin-container {\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    .header-content {\r\n      width: 100%;\r\n      margin-bottom: 12px;\r\n\r\n      .page-title {\r\n        font-size: 20px;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .page-description {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      width: 100%;\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .el-button {\r\n        flex: 1;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .prompt-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .prompt-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .prompt-card {\r\n        padding: 16px;\r\n\r\n        .prompt-icon {\r\n          font-size: 24px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-title {\r\n          font-size: 14px;\r\n          margin-bottom: 6px;\r\n        }\r\n\r\n        .prompt-desc {\r\n          font-size: 12px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-preview {\r\n          font-size: 11px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .template-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .template-grid {\r\n      grid-template-columns: 1fr;\r\n      gap: 12px;\r\n\r\n      .template-card {\r\n        padding: 16px;\r\n\r\n        .template-title {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .template-preview {\r\n          font-size: 13px;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 2;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-section {\r\n    padding: 16px;\r\n\r\n    .section-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 16px;\r\n\r\n      h3 {\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .section-filters {\r\n        width: 100%;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n\r\n        .el-select {\r\n          width: 100% !important;\r\n        }\r\n\r\n        .el-input {\r\n          width: 100% !important;\r\n          margin-left: 0 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .library-list {\r\n      .library-item {\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n\r\n        .item-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          margin-bottom: 12px;\r\n\r\n          .item-title {\r\n            font-size: 15px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .item-meta {\r\n            width: 100%;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .item-time {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-content {\r\n          .library-info {\r\n            flex-direction: column;\r\n            gap: 8px;\r\n\r\n            .info-item {\r\n              display: flex;\r\n              justify-content: space-between;\r\n\r\n              .label {\r\n                font-size: 12px;\r\n              }\r\n\r\n              .value {\r\n                font-size: 13px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .shop-info {\r\n            font-size: 13px;\r\n\r\n            .preview {\r\n              display: block;\r\n              margin-top: 4px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-actions {\r\n          gap: 6px;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n            font-size: 12px;\r\n            padding: 6px 8px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .empty-state {\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n        }\r\n\r\n        h3 {\r\n          font-size: 16px;\r\n        }\r\n\r\n        p {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 对话框移动端优化\r\n  .create-dialog,\r\n  .view-dialog {\r\n    .el-dialog__body {\r\n      padding: 16px;\r\n      max-height: calc(100vh - 120px);\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .el-form {\r\n      .el-form-item {\r\n        margin-bottom: 16px;\r\n\r\n        .el-form-item__label {\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-textarea {\r\n          font-size: 14px;\r\n        }\r\n\r\n        .el-checkbox-group {\r\n          .el-checkbox {\r\n            margin-bottom: 8px;\r\n            margin-right: 16px;\r\n\r\n            .el-checkbox__label {\r\n              font-size: 14px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-dialog__footer {\r\n      padding: 12px 16px;\r\n\r\n      .el-button {\r\n        margin-left: 8px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-header {\r\n      h3 {\r\n        font-size: 18px;\r\n      }\r\n\r\n      .detail-meta {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .detail-info {\r\n      .info-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 12px;\r\n\r\n        .info-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 8px 12px;\r\n          background: #f8f9fa;\r\n          border-radius: 4px;\r\n\r\n          .label {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .value {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .shop-details,\r\n      .prompt-info {\r\n        .details-text,\r\n        .prompt-text {\r\n          font-size: 13px;\r\n          padding: 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .list-header {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n\r\n        h4 {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .list-actions {\r\n          width: 100%;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n\r\n      .content-list {\r\n        max-height: 300px;\r\n\r\n        .content-item {\r\n          padding: 12px;\r\n\r\n          .content-header {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n\r\n            .content-actions {\r\n              width: 100%;\r\n              justify-content: space-between;\r\n\r\n              .el-button {\r\n                flex: 1;\r\n                margin: 0 2px;\r\n                font-size: 11px;\r\n                padding: 4px 6px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .content-text {\r\n            font-size: 13px;\r\n            line-height: 1.5;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 超小屏幕优化 (小于480px)\r\n@media (max-width: 480px) {\r\n  .shipin-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .prompt-section {\r\n    .prompt-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-info {\r\n      .info-grid {\r\n        .info-item {\r\n          padding: 6px 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .content-list {\r\n        .content-item {\r\n          padding: 10px;\r\n\r\n          .content-header {\r\n            .content-actions {\r\n              .el-button {\r\n                font-size: 10px;\r\n                padding: 3px 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 提示词帮助对话框样式\r\n::v-deep .prompt-help-dialog {\r\n  .el-message-box {\r\n    width: 600px;\r\n    max-width: 90vw;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  h4, h5 {\r\n    color: #409EFF;\r\n    margin: 15px 0 10px 0;\r\n  }\r\n\r\n  p {\r\n    margin: 8px 0;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  strong {\r\n    color: #303133;\r\n  }\r\n}\r\n</style>"]}]}