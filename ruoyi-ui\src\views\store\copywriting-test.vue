<template>
  <div class="app-container">
    <div class="test-header">
      <h2>AI文案库测试页面</h2>
      <p>此页面用于测试AI文案库功能，无需登录权限</p>
    </div>

    <!-- 测试按钮区域 -->
    <div class="test-actions">
      <el-button type="primary" @click="testHealth" :loading="testing.health">
        健康检查
      </el-button>
      <el-button type="success" @click="testDeepSeek" :loading="testing.deepseek">
        测试DeepSeek-V3
      </el-button>
      <el-button type="info" @click="loadLibraryList" :loading="testing.list">
        加载文案库列表
      </el-button>
      <el-button type="warning" @click="showCreateDialog">
        创建测试文案库
      </el-button>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <el-card v-if="testResult" class="result-card">
        <div slot="header">
          <span>测试结果</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="testResult = null">清除</el-button>
        </div>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </el-card>
    </div>

    <!-- 文案库列表 -->
    <div class="library-list" v-if="libraryList.length > 0">
      <h3>文案库列表</h3>
      <el-table :data="libraryList" border>
        <el-table-column prop="libraryId" label="ID" width="80"></el-table-column>
        <el-table-column prop="libraryName" label="文案库名称"></el-table-column>
        <el-table-column prop="useAi" label="使用AI">
          <template slot-scope="scope">
            <el-tag :type="scope.row.useAi ? 'success' : 'info'">
              {{ scope.row.useAi ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetCount" label="目标条数" width="100"></el-table-column>
        <el-table-column prop="generatedCount" label="已生成" width="100"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
      </el-table>
    </div>

    <!-- 创建文案库对话框 -->
    <el-dialog
      title="创建测试文案库"
      :visible.sync="createDialogVisible"
      width="600px"
    >
      <el-form :model="createForm" :rules="createRules" ref="createForm" label-width="120px">
        <el-form-item label="文案库名称" prop="libraryName">
          <el-input v-model="createForm.libraryName" placeholder="请输入文案库名称"></el-input>
        </el-form-item>
        
        <el-form-item label="是否使用AI" prop="useAi">
          <el-switch v-model="createForm.useAi" active-text="使用AI" inactive-text="手动创建"></el-switch>
        </el-form-item>

        <template v-if="createForm.useAi">
          <el-form-item label="店铺详情" prop="shopDetails">
            <el-input
              v-model="createForm.shopDetails"
              type="textarea"
              :rows="3"
              placeholder="请描述您的店铺信息"
            ></el-input>
          </el-form-item>

          <el-form-item label="AI提示词" prop="prompt">
            <el-input
              v-model="createForm.prompt"
              type="textarea"
              :rows="2"
              placeholder="请输入AI生成提示词"
            ></el-input>
          </el-form-item>

          <el-form-item label="生成条数" prop="targetCount">
            <el-input-number v-model="createForm.targetCount" :min="1" :max="50"></el-input-number>
          </el-form-item>

          <el-form-item label="文案字数" prop="wordCount">
            <el-select v-model="createForm.wordCount" placeholder="选择字数">
              <el-option label="50字以内" :value="50"></el-option>
              <el-option label="100字左右" :value="100"></el-option>
              <el-option label="200字左右" :value="200"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      
      <div slot="footer">
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createLibrary" :loading="creating">
          {{ creating ? '创建中...' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLibraryTest,
  addLibraryTest,
  testDeepSeekDirect,
  testGenerate,
  healthCheck
} from '@/api/ai/copywriting-test'

export default {
  name: 'CopywritingTest',
  data() {
    return {
      // 测试状态
      testing: {
        health: false,
        deepseek: false,
        list: false
      },
      
      // 数据
      testResult: null,
      libraryList: [],
      
      // 对话框
      createDialogVisible: false,
      creating: false,
      
      // 表单
      createForm: {
        libraryName: '',
        useAi: true,
        shopDetails: '',
        prompt: '',
        targetCount: 5,
        wordCount: 100
      },
      
      // 验证规则
      createRules: {
        libraryName: [
          { required: true, message: '请输入文案库名称', trigger: 'blur' }
        ]
      }
    }
  },
  
  mounted() {
    this.testHealth()
  },
  
  methods: {
    // 健康检查
    async testHealth() {
      this.testing.health = true
      try {
        const response = await healthCheck()
        this.testResult = response
        this.$message.success('健康检查通过')
      } catch (error) {
        this.testResult = error
        this.$message.error('健康检查失败')
      } finally {
        this.testing.health = false
      }
    },
    
    // 测试DeepSeek-V3
    async testDeepSeek() {
      this.testing.deepseek = true
      try {
        const response = await testDeepSeekDirect()
        this.testResult = response
        this.$message.success('DeepSeek-V3测试成功')
      } catch (error) {
        this.testResult = error
        this.$message.error('DeepSeek-V3测试失败')
      } finally {
        this.testing.deepseek = false
      }
    },
    
    // 加载文案库列表
    async loadLibraryList() {
      this.testing.list = true
      try {
        const response = await listLibraryTest()
        this.libraryList = response.data || []
        this.testResult = response
        this.$message.success('文案库列表加载成功')
      } catch (error) {
        this.testResult = error
        this.$message.error('文案库列表加载失败')
      } finally {
        this.testing.list = false
      }
    },
    
    // 显示创建对话框
    showCreateDialog() {
      this.createDialogVisible = true
      this.createForm = {
        libraryName: '测试文案库_' + Date.now(),
        useAi: true,
        shopDetails: '测试商户：一家温馨的咖啡厅，主营手工咖啡和精致甜点',
        prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感',
        targetCount: 5,
        wordCount: 100
      }
    },
    
    // 创建文案库
    async createLibrary() {
      this.$refs.createForm.validate(async (valid) => {
        if (valid) {
          this.creating = true
          try {
            const response = await addLibraryTest(this.createForm)
            this.testResult = response
            this.$message.success('文案库创建成功')
            this.createDialogVisible = false
            this.loadLibraryList()
          } catch (error) {
            this.testResult = error
            this.$message.error('文案库创建失败：' + (error.msg || error.message))
          } finally {
            this.creating = false
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.test-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.test-actions {
  margin-bottom: 20px;
  text-align: center;
}

.test-actions .el-button {
  margin: 0 10px;
}

.test-results {
  margin-bottom: 20px;
}

.result-card pre {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

.library-list {
  margin-top: 20px;
}
</style>
