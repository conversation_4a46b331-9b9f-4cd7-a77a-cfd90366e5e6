{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\hong-new.vue?vue&type=template&id=113e34db", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\hong-new.vue", "mtime": 1754556842269}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}