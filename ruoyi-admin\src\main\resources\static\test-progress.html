<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 进度条测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .progress-test {
            margin: 20px 0;
            padding: 20px;
            border: 3px solid #007bff;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .progress-test h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        /* 超级明显的进度条 */
        .super-visible-progress {
            width: 100%;
            height: 40px;
            background: #e9ecef;
            border: 3px solid #007bff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            margin: 15px 0;
        }
        
        .super-visible-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 60%;
            transition: width 0.5s ease;
            position: relative;
        }
        
        .super-visible-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        /* 红色醒目进度条 */
        .red-progress {
            width: 100%;
            height: 50px;
            background: #ffebee;
            border: 4px solid #f44336;
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            margin: 15px 0;
        }
        
        .red-fill {
            height: 100%;
            background: linear-gradient(90deg, #f44336, #d32f2f);
            width: 75%;
            transition: width 0.5s ease;
            position: relative;
        }
        
        .red-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        }
        
        /* 绿色成功进度条 */
        .green-progress {
            width: 100%;
            height: 35px;
            background: #e8f5e8;
            border: 3px solid #4caf50;
            border-radius: 17px;
            overflow: hidden;
            position: relative;
            margin: 15px 0;
        }
        
        .green-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #388e3c);
            width: 90%;
            transition: width 0.5s ease;
            position: relative;
            animation: pulse 2s infinite;
        }
        
        .green-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 进度条可见性测试页面</h1>
        
        <div class="status">
            <strong>📍 当前位置：</strong> 这是独立的进度条测试页面<br>
            <strong>🌐 访问地址：</strong> http://localhost:8078/test-progress.html<br>
            <strong>💡 说明：</strong> 如果您能看到下面的进度条，说明显示功能正常
        </div>
        
        <div class="progress-test">
            <h3>🔵 蓝色进度条 (60%)</h3>
            <div class="super-visible-progress">
                <div class="super-visible-fill">
                    <div class="super-visible-text">60% - 蓝色进度条测试</div>
                </div>
            </div>
        </div>
        
        <div class="progress-test">
            <h3>🔴 红色进度条 (75%)</h3>
            <div class="red-progress">
                <div class="red-fill">
                    <div class="red-text">75% - 红色进度条测试</div>
                </div>
            </div>
        </div>
        
        <div class="progress-test">
            <h3>🟢 绿色进度条 (90%) - 带动画</h3>
            <div class="green-progress">
                <div class="green-fill">
                    <div class="green-text">90% - 绿色进度条测试</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="testAnimation()">🎬 测试动画效果</button>
            <button class="btn" onclick="goToUpdatePage()">🚀 跳转到更新页面</button>
        </div>
        
        <div class="status">
            <strong>✅ 如果您能看到上面的彩色进度条：</strong> 说明浏览器支持进度条显示<br>
            <strong>❌ 如果看不到进度条：</strong> 可能是浏览器兼容性问题或CSS被禁用<br>
            <strong>🔧 下一步：</strong> 点击"跳转到更新页面"按钮查看实际的更新进度条
        </div>
    </div>
    
    <script>
        function testAnimation() {
            console.log('开始测试动画效果');
            
            const fills = [
                document.querySelector('.super-visible-fill'),
                document.querySelector('.red-fill'),
                document.querySelector('.green-fill')
            ];
            
            const texts = [
                document.querySelector('.super-visible-text'),
                document.querySelector('.red-text'),
                document.querySelector('.green-text')
            ];
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                
                fills.forEach((fill, index) => {
                    if (fill) {
                        fill.style.width = progress + '%';
                        if (texts[index]) {
                            texts[index].textContent = progress + '% - 动画测试中...';
                        }
                    }
                });
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        // 重置
                        fills[0].style.width = '60%';
                        fills[1].style.width = '75%';
                        fills[2].style.width = '90%';
                        texts[0].textContent = '60% - 蓝色进度条测试';
                        texts[1].textContent = '75% - 红色进度条测试';
                        texts[2].textContent = '90% - 绿色进度条测试';
                    }, 1000);
                }
            }, 200);
        }
        
        function goToUpdatePage() {
            window.open('/index.html', '_blank');
        }
        
        // 页面加载完成后的检查
        window.onload = function() {
            console.log('进度条测试页面加载完成');
            
            // 检查是否所有进度条都可见
            const progressBars = document.querySelectorAll('.super-visible-progress, .red-progress, .green-progress');
            console.log('找到进度条数量:', progressBars.length);
            
            progressBars.forEach((bar, index) => {
                const rect = bar.getBoundingClientRect();
                console.log(`进度条 ${index + 1} 位置:`, rect);
                
                if (rect.height === 0 || rect.width === 0) {
                    console.error(`进度条 ${index + 1} 不可见!`);
                } else {
                    console.log(`进度条 ${index + 1} 正常显示`);
                }
            });
        };
    </script>
</body>
</html>
