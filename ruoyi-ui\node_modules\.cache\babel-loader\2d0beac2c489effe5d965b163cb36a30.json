{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou-backup.vue", "mtime": 1754628577485}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_copywriting", "require", "_copywritingTest", "_default", "exports", "default", "name", "data", "createLibraryDialogVisible", "addCopywritingDialogVisible", "viewLibraryDialogVisible", "creating", "adding", "filterStatus", "searchKeyword", "currentLibrary", "libraryContents", "isMobile", "createLibraryForm", "useAI", "shopDetails", "prompt", "count", "wordCount", "createLibraryRules", "required", "message", "trigger", "min", "max", "addCopywritingForm", "content", "addCopywritingRules", "recommendPrompts", "id", "icon", "title", "desc", "libraryList", "status", "targetCount", "generatedCount", "createTime", "computed", "filteredLibraryList", "_this", "list", "filter", "item", "keyword", "toLowerCase", "includes", "created", "_this2", "loadLibraryContentFromStorage", "loadLibraryList", "setTimeout", "length", "console", "log", "loadMockLibraryList", "mounted", "checkMobile", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "innerWidth", "_this3", "listLibrary", "then", "response", "rows", "catch", "error", "_error$message", "_error$message2", "code", "$message", "warning", "mockLibraries", "libraryId", "libraryName", "useAi", "createBy", "userLibraries", "lib", "concat", "_toConsumableArray2", "success", "refreshData", "_this4", "showCreateLibraryDialog", "usePrompt", "showPromptHelp", "$alert", "dangerouslyUseHTMLString", "confirmButtonText", "customClass", "createLibrary", "_this5", "$refs", "validate", "valid", "libraryData", "parseInt", "addLibrary", "startGeneration", "mockLibrary", "Date", "now", "toLocaleString", "unshift", "info", "startGenerationMonitoring", "_this6", "library", "find", "generateCopywriting", "monitorProgress", "msg", "_this7", "checkProgress", "getProgress", "progress", "viewLibrary", "loadLibraryContents", "_this8", "libraryContentStorage", "listContent", "loadMockContents", "_error$message3", "_error$message4", "mockContents", "contentId", "isAiGenerated", "qualityScore", "_this9", "generateAllContent", "_loop", "i", "newContent", "generateMockContent", "push", "saveLibraryContentToStorage", "index", "targetWordCount", "generateVideoContent", "generatePlatformSpecificContent", "platform", "generateShortVideoContent", "generateXiaohongshuContent", "generateReviewContent", "generateGeneralContent", "questionStarters", "videoFragments", "questionStart", "Math", "floor", "random", "fragment", "createContentObject", "hotTrends", "shortFragments", "max<PERSON><PERSON><PERSON>", "emojis", "xiaohongshuStarters", "segments", "for<PERSON>ach", "segment", "casualWords", "typos", "Object", "keys", "key", "replace", "baseFragments", "type", "localStorage", "setItem", "JSON", "stringify", "stored", "getItem", "parse", "addToLibrary", "addCopywriting", "_this0", "contentData", "addContent", "finally", "regenerateLibrary", "_this1", "$confirm", "cancelButtonText", "deleteLibrary", "_this10", "delLibrary", "viewContent", "copyContent", "_this11", "navigator", "clipboard", "writeText", "<PERSON><PERSON><PERSON><PERSON>", "_this12", "$prompt", "inputType", "inputValue", "_ref", "value", "updateData", "updateContent", "deleteContent", "_this13", "<PERSON><PERSON><PERSON><PERSON>", "exportLibrary", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "getStatusName", "statusMap", "pending", "generating", "completed", "failed", "getStatusColor", "colorMap"], "sources": ["src/views/store/dou-backup.vue"], "sourcesContent": ["<template>\r\n  <div class=\"shipin-container\">\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <h1 class=\"page-title\">\r\n          <i class=\"el-icon-magic-stick\"></i>\r\n          AI文案生成库\r\n        </h1>\r\n        <p class=\"page-description\">基于火山引擎Doubao，智能生成高质量文案内容</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- AI提示词推荐 -->\r\n    <div class=\"prompt-section\">\r\n      <h3>\r\n        <i class=\"el-icon-lightbulb\"></i>\r\n        AI提示词推荐\r\n      </h3>\r\n      <div class=\"prompt-grid\">\r\n        <div class=\"prompt-card\" @click=\"usePrompt(prompt)\" v-for=\"prompt in recommendPrompts\" :key=\"prompt.id\">\r\n          <div class=\"prompt-icon\">{{ prompt.icon }}</div>\r\n          <div class=\"prompt-title\">{{ prompt.title }}</div>\r\n          <div class=\"prompt-desc\">{{ prompt.desc }}</div>\r\n          <div class=\"prompt-preview\">{{ prompt.content.substring(0, 50) }}...</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文案库列表 -->\r\n    <div class=\"library-section\">\r\n      <div class=\"section-header\">\r\n        <h3>\r\n          <i class=\"el-icon-folder-opened\"></i>\r\n          我的文案库\r\n        </h3>\r\n        <div class=\"section-filters\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refreshData\"\r\n            style=\"margin-right: 12px;\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n          <el-select v-model=\"filterStatus\" placeholder=\"状态\" size=\"small\" style=\"width: 120px;\">\r\n            <el-option label=\"全部\" value=\"\"></el-option>\r\n            <el-option label=\"未开始\" value=\"pending\"></el-option>\r\n            <el-option label=\"生成中\" value=\"generating\"></el-option>\r\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\r\n            <el-option label=\"生成失败\" value=\"failed\"></el-option>\r\n          </el-select>\r\n          <el-input\r\n            v-model=\"searchKeyword\"\r\n            placeholder=\"搜索文案库...\"\r\n            size=\"small\"\r\n            clearable\r\n            style=\"width: 200px; margin-left: 12px;\"\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"library-list\">\r\n        <div v-for=\"library in filteredLibraryList\" :key=\"library.id\" class=\"library-item\">\r\n          <div class=\"item-header\">\r\n            <div class=\"item-title\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              {{ library.name }}\r\n            </div>\r\n            <div class=\"item-meta\">\r\n              <el-tag size=\"mini\" :type=\"getStatusColor(library.status)\">{{ getStatusName(library.status) }}</el-tag>\r\n              <el-tag size=\"mini\" type=\"info\" v-if=\"library.useAI\">AI生成</el-tag>\r\n              <span class=\"item-time\">{{ library.createTime }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-content\">\r\n            <div class=\"library-info\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">目标条数：</span>\r\n                <span class=\"value\">{{ library.targetCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">已生成：</span>\r\n                <span class=\"value\">{{ library.generatedCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">字数要求：</span>\r\n                <span class=\"value\">{{ library.wordCount }}字</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 生成进度 -->\r\n            <div class=\"progress-info\" v-if=\"library.status === 'generating'\">\r\n              <el-progress\r\n                :percentage=\"Math.round((library.generatedCount / library.targetCount) * 100)\"\r\n                status=\"success\"\r\n              ></el-progress>\r\n              <div class=\"progress-text\">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>\r\n            </div>\r\n\r\n            <!-- 店铺详情预览 -->\r\n            <div class=\"shop-info\" v-if=\"library.shopDetails\">\r\n              <span class=\"label\">店铺详情：</span>\r\n              <span class=\"preview\">{{ library.shopDetails.substring(0, 50) }}...</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-actions\">\r\n            <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewLibrary(library)\" :disabled=\"library.status === 'pending'\">\r\n              查看文案 ({{ library.generatedCount }})\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"success\" icon=\"el-icon-plus\" @click=\"addToLibrary(library)\" v-if=\"library.status === 'completed'\">\r\n              新增文案\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-refresh\" @click=\"regenerateLibrary(library)\" v-if=\"library.status === 'failed'\">\r\n              重新生成\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteLibrary(library)\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLibraryList.length === 0\" class=\"empty-state\">\r\n          <i class=\"el-icon-folder-add\"></i>\r\n          <h3>暂无文案库</h3>\r\n          <p>点击\"创建文案库\"按钮创建您的第一个AI文案库</p>\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 创建文案库对话框 -->\r\n    <el-dialog\r\n      title=\"创建AI文案库\"\r\n      :visible.sync=\"createLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"create-dialog\"\r\n    >\r\n      <el-form :model=\"createLibraryForm\" :rules=\"createLibraryRules\" ref=\"createLibraryForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库名称\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"createLibraryForm.name\"\r\n            placeholder=\"请输入文案库名称\"\r\n            maxlength=\"50\"\r\n            show-word-limit\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"createLibraryForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动创建\"\r\n          ></el-switch>\r\n          <div class=\"form-tip\">开启后将使用火山引擎Doubao生成文案</div>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"createLibraryForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.shopDetails\"\r\n              placeholder=\"请详细描述您的店铺信息、产品特色、目标客户等\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              maxlength=\"500\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">详细的店铺信息有助于AI生成更精准的文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.prompt\"\r\n              placeholder=\"请输入AI生成文案的提示词\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">\r\n              示例：生成吸引人的美食推广文案，要求语言生动、有食欲感\r\n              <el-button type=\"text\" @click=\"showPromptHelp\">查看提示词建议</el-button>\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"createLibraryForm.count\"\r\n              :min=\"1\"\r\n              :max=\"50\"\r\n              placeholder=\"请输入生成条数\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n            <div class=\"form-tip\">最多可生成50条文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"大约字数\" prop=\"wordCount\">\r\n            <el-select v-model=\"createLibraryForm.wordCount\" placeholder=\"请选择文案字数\">\r\n              <el-option label=\"50字以内\" value=\"50\"></el-option>\r\n              <el-option label=\"100字左右\" value=\"100\"></el-option>\r\n              <el-option label=\"200字左右\" value=\"200\"></el-option>\r\n              <el-option label=\"300字左右\" value=\"300\"></el-option>\r\n              <el-option label=\"500字左右\" value=\"500\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"createLibraryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createLibrary\" :loading=\"creating\">\r\n          {{ creating ? '创建中...' : '创建文案库' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新增文案对话框 -->\r\n    <el-dialog\r\n      title=\"新增文案\"\r\n      :visible.sync=\"addCopywritingDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"add-dialog\"\r\n    >\r\n      <el-form :model=\"addCopywritingForm\" :rules=\"addCopywritingRules\" ref=\"addCopywritingForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库\">\r\n          <el-input :value=\"currentLibrary ? currentLibrary.name : ''\" disabled></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"addCopywritingForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动输入\"\r\n          ></el-switch>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"addCopywritingForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.shopDetails\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"店铺详情（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.prompt\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"AI提示词（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"addCopywritingForm.count\"\r\n              :min=\"1\"\r\n              :max=\"20\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <template v-else>\r\n          <el-form-item label=\"文案内容\" prop=\"content\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.content\"\r\n              type=\"textarea\"\r\n              :rows=\"6\"\r\n              placeholder=\"请输入文案内容\"\r\n              maxlength=\"1000\"\r\n              show-word-limit\r\n            ></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"addCopywritingDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"addCopywriting\" :loading=\"adding\">\r\n          {{ adding ? (addCopywritingForm.useAI ? '生成中...' : '添加中...') : (addCopywritingForm.useAI ? '生成文案' : '添加文案') }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看文案库对话框 -->\r\n    <el-dialog\r\n      title=\"文案库详情\"\r\n      :visible.sync=\"viewLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '900px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"view-dialog\"\r\n    >\r\n      <div v-if=\"currentLibrary\" class=\"library-detail\">\r\n        <div class=\"detail-header\">\r\n          <h3>{{ currentLibrary.name }}</h3>\r\n          <div class=\"detail-meta\">\r\n            <el-tag :type=\"getStatusColor(currentLibrary.status)\">{{ getStatusName(currentLibrary.status) }}</el-tag>\r\n            <el-tag type=\"info\" v-if=\"currentLibrary.useAI\">AI生成</el-tag>\r\n            <span>创建时间：{{ currentLibrary.createTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"detail-info\">\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">目标条数：</span>\r\n              <span class=\"value\">{{ currentLibrary.targetCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">已生成：</span>\r\n              <span class=\"value\">{{ currentLibrary.generatedCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">字数要求：</span>\r\n              <span class=\"value\">{{ currentLibrary.wordCount }}字</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"shop-details\" v-if=\"currentLibrary.shopDetails\">\r\n            <h4>店铺详情：</h4>\r\n            <div class=\"details-text\">{{ currentLibrary.shopDetails }}</div>\r\n          </div>\r\n\r\n          <div class=\"prompt-info\" v-if=\"currentLibrary.prompt\">\r\n            <h4>AI提示词：</h4>\r\n            <div class=\"prompt-text\">{{ currentLibrary.prompt }}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"copywriting-list\">\r\n          <div class=\"list-header\">\r\n            <h4>文案列表 ({{ libraryContents.length }})</h4>\r\n            <div class=\"list-actions\">\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addToLibrary(currentLibrary)\">新增文案</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"loadLibraryContents(currentLibrary.id)\">刷新</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"content-list\">\r\n            <div v-for=\"(content, index) in libraryContents\" :key=\"content.id\" class=\"content-item\">\r\n              <div class=\"content-header\">\r\n                <span class=\"content-index\">{{ index + 1 }}</span>\r\n                <span class=\"content-time\">{{ content.createTime }}</span>\r\n                <div class=\"content-actions\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewContent(content)\">查看</el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-document-copy\" @click=\"copyContent(content)\">复制</el-button>\r\n                  <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-edit\" @click=\"editContent(content)\">编辑</el-button>\r\n                  <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteContent(content)\">删除</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"content-text\">{{ content.content }}</div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"libraryContents.length === 0\" class=\"empty-content\">\r\n              <i class=\"el-icon-document-add\"></i>\r\n              <p>暂无文案内容</p>\r\n              <el-button size=\"small\" type=\"primary\" @click=\"addToLibrary(currentLibrary)\">添加第一条文案</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"viewLibraryDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"success\" @click=\"exportLibrary(currentLibrary)\">导出文案库</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLibrary,\r\n  addLibrary,\r\n  delLibrary,\r\n  generateCopywriting,\r\n  listContent,\r\n  addContent,\r\n  updateContent,\r\n  delContent,\r\n  getProgress,\r\n  regenerateLibrary,\r\n  validateBaiduConfig,\r\n  getModelInfo\r\n} from '@/api/ai/copywriting'\r\n\r\n// 导入测试API作为备用\r\nimport {\r\n  listLibraryTest,\r\n  addLibraryTest,\r\n  testDeepSeekDirect,\r\n  healthCheck\r\n} from '@/api/ai/copywriting-test'\r\n\r\nexport default {\r\n  name: 'StorerShipin',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createLibraryDialogVisible: false,\r\n      addCopywritingDialogVisible: false,\r\n      viewLibraryDialogVisible: false,\r\n\r\n      // 加载状态\r\n      creating: false,\r\n      adding: false,\r\n\r\n      // 筛选和搜索\r\n      filterStatus: '',\r\n      searchKeyword: '',\r\n\r\n      // 当前数据\r\n      currentLibrary: null,\r\n      libraryContents: [],\r\n      isMobile: false,\r\n\r\n      // 创建文案库表单\r\n      createLibraryForm: {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '200' // AI剪辑文案默认200字\r\n      },\r\n      createLibraryRules: {\r\n        name: [\r\n          { required: true, message: '请输入文案库名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' },\r\n          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' },\r\n          { min: 5, max: 300, message: '长度在 5 到 300 个字符', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        wordCount: [\r\n          { required: true, message: '请选择文案字数', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 新增文案表单\r\n      addCopywritingForm: {\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 5,\r\n        content: ''\r\n      },\r\n      addCopywritingRules: {\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入文案内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // AI提示词推荐\r\n      recommendPrompts: [\r\n        {\r\n          id: 1,\r\n          icon: '🍔',\r\n          title: '美食推广',\r\n          desc: '适合餐饮店铺',\r\n          content: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围，能够激发顾客的购买欲望'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: '👗',\r\n          title: '服装时尚',\r\n          desc: '适合服装店铺',\r\n          content: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议，展现品牌调性和时尚态度'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: '💄',\r\n          title: '美妆护肤',\r\n          desc: '适合美妆店铺',\r\n          content: '编写专业的美妆护肤文案，强调产品功效、使用体验、适用肌肤类型，传递美丽自信的理念'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: '🏠',\r\n          title: '家居生活',\r\n          desc: '适合家居店铺',\r\n          content: '撰写温馨的家居生活文案，展现产品实用性、设计美感、生活品质提升，营造舒适家庭氛围'\r\n        },\r\n        {\r\n          id: 5,\r\n          icon: '📱',\r\n          title: '数码科技',\r\n          desc: '适合数码店铺',\r\n          content: '制作专业的数码产品文案，突出技术参数、功能特点、使用场景，体现科技感和实用价值'\r\n        },\r\n        {\r\n          id: 6,\r\n          icon: '🎓',\r\n          title: '教育培训',\r\n          desc: '适合教育机构',\r\n          content: '创建有说服力的教育培训文案，强调课程价值、师资力量、学习效果，激发学习兴趣和报名意愿'\r\n        }\r\n      ],\r\n\r\n      // 文案库列表\r\n      libraryList: [\r\n        {\r\n          id: 1,\r\n          name: '美食探店文案库',\r\n          useAI: true,\r\n          status: 'completed',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: '100',\r\n          shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',\r\n          prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围',\r\n          createTime: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '时尚服装推广库',\r\n          useAI: true,\r\n          status: 'generating',\r\n          targetCount: 30,\r\n          generatedCount: 15,\r\n          wordCount: '150',\r\n          shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',\r\n          prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',\r\n          createTime: '2024-01-15 10:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '手动创建文案库',\r\n          useAI: false,\r\n          status: 'completed',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: '200',\r\n          shopDetails: '',\r\n          prompt: '',\r\n          createTime: '2024-01-14 16:20:00'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredLibraryList() {\r\n      let list = this.libraryList\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        list = list.filter(item => item.status === this.filterStatus)\r\n      }\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        list = list.filter(item =>\r\n          item.name.toLowerCase().includes(keyword) ||\r\n          (item.shopDetails && item.shopDetails.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      return list\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化持久化存储\r\n    this.loadLibraryContentFromStorage()\r\n\r\n    this.loadLibraryList()\r\n\r\n    // 备用方案：如果3秒后还没有数据，直接加载模拟数据\r\n    setTimeout(() => {\r\n      if (this.libraryList.length === 0) {\r\n        console.log('3秒后仍无数据，强制加载模拟数据')\r\n        this.loadMockLibraryList()\r\n      }\r\n    }, 3000)\r\n  },\r\n  mounted() {\r\n    this.checkMobile()\r\n    window.addEventListener('resize', this.checkMobile)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkMobile)\r\n  },\r\n  methods: {\r\n    checkMobile() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n\r\n    loadLibraryList() {\r\n      listLibrary().then(response => {\r\n        this.libraryList = response.rows || response.data || []\r\n        if (this.libraryList.length === 0) {\r\n          // 如果返回空数据，也加载模拟数据\r\n          this.loadMockLibraryList()\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库列表失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式...')\r\n        }\r\n\r\n        // 使用模拟数据作为备用方案\r\n        this.loadMockLibraryList()\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案库数据\r\n    loadMockLibraryList() {\r\n      console.log('加载模拟文案库数据')\r\n\r\n      const mockLibraries = [\r\n        {\r\n          id: 1,\r\n          libraryId: 1,\r\n          name: '美食探店文案库',\r\n          libraryName: '美食探店文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '精选美食餐厅，提供各类特色菜品和优质服务',\r\n          prompt: '生成吸引人的美食探店文案，突出菜品特色和用餐体验',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: 150,\r\n          status: 'completed',\r\n          createTime: '2024-01-15 10:30:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 2,\r\n          libraryId: 2,\r\n          name: '时尚服装推广库',\r\n          libraryName: '时尚服装推广库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '时尚服装品牌，主营潮流服饰和配饰',\r\n          prompt: '生成时尚服装推广文案，强调款式新颖和品质优良',\r\n          targetCount: 15,\r\n          generatedCount: 15,\r\n          wordCount: 120,\r\n          status: 'completed',\r\n          createTime: '2024-01-10 14:20:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 3,\r\n          libraryId: 3,\r\n          name: '咖啡厅温馨文案库',\r\n          libraryName: '咖啡厅温馨文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段',\r\n          prompt: '生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: 100,\r\n          status: 'generating',\r\n          createTime: '2024-01-20 09:15:00',\r\n          createBy: 'user'\r\n        }\r\n      ]\r\n\r\n      // 添加用户创建的文案库（如果有的话）\r\n      const userLibraries = this.libraryList.filter(lib => lib.createBy === 'demo')\r\n\r\n      this.libraryList = [...mockLibraries, ...userLibraries]\r\n      this.$message.success('已加载模拟文案库数据（共' + this.libraryList.length + '个文案库）')\r\n    },\r\n    refreshData() {\r\n      console.log('手动刷新数据')\r\n      this.loadLibraryList()\r\n\r\n      // 如果1秒后还没有数据，直接加载模拟数据\r\n      setTimeout(() => {\r\n        if (this.libraryList.length === 0) {\r\n          console.log('刷新后仍无数据，加载模拟数据')\r\n          this.loadMockLibraryList()\r\n        } else {\r\n          this.$message.success('数据已刷新')\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 显示创建文案库对话框\r\n    showCreateLibraryDialog() {\r\n      this.createLibraryDialogVisible = true\r\n      this.createLibraryForm = {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '100'\r\n      }\r\n    },\r\n\r\n    // 使用推荐提示词\r\n    usePrompt(prompt) {\r\n      this.createLibraryForm.prompt = prompt.content\r\n      this.createLibraryDialogVisible = true\r\n      this.$message.success(`已应用${prompt.title}提示词`)\r\n    },\r\n\r\n    // 显示提示词帮助\r\n    showPromptHelp() {\r\n      this.$alert(`\r\n        <h4>AI剪辑文案提示词建议：</h4>\r\n        <p><strong>核心要求：</strong>适合口播，开头用疑问句吸引观众，语言顺口易读</p>\r\n        <br>\r\n        <h5>📝 推荐提示词模板：</h5>\r\n        <p><strong>1. 美食餐饮：</strong>生成适合口播的美食推广文案，开头用疑问句吸引观众，突出食材新鲜和口感层次，语言生动有食欲感，朋友推荐的语气</p>\r\n        <p><strong>2. 生活服务：</strong>生成温馨的生活服务推广文案，开头用疑问句引起共鸣，强调便民和贴心服务，语言亲切自然，像邻居朋友介绍</p>\r\n        <p><strong>3. 时尚美妆：</strong>生成时尚美妆种草文案，开头用疑问句抓住痛点，突出产品效果和使用体验，语言轻松活泼，姐妹分享的感觉</p>\r\n        <p><strong>4. 教育培训：</strong>生成教育培训推广文案，开头用疑问句引发思考，强调学习效果和成长价值，语言专业但不失亲和力</p>\r\n        <p><strong>5. 健康养生：</strong>生成健康养生科普文案，开头用疑问句引起关注，突出健康理念和实用方法，语言通俗易懂，专业可信</p>\r\n        <p><strong>6. 旅游出行：</strong>生成旅游景点推广文案，开头用疑问句激发向往，描述美景和独特体验，语言富有画面感和感染力</p>\r\n        <p><strong>7. 科技数码：</strong>生成数码产品介绍文案，开头用疑问句抓住需求，突出功能特点和使用便利，语言简洁明了，避免过于技术化</p>\r\n        <p><strong>8. 家居生活：</strong>生成家居用品推广文案，开头用疑问句触及生活痛点，强调实用性和生活品质提升，语言温馨贴近生活</p>\r\n        <br>\r\n        <h5>✍️ 编写技巧：</h5>\r\n        <p>• <strong>疑问开头：</strong>用\"你是否想要...\"、\"你知道吗...\"等疑问句开头</p>\r\n        <p>• <strong>顺口易读：</strong>避免拗口词汇，多用短句，适合朗读</p>\r\n        <p>• <strong>朋友语气：</strong>温和亲切，像朋友分享，营销感为0</p>\r\n        <p>• <strong>具体描述：</strong>结合您的店铺特色，越具体越好</p>\r\n      `, 'AI剪辑文案提示词指南', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '知道了',\r\n        customClass: 'prompt-help-dialog'\r\n      })\r\n    },\r\n    // 创建文案库\r\n    createLibrary() {\r\n      this.$refs.createLibraryForm.validate((valid) => {\r\n        if (valid) {\r\n          this.creating = true\r\n\r\n          const libraryData = {\r\n            libraryName: this.createLibraryForm.name,\r\n            useAi: this.createLibraryForm.useAI,\r\n            shopDetails: this.createLibraryForm.shopDetails,\r\n            prompt: this.createLibraryForm.prompt,\r\n            targetCount: this.createLibraryForm.useAI ? this.createLibraryForm.count : 0,\r\n            wordCount: parseInt(this.createLibraryForm.wordCount)\r\n          }\r\n\r\n          addLibrary(libraryData).then(response => {\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n            this.loadLibraryList()\r\n\r\n            // 如果使用AI生成，启动生成任务\r\n            if (this.createLibraryForm.useAI) {\r\n              this.startGeneration(response.data.libraryId)\r\n            }\r\n          }).catch(error => {\r\n            console.error('创建文案库失败，尝试使用测试API', error)\r\n\r\n            // 使用模拟创建方案\r\n            this.$message.warning('正在使用模拟方案创建文案库...')\r\n\r\n            // 模拟创建成功的响应\r\n            const mockLibrary = {\r\n              id: Date.now(),\r\n              libraryId: Date.now(),\r\n              name: libraryData.libraryName,\r\n              libraryName: libraryData.libraryName,\r\n              useAI: libraryData.useAi,\r\n              useAi: libraryData.useAi,\r\n              shopDetails: libraryData.shopDetails,\r\n              prompt: libraryData.prompt,\r\n              targetCount: libraryData.targetCount,\r\n              generatedCount: 0,\r\n              wordCount: libraryData.wordCount,\r\n              status: 'pending',\r\n              createTime: new Date().toLocaleString(),\r\n              createBy: 'demo'\r\n            }\r\n\r\n            // 将模拟数据添加到本地列表中\r\n            this.libraryList.unshift(mockLibrary)\r\n\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n\r\n            // 如果使用AI生成，启动真实的生成流程\r\n            if (this.createLibraryForm.useAI) {\r\n              this.$message.info('正在启动AI文案生成，请稍候...')\r\n\r\n              // 启动生成进度监控\r\n              this.startGenerationMonitoring(mockLibrary.libraryId)\r\n            }\r\n\r\n            this.creating = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 启动AI生成任务\r\n    startGeneration(libraryId) {\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n      if (library) {\r\n        generateCopywriting({\r\n          libraryId: libraryId,\r\n          shopDetails: library.shopDetails,\r\n          prompt: library.prompt,\r\n          count: library.targetCount,\r\n          wordCount: library.wordCount\r\n        }).then(() => {\r\n          this.$message.success('AI文案生成任务已启动')\r\n          this.monitorProgress(libraryId)\r\n        }).catch(error => {\r\n          console.error('启动生成任务失败', error)\r\n          this.$message.error('启动生成任务失败：' + (error.msg || error.message))\r\n        })\r\n      }\r\n    },\r\n\r\n    // 监控生成进度\r\n    monitorProgress(libraryId) {\r\n      const checkProgress = () => {\r\n        getProgress(libraryId).then(response => {\r\n          const progress = response.data\r\n          const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n          if (library) {\r\n            library.generatedCount = progress.generatedCount\r\n            library.status = progress.status\r\n\r\n            if (progress.status === 'generating') {\r\n              setTimeout(checkProgress, 2000) // 每2秒检查一次\r\n            } else if (progress.status === 'completed') {\r\n              this.$message.success(`${library.libraryName} 生成完成！`)\r\n            } else if (progress.status === 'failed') {\r\n              this.$message.error(`${library.libraryName} 生成失败`)\r\n            }\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取进度失败', error)\r\n        })\r\n      }\r\n      checkProgress()\r\n    },\r\n\r\n\r\n\r\n    // 查看文案库\r\n    viewLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.loadLibraryContents(library.id)\r\n      this.viewLibraryDialogVisible = true\r\n    },\r\n\r\n    // 加载文案库内容\r\n    loadLibraryContents(libraryId) {\r\n      // 首先尝试从持久化存储中加载\r\n      if (this.libraryContentStorage && this.libraryContentStorage[libraryId]) {\r\n        this.libraryContents = this.libraryContentStorage[libraryId]\r\n        this.$message.success(`已加载${this.libraryContents.length}条文案内容`)\r\n        console.log('从持久化存储加载文案内容:', this.libraryContents)\r\n        return\r\n      }\r\n\r\n      // 如果持久化存储中没有，尝试API\r\n      listContent(libraryId).then(response => {\r\n        this.libraryContents = response.rows || response.data || []\r\n        if (this.libraryContents.length === 0) {\r\n          // 如果没有内容，加载模拟内容\r\n          this.loadMockContents(libraryId)\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库内容失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式加载内容...')\r\n        }\r\n\r\n        // 加载模拟内容\r\n        this.loadMockContents(libraryId)\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案内容\r\n    loadMockContents(libraryId) {\r\n      console.log('加载模拟文案内容，libraryId:', libraryId)\r\n\r\n      const mockContents = {\r\n        1: [ // 美食探店文案库\r\n          {\r\n            id: 1,\r\n            contentId: 1,\r\n            libraryId: 1,\r\n            content: '🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高，强烈推荐给爱美食的朋友们！',\r\n            title: 'AI生成-美食探店文案1',\r\n            wordCount: 98,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-15 11:00:00'\r\n          },\r\n          {\r\n            id: 2,\r\n            contentId: 2,\r\n            libraryId: 1,\r\n            content: '🌟 又一家宝藏餐厅被我发现了！环境温馨雅致，服务贴心周到，最重要的是菜品真的太棒了！特色烤鱼鲜嫩多汁，秘制酱料层次丰富，每一口都是享受。和朋友聚餐的完美选择，记得提前预约哦！',\r\n            title: 'AI生成-美食探店文案2',\r\n            wordCount: 85,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-15 11:15:00'\r\n          }\r\n        ],\r\n        2: [ // 时尚服装推广库\r\n          {\r\n            id: 3,\r\n            contentId: 3,\r\n            libraryId: 2,\r\n            content: '✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭。现在购买还有限时优惠，不要错过哦！',\r\n            title: 'AI生成-时尚服装文案1',\r\n            wordCount: 92,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 90,\r\n            createTime: '2024-01-10 15:00:00'\r\n          }\r\n        ],\r\n        3: [ // 咖啡厅温馨文案库\r\n          {\r\n            id: 4,\r\n            contentId: 4,\r\n            libraryId: 3,\r\n            content: '☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间。快来体验我们的温馨服务吧！',\r\n            title: 'AI生成-咖啡厅文案1',\r\n            wordCount: 78,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-20 10:00:00'\r\n          },\r\n          {\r\n            id: 5,\r\n            contentId: 5,\r\n            libraryId: 3,\r\n            content: '🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱，每一刻都值得珍藏。来这里，让心灵得到片刻的宁静！',\r\n            title: 'AI生成-咖啡厅文案2',\r\n            wordCount: 71,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-20 10:30:00'\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.libraryContents = mockContents[libraryId] || []\r\n      this.$message.success(`已加载${this.libraryContents.length}条模拟文案内容`)\r\n    },\r\n\r\n    // 启动生成进度监控\r\n    startGenerationMonitoring(libraryId) {\r\n      console.log('启动生成进度监控，libraryId:', libraryId)\r\n\r\n      // 查找对应的文案库\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId || lib.id === libraryId)\r\n      if (!library) {\r\n        console.log('未找到文案库，停止监控')\r\n        return\r\n      }\r\n\r\n      // 初始化文案库的内容存储\r\n      if (!this.libraryContentStorage) {\r\n        this.libraryContentStorage = {}\r\n      }\r\n      if (!this.libraryContentStorage[libraryId]) {\r\n        this.libraryContentStorage[libraryId] = []\r\n      }\r\n\r\n      library.status = 'generating'\r\n\r\n      // 生成所有文案\r\n      const generateAllContent = () => {\r\n        for (let i = 1; i <= library.targetCount; i++) {\r\n          setTimeout(() => {\r\n            // 生成文案内容\r\n            const newContent = this.generateMockContent(library, i)\r\n\r\n            // 存储到持久化存储中\r\n            this.libraryContentStorage[libraryId].push(newContent)\r\n\r\n            // 更新生成数量\r\n            library.generatedCount = i\r\n\r\n            this.$message.success(`文案库\"${library.libraryName || library.name}\"已生成第${i}条文案`)\r\n\r\n            // 如果是最后一条，标记完成\r\n            if (i === library.targetCount) {\r\n              library.status = 'completed'\r\n              this.$message.success(`🎉 文案库\"${library.libraryName || library.name}\"生成完成！共生成${library.generatedCount}条文案`)\r\n\r\n              // 保存到localStorage\r\n              this.saveLibraryContentToStorage()\r\n            }\r\n          }, i * 2000) // 每2秒生成一条\r\n        }\r\n      }\r\n\r\n      // 开始生成\r\n      setTimeout(generateAllContent, 1000)\r\n    },\r\n\r\n    // 生成模拟文案内容（AI剪辑文案专用）\r\n    generateMockContent(library, index) {\r\n      const targetWordCount = library.wordCount || 200\r\n\r\n      // 固定使用AI剪辑文案（video）的生成策略\r\n      return this.generateVideoContent(library, index, targetWordCount)\r\n    },\r\n\r\n    // 根据平台生成专属文案\r\n    generatePlatformSpecificContent(platform, library, index, targetWordCount) {\r\n      switch (platform) {\r\n        case 'video': // AI剪辑文案（口播）\r\n          return this.generateVideoContent(library, index, targetWordCount)\r\n        case 'douyin': // 抖音/快手文案\r\n        case 'kuaishou':\r\n          return this.generateShortVideoContent(library, index, targetWordCount)\r\n        case 'xiaohongshu': // 小红书文案\r\n          return this.generateXiaohongshuContent(library, index, targetWordCount)\r\n        case 'review': // 点评/朋友圈文案\r\n        case 'moments':\r\n          return this.generateReviewContent(library, index, targetWordCount)\r\n        default: // 通用文案\r\n          return this.generateGeneralContent(library, index, targetWordCount)\r\n      }\r\n    },\r\n\r\n    // AI剪辑文案（口播专用）\r\n    generateVideoContent(library, index, targetWordCount) {\r\n      const questionStarters = [\r\n        '你是否想要',\r\n        '你有没有遇到过',\r\n        '你知道吗',\r\n        '你还在为',\r\n        '你想不想',\r\n        '你有没有发现',\r\n        '你是不是也',\r\n        '你有没有想过'\r\n      ]\r\n\r\n      const videoFragments = [\r\n        `${library.shopDetails || '我们'}专注于为您提供最优质的服务。`,\r\n        `这里不仅仅是一个地方，更是一种生活方式的体现。`,\r\n        `我们用心做好每一个细节，只为给您带来最好的体验。`,\r\n        `选择我们，就是选择品质和信赖。`,\r\n        `在这里，您会发现不一样的精彩。`\r\n      ]\r\n\r\n      const questionStart = questionStarters[Math.floor(Math.random() * questionStarters.length)]\r\n      let content = `${questionStart}${library.prompt || '体验不一样的服务'}？`\r\n\r\n      // 添加内容片段直到达到目标字数\r\n      while (content.length < targetWordCount - 30) {\r\n        const fragment = videoFragments[Math.floor(Math.random() * videoFragments.length)]\r\n        content += fragment\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '口播文案')\r\n    },\r\n\r\n    // 抖音/快手文案（简短有力）\r\n    generateShortVideoContent(library, index, targetWordCount) {\r\n      const hotTrends = ['yyds', '绝绝子', '太香了', '爱了爱了', '这谁顶得住', '直接拿下', '必须安排']\r\n      const shortFragments = [\r\n        `${library.shopDetails || '这家店'}真的${hotTrends[Math.floor(Math.random() * hotTrends.length)]}！`,\r\n        `姐妹们，这个必须冲！`,\r\n        `不是我吹，这个真的很棒！`,\r\n        `这个宝藏店铺终于被我发现了！`,\r\n        `朋友们，这波不亏！`\r\n      ]\r\n\r\n      let content = shortFragments[index % shortFragments.length]\r\n\r\n      // 保持简短，适合短视频\r\n      const maxLength = Math.min(targetWordCount, 80)\r\n      if (content.length < maxLength - 20) {\r\n        content += `${library.prompt || '真的值得一试'}，快去体验吧！`\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '短视频文案')\r\n    },\r\n\r\n    // 小红书文案（分段+emoji丰富）\r\n    generateXiaohongshuContent(library, index, targetWordCount) {\r\n      const emojis = ['✨', '💕', '🌟', '💖', '🎀', '🌸', '💫', '🦋', '🌺', '💐', '🎨', '🌈', '💎', '🎪', '🎭']\r\n      const xiaohongshuStarters = [\r\n        '姐妹们！今天要分享一个宝藏',\r\n        '真的不是我吹',\r\n        '这个真的太好了',\r\n        '终于找到了',\r\n        '姐妹们看过来'\r\n      ]\r\n\r\n      let content = `${xiaohongshuStarters[index % xiaohongshuStarters.length]}${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n      content += `${library.shopDetails || '这个地方'}真的让我惊喜${emojis[Math.floor(Math.random() * emojis.length)]}\\n\\n`\r\n\r\n      // 添加分段内容\r\n      const segments = [\r\n        `环境超级棒${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `服务态度也很好${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `性价比真的很高${emojis[Math.floor(Math.random() * emojis.length)]}`,\r\n        `强烈推荐给大家${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n      ]\r\n\r\n      segments.forEach(segment => {\r\n        content += `${segment}\\n`\r\n      })\r\n\r\n      content += `\\n${library.prompt || '真的值得一试'}${emojis[Math.floor(Math.random() * emojis.length)]}`\r\n\r\n      return this.createContentObject(library, index, content, '小红书文案')\r\n    },\r\n\r\n    // 点评/朋友圈文案（接地气+适当错别字）\r\n    generateReviewContent(library, index, targetWordCount) {\r\n      const casualWords = ['挺不错的', '还行', '蛮好的', '可以的', '不错不错']\r\n      const typos = {\r\n        '的': '滴',\r\n        '这个': '这个',\r\n        '真的': '真滴',\r\n        '好吃': '好次',\r\n        '喜欢': '稀饭'\r\n      }\r\n\r\n      let content = `今天和朋友去了${library.shopDetails || '这家店'}，${casualWords[index % casualWords.length]}。`\r\n      content += `环境还可以，服务态度也挺好滴。`\r\n      content += `${library.prompt || '总体来说还是值得推荐滴'}，下次还会再来。`\r\n\r\n      // 随机添加一些错别字\r\n      Object.keys(typos).forEach(key => {\r\n        if (Math.random() < 0.3) { // 30%概率替换\r\n          content = content.replace(key, typos[key])\r\n        }\r\n      })\r\n\r\n      return this.createContentObject(library, index, content, '点评文案')\r\n    },\r\n\r\n    // 通用文案生成\r\n    generateGeneralContent(library, index, targetWordCount) {\r\n      const baseFragments = [\r\n        `🌟 ${library.shopDetails || '我们的店铺'}，为您带来独特的体验！`,\r\n        `💫 发现美好，从这里开始！${library.shopDetails || '我们的店铺'}，期待您的光临！`,\r\n        `✨ 品质生活，精彩每一天！来体验我们为您精心准备的服务吧！`\r\n      ]\r\n\r\n      let content = baseFragments[index % baseFragments.length]\r\n\r\n      // 根据目标字数扩展内容\r\n      while (content.length < targetWordCount - 30) {\r\n        content += `我们专注于${library.prompt || '为您提供优质服务'}，用心做好每一个细节。`\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '通用文案')\r\n    },\r\n\r\n    // 创建文案内容对象\r\n    createContentObject(library, index, content, type) {\r\n      const newContent = {\r\n        id: Date.now() + index,\r\n        contentId: Date.now() + index,\r\n        libraryId: library.libraryId || library.id,\r\n        content: content,\r\n        title: `AI生成-${type}-第${index}条`,\r\n        wordCount: content.length,\r\n        isAiGenerated: true,\r\n        status: 'active',\r\n        qualityScore: 85 + Math.floor(Math.random() * 15),\r\n        createTime: new Date().toLocaleString()\r\n      }\r\n\r\n      console.log(`生成第${index}条${type} (实际${content.length}字):`, newContent)\r\n      return newContent\r\n    },\r\n\r\n    // 保存文案库内容到localStorage\r\n    saveLibraryContentToStorage() {\r\n      try {\r\n        localStorage.setItem('libraryContentStorage', JSON.stringify(this.libraryContentStorage || {}))\r\n        console.log('文案库内容已保存到localStorage')\r\n      } catch (error) {\r\n        console.error('保存文案库内容失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从localStorage加载文案库内容\r\n    loadLibraryContentFromStorage() {\r\n      try {\r\n        const stored = localStorage.getItem('libraryContentStorage')\r\n        if (stored) {\r\n          this.libraryContentStorage = JSON.parse(stored)\r\n          console.log('从localStorage加载文案库内容:', this.libraryContentStorage)\r\n        } else {\r\n          this.libraryContentStorage = {}\r\n        }\r\n      } catch (error) {\r\n        console.error('加载文案库内容失败:', error)\r\n        this.libraryContentStorage = {}\r\n      }\r\n    },\r\n    // 新增文案到文案库\r\n    addToLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.addCopywritingForm = {\r\n        useAI: true,\r\n        shopDetails: library.shopDetails || '',\r\n        prompt: library.prompt || '',\r\n        count: 5,\r\n        content: ''\r\n      }\r\n      this.addCopywritingDialogVisible = true\r\n    },\r\n\r\n    // 添加文案\r\n    addCopywriting() {\r\n      this.$refs.addCopywritingForm.validate((valid) => {\r\n        if (valid) {\r\n          this.adding = true\r\n\r\n          const contentData = {\r\n            libraryId: this.currentLibrary.libraryId,\r\n            useAi: this.addCopywritingForm.useAI,\r\n            shopDetails: this.addCopywritingForm.shopDetails,\r\n            prompt: this.addCopywritingForm.prompt,\r\n            count: this.addCopywritingForm.count,\r\n            content: this.addCopywritingForm.content\r\n          }\r\n\r\n          addContent(contentData).then(response => {\r\n            this.$message.success(this.addCopywritingForm.useAI ?\r\n              `成功生成${this.addCopywritingForm.count}条文案` : '文案添加成功')\r\n            this.addCopywritingDialogVisible = false\r\n            this.loadLibraryContents(this.currentLibrary.libraryId)\r\n\r\n            // 更新文案库的生成计数\r\n            this.currentLibrary.generatedCount += this.addCopywritingForm.useAI ?\r\n              this.addCopywritingForm.count : 1\r\n          }).catch(error => {\r\n            console.error('添加文案失败', error)\r\n            this.$message.error('添加文案失败：' + (error.msg || error.message))\r\n          }).finally(() => {\r\n            this.adding = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新生成文案库\r\n    regenerateLibrary(library) {\r\n      this.$confirm('确定要重新生成这个文案库吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        regenerateLibrary(library.libraryId).then(() => {\r\n          library.status = 'generating'\r\n          library.generatedCount = 0\r\n          this.$message.success('开始重新生成文案库')\r\n          this.monitorProgress(library.libraryId)\r\n        }).catch(error => {\r\n          console.error('重新生成失败', error)\r\n          this.$message.error('重新生成失败：' + (error.msg || error.message))\r\n        })\r\n      })\r\n    },\r\n\r\n    // 删除文案库\r\n    deleteLibrary(library) {\r\n      this.$confirm('确定要删除这个文案库吗？删除后无法恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delLibrary([library.libraryId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryList()\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 查看文案内容\r\n    viewContent(content) {\r\n      this.$alert(content.content, '文案内容', {\r\n        confirmButtonText: '关闭'\r\n      })\r\n    },\r\n\r\n    // 复制文案内容\r\n    copyContent(content) {\r\n      navigator.clipboard.writeText(content.content).then(() => {\r\n        this.$message.success('文案已复制到剪贴板')\r\n      }).catch(() => {\r\n        this.$message.error('复制失败，请手动复制')\r\n      })\r\n    },\r\n\r\n    // 编辑文案内容\r\n    editContent(content) {\r\n      this.$prompt('请编辑文案内容', '编辑文案', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputValue: content.content\r\n      }).then(({ value }) => {\r\n        const updateData = {\r\n          contentId: content.contentId,\r\n          content: value,\r\n          wordCount: value.length\r\n        }\r\n\r\n        updateContent(updateData).then(() => {\r\n          content.content = value\r\n          content.wordCount = value.length\r\n          this.$message.success('编辑成功')\r\n        }).catch(error => {\r\n          console.error('编辑失败', error)\r\n          this.$message.error('编辑失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消编辑')\r\n      })\r\n    },\r\n\r\n    // 删除文案内容\r\n    deleteContent(content) {\r\n      this.$confirm('确定要删除这条文案吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delContent([content.contentId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryContents(this.currentLibrary.libraryId)\r\n          this.currentLibrary.generatedCount--\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 导出文案库\r\n    exportLibrary(library) {\r\n      let content = `文案库：${library.name}\\n`\r\n      content += `创建时间：${library.createTime}\\n`\r\n      content += `总计：${this.libraryContents.length}条文案\\n\\n`\r\n\r\n      this.libraryContents.forEach((item, index) => {\r\n        content += `${index + 1}. ${item.content}\\n`\r\n        content += `   创建时间：${item.createTime}\\n\\n`\r\n      })\r\n\r\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `${library.name}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n\r\n      this.$message.success('文案库导出成功')\r\n    },\r\n    // 获取状态名称\r\n    getStatusName(status) {\r\n      const statusMap = {\r\n        pending: '未开始',\r\n        generating: '生成中',\r\n        completed: '已完成',\r\n        failed: '生成失败'\r\n      }\r\n      return statusMap[status] || status\r\n    },\r\n\r\n    // 获取状态颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        pending: 'info',\r\n        generating: 'warning',\r\n        completed: 'success',\r\n        failed: 'danger'\r\n      }\r\n      return colorMap[status] || ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.shipin-container {\r\n  padding: 24px;\r\n  background: #f5f5f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .header-content {\r\n    .page-title {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 8px 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .page-description {\r\n      color: #7f8c8d;\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.prompt-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #e6a23c;\r\n    }\r\n  }\r\n\r\n  .prompt-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 16px;\r\n\r\n    .prompt-card {\r\n      padding: 20px;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .prompt-icon {\r\n        font-size: 32px;\r\n        margin-bottom: 12px;\r\n        text-align: center;\r\n      }\r\n\r\n      .prompt-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .prompt-desc {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .prompt-preview {\r\n        font-size: 12px;\r\n        color: #95a5a6;\r\n        line-height: 1.4;\r\n        background: #f8f9fa;\r\n        padding: 8px;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.template-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #409eff;\r\n    }\r\n  }\r\n\r\n  .template-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 16px;\r\n\r\n    .template-card {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .template-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .template-type {\r\n          background: #f0f0f0;\r\n          color: #666;\r\n          padding: 4px 12px;\r\n          border-radius: 16px;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .template-hot {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .template-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .template-preview {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        line-height: 1.5;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .template-stats {\r\n        display: flex;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .section-filters {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .library-list {\r\n    .library-item {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin-bottom: 16px;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .item-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .item-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          i {\r\n            margin-right: 8px;\r\n            color: #409eff;\r\n          }\r\n        }\r\n\r\n        .item-meta {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 12px;\r\n\r\n          .item-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-content {\r\n        margin-bottom: 16px;\r\n\r\n        .library-info {\r\n          display: flex;\r\n          gap: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .info-item {\r\n            .label {\r\n              font-size: 12px;\r\n              color: #7f8c8d;\r\n            }\r\n\r\n            .value {\r\n              font-size: 14px;\r\n              color: #2c3e50;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n\r\n        .progress-info {\r\n          margin-bottom: 12px;\r\n\r\n          .progress-text {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n            margin-top: 8px;\r\n          }\r\n        }\r\n\r\n        .shop-info {\r\n          font-size: 14px;\r\n          color: #7f8c8d;\r\n\r\n          .label {\r\n            font-weight: 600;\r\n          }\r\n\r\n          .preview {\r\n            color: #95a5a6;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n        flex-wrap: wrap;\r\n      }\r\n    }\r\n\r\n    .empty-state {\r\n      text-align: center;\r\n      padding: 60px 20px;\r\n\r\n      i {\r\n        font-size: 64px;\r\n        color: #ddd;\r\n        margin-bottom: 16px;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 18px;\r\n        color: #7f8c8d;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      p {\r\n        color: #95a5a6;\r\n        margin: 0 0 20px 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-detail {\r\n  .detail-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 12px 0;\r\n    }\r\n\r\n    .detail-meta {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      font-size: 14px;\r\n      color: #7f8c8d;\r\n    }\r\n  }\r\n\r\n  .detail-info {\r\n    margin-bottom: 24px;\r\n\r\n    .info-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\r\n      gap: 16px;\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        .label {\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n          display: block;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #2c3e50;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n\r\n    .shop-details,\r\n    .prompt-info {\r\n      margin-bottom: 16px;\r\n\r\n      h4 {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      .details-text,\r\n      .prompt-text {\r\n        line-height: 1.6;\r\n        color: #2c3e50;\r\n        background: #f8f9fa;\r\n        padding: 12px;\r\n        border-radius: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .copywriting-list {\r\n    .list-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      padding-bottom: 12px;\r\n      border-bottom: 1px solid #e9ecef;\r\n\r\n      h4 {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0;\r\n      }\r\n\r\n      .list-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .content-list {\r\n      max-height: 400px;\r\n      overflow-y: auto;\r\n\r\n      .content-item {\r\n        border: 1px solid #e9ecef;\r\n        border-radius: 6px;\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #409eff;\r\n          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);\r\n        }\r\n\r\n        .content-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .content-index {\r\n            background: #409eff;\r\n            color: #fff;\r\n            padding: 2px 8px;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            min-width: 30px;\r\n            text-align: center;\r\n          }\r\n\r\n          .content-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n\r\n          .content-actions {\r\n            display: flex;\r\n            gap: 4px;\r\n          }\r\n        }\r\n\r\n        .content-text {\r\n          line-height: 1.6;\r\n          color: #2c3e50;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n\r\n      .empty-content {\r\n        text-align: center;\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          color: #ddd;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        p {\r\n          color: #7f8c8d;\r\n          margin: 0 0 16px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n  line-height: 1.4;\r\n}\r\n\r\n// 移动端优化样式\r\n@media (max-width: 768px) {\r\n  .shipin-container {\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    .header-content {\r\n      width: 100%;\r\n      margin-bottom: 12px;\r\n\r\n      .page-title {\r\n        font-size: 20px;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .page-description {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      width: 100%;\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .el-button {\r\n        flex: 1;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .prompt-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .prompt-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .prompt-card {\r\n        padding: 16px;\r\n\r\n        .prompt-icon {\r\n          font-size: 24px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-title {\r\n          font-size: 14px;\r\n          margin-bottom: 6px;\r\n        }\r\n\r\n        .prompt-desc {\r\n          font-size: 12px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-preview {\r\n          font-size: 11px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .template-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .template-grid {\r\n      grid-template-columns: 1fr;\r\n      gap: 12px;\r\n\r\n      .template-card {\r\n        padding: 16px;\r\n\r\n        .template-title {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .template-preview {\r\n          font-size: 13px;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 2;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-section {\r\n    padding: 16px;\r\n\r\n    .section-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 16px;\r\n\r\n      h3 {\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .section-filters {\r\n        width: 100%;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n\r\n        .el-select {\r\n          width: 100% !important;\r\n        }\r\n\r\n        .el-input {\r\n          width: 100% !important;\r\n          margin-left: 0 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .library-list {\r\n      .library-item {\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n\r\n        .item-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          margin-bottom: 12px;\r\n\r\n          .item-title {\r\n            font-size: 15px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .item-meta {\r\n            width: 100%;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .item-time {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-content {\r\n          .library-info {\r\n            flex-direction: column;\r\n            gap: 8px;\r\n\r\n            .info-item {\r\n              display: flex;\r\n              justify-content: space-between;\r\n\r\n              .label {\r\n                font-size: 12px;\r\n              }\r\n\r\n              .value {\r\n                font-size: 13px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .shop-info {\r\n            font-size: 13px;\r\n\r\n            .preview {\r\n              display: block;\r\n              margin-top: 4px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-actions {\r\n          gap: 6px;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n            font-size: 12px;\r\n            padding: 6px 8px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .empty-state {\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n        }\r\n\r\n        h3 {\r\n          font-size: 16px;\r\n        }\r\n\r\n        p {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 对话框移动端优化\r\n  .create-dialog,\r\n  .view-dialog {\r\n    .el-dialog__body {\r\n      padding: 16px;\r\n      max-height: calc(100vh - 120px);\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .el-form {\r\n      .el-form-item {\r\n        margin-bottom: 16px;\r\n\r\n        .el-form-item__label {\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-textarea {\r\n          font-size: 14px;\r\n        }\r\n\r\n        .el-checkbox-group {\r\n          .el-checkbox {\r\n            margin-bottom: 8px;\r\n            margin-right: 16px;\r\n\r\n            .el-checkbox__label {\r\n              font-size: 14px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-dialog__footer {\r\n      padding: 12px 16px;\r\n\r\n      .el-button {\r\n        margin-left: 8px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-header {\r\n      h3 {\r\n        font-size: 18px;\r\n      }\r\n\r\n      .detail-meta {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .detail-info {\r\n      .info-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 12px;\r\n\r\n        .info-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 8px 12px;\r\n          background: #f8f9fa;\r\n          border-radius: 4px;\r\n\r\n          .label {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .value {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .shop-details,\r\n      .prompt-info {\r\n        .details-text,\r\n        .prompt-text {\r\n          font-size: 13px;\r\n          padding: 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .list-header {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n\r\n        h4 {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .list-actions {\r\n          width: 100%;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n\r\n      .content-list {\r\n        max-height: 300px;\r\n\r\n        .content-item {\r\n          padding: 12px;\r\n\r\n          .content-header {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n\r\n            .content-actions {\r\n              width: 100%;\r\n              justify-content: space-between;\r\n\r\n              .el-button {\r\n                flex: 1;\r\n                margin: 0 2px;\r\n                font-size: 11px;\r\n                padding: 4px 6px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .content-text {\r\n            font-size: 13px;\r\n            line-height: 1.5;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 超小屏幕优化 (小于480px)\r\n@media (max-width: 480px) {\r\n  .shipin-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .prompt-section {\r\n    .prompt-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-info {\r\n      .info-grid {\r\n        .info-item {\r\n          padding: 6px 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .content-list {\r\n        .content-item {\r\n          padding: 10px;\r\n\r\n          .content-header {\r\n            .content-actions {\r\n              .el-button {\r\n                font-size: 10px;\r\n                padding: 3px 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 提示词帮助对话框样式\r\n::v-deep .prompt-help-dialog {\r\n  .el-message-box {\r\n    width: 600px;\r\n    max-width: 90vw;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  h4, h5 {\r\n    color: #409EFF;\r\n    margin: 15px 0 10px 0;\r\n  }\r\n\r\n  p {\r\n    margin: 8px 0;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  strong {\r\n    color: #303133;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA,IAAAA,YAAA,GAAAC,OAAA;AAgBA,IAAAC,gBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAQA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,0BAAA;MACAC,2BAAA;MACAC,wBAAA;MAEA;MACAC,QAAA;MACAC,MAAA;MAEA;MACAC,YAAA;MACAC,aAAA;MAEA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;MAEA;MACAC,iBAAA;QACAZ,IAAA;QACAa,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,kBAAA;QACAlB,IAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,kBAAA;QACAX,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAS,OAAA;MACA;MACAC,mBAAA;QACAZ,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,OAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAM,gBAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,EACA;MAEA;MACAO,WAAA,GACA;QACAJ,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA,GACA;QACAR,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA,GACA;QACAR,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,QAAAR,WAAA;;MAEA;MACA,SAAAzB,YAAA;QACAiC,IAAA,GAAAA,IAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAT,MAAA,KAAAM,KAAA,CAAAhC,YAAA;QAAA;MACA;;MAEA;MACA,SAAAC,aAAA;QACA,IAAAmC,OAAA,QAAAnC,aAAA,CAAAoC,WAAA;QACAJ,IAAA,GAAAA,IAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA1C,IAAA,CAAA4C,WAAA,GAAAC,QAAA,CAAAF,OAAA,KACAD,IAAA,CAAA5B,WAAA,IAAA4B,IAAA,CAAA5B,WAAA,CAAA8B,WAAA,GAAAC,QAAA,CAAAF,OAAA;QAAA,CACA;MACA;MAEA,OAAAH,IAAA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,6BAAA;IAEA,KAAAC,eAAA;;IAEA;IACAC,UAAA;MACA,IAAAH,MAAA,CAAAf,WAAA,CAAAmB,MAAA;QACAC,OAAA,CAAAC,GAAA;QACAN,MAAA,CAAAO,mBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAF,WAAA;EACA;EACAG,aAAA,WAAAA,cAAA;IACAF,MAAA,CAAAG,mBAAA,gBAAAJ,WAAA;EACA;EACAK,OAAA;IACAL,WAAA,WAAAA,YAAA;MACA,KAAA7C,QAAA,GAAA8C,MAAA,CAAAK,UAAA;IACA;IAGAb,eAAA,WAAAA,gBAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,wBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA/B,WAAA,GAAAkC,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAjE,IAAA;QACA,IAAA8D,MAAA,CAAA/B,WAAA,CAAAmB,MAAA;UACA;UACAY,MAAA,CAAAT,mBAAA;QACA;MACA,GAAAc,KAAA,WAAAC,KAAA;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACAnB,OAAA,CAAAiB,KAAA,qBAAAA,KAAA;;QAEA;QACA,IAAAA,KAAA,CAAAG,IAAA,aAAAF,cAAA,GAAAD,KAAA,CAAAjD,OAAA,cAAAkD,cAAA,eAAAA,cAAA,CAAAzB,QAAA,WAAA0B,eAAA,GAAAF,KAAA,CAAAjD,OAAA,cAAAmD,eAAA,eAAAA,eAAA,CAAA1B,QAAA;UACAkB,MAAA,CAAAU,QAAA,CAAAC,OAAA;QACA;;QAEA;QACAX,MAAA,CAAAT,mBAAA;MACA;IACA;IAEA;IACAA,mBAAA,WAAAA,oBAAA;MACAF,OAAA,CAAAC,GAAA;MAEA,IAAAsB,aAAA,IACA;QACA/C,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,GACA;QACAnD,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,GACA;QACAnD,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,EACA;;MAEA;MACA,IAAAC,aAAA,QAAAhD,WAAA,CAAAS,MAAA,WAAAwC,GAAA;QAAA,OAAAA,GAAA,CAAAF,QAAA;MAAA;MAEA,KAAA/C,WAAA,MAAAkD,MAAA,CAAAP,aAAA,MAAAQ,mBAAA,CAAApF,OAAA,EAAAiF,aAAA;MACA,KAAAP,QAAA,CAAAW,OAAA,uBAAApD,WAAA,CAAAmB,MAAA;IACA;IACAkC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACAlC,OAAA,CAAAC,GAAA;MACA,KAAAJ,eAAA;;MAEA;MACAC,UAAA;QACA,IAAAoC,MAAA,CAAAtD,WAAA,CAAAmB,MAAA;UACAC,OAAA,CAAAC,GAAA;UACAiC,MAAA,CAAAhC,mBAAA;QACA;UACAgC,MAAA,CAAAb,QAAA,CAAAW,OAAA;QACA;MACA;IACA;IAEA;IACAG,uBAAA,WAAAA,wBAAA;MACA,KAAArF,0BAAA;MACA,KAAAU,iBAAA;QACAZ,IAAA;QACAa,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;IACA;IAEA;IACAuE,SAAA,WAAAA,UAAAzE,MAAA;MACA,KAAAH,iBAAA,CAAAG,MAAA,GAAAA,MAAA,CAAAU,OAAA;MACA,KAAAvB,0BAAA;MACA,KAAAuE,QAAA,CAAAW,OAAA,sBAAAF,MAAA,CAAAnE,MAAA,CAAAe,KAAA;IACA;IAEA;IACA2D,cAAA,WAAAA,eAAA;MACA,KAAAC,MAAA,60HAmBA;QACAC,wBAAA;QACAC,iBAAA;QACAC,WAAA;MACA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAApF,iBAAA,CAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA1F,QAAA;UAEA,IAAA8F,WAAA;YACAtB,WAAA,EAAAkB,MAAA,CAAAnF,iBAAA,CAAAZ,IAAA;YACA8E,KAAA,EAAAiB,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;YACAC,WAAA,EAAAiF,MAAA,CAAAnF,iBAAA,CAAAE,WAAA;YACAC,MAAA,EAAAgF,MAAA,CAAAnF,iBAAA,CAAAG,MAAA;YACAmB,WAAA,EAAA6D,MAAA,CAAAnF,iBAAA,CAAAC,KAAA,GAAAkF,MAAA,CAAAnF,iBAAA,CAAAI,KAAA;YACAC,SAAA,EAAAmF,QAAA,CAAAL,MAAA,CAAAnF,iBAAA,CAAAK,SAAA;UACA;UAEA,IAAAoF,uBAAA,EAAAF,WAAA,EAAAlC,IAAA,WAAAC,QAAA;YACA6B,MAAA,CAAAtB,QAAA,CAAAW,OAAA;YACAW,MAAA,CAAA7F,0BAAA;YACA6F,MAAA,CAAA9C,eAAA;;YAEA;YACA,IAAA8C,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;cACAkF,MAAA,CAAAO,eAAA,CAAApC,QAAA,CAAAjE,IAAA,CAAA2E,SAAA;YACA;UACA,GAAAR,KAAA,WAAAC,KAAA;YACAjB,OAAA,CAAAiB,KAAA,sBAAAA,KAAA;;YAEA;YACA0B,MAAA,CAAAtB,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAA6B,WAAA;cACA3E,EAAA,EAAA4E,IAAA,CAAAC,GAAA;cACA7B,SAAA,EAAA4B,IAAA,CAAAC,GAAA;cACAzG,IAAA,EAAAmG,WAAA,CAAAtB,WAAA;cACAA,WAAA,EAAAsB,WAAA,CAAAtB,WAAA;cACAhE,KAAA,EAAAsF,WAAA,CAAArB,KAAA;cACAA,KAAA,EAAAqB,WAAA,CAAArB,KAAA;cACAhE,WAAA,EAAAqF,WAAA,CAAArF,WAAA;cACAC,MAAA,EAAAoF,WAAA,CAAApF,MAAA;cACAmB,WAAA,EAAAiE,WAAA,CAAAjE,WAAA;cACAC,cAAA;cACAlB,SAAA,EAAAkF,WAAA,CAAAlF,SAAA;cACAgB,MAAA;cACAG,UAAA,MAAAoE,IAAA,GAAAE,cAAA;cACA3B,QAAA;YACA;;YAEA;YACAgB,MAAA,CAAA/D,WAAA,CAAA2E,OAAA,CAAAJ,WAAA;YAEAR,MAAA,CAAAtB,QAAA,CAAAW,OAAA;YACAW,MAAA,CAAA7F,0BAAA;;YAEA;YACA,IAAA6F,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;cACAkF,MAAA,CAAAtB,QAAA,CAAAmC,IAAA;;cAEA;cACAb,MAAA,CAAAc,yBAAA,CAAAN,WAAA,CAAA3B,SAAA;YACA;YAEAmB,MAAA,CAAA1F,QAAA;UACA;QACA;MACA;IACA;IAEA;IACAiG,eAAA,WAAAA,gBAAA1B,SAAA;MAAA,IAAAkC,MAAA;MACA,IAAAC,OAAA,QAAA/E,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;QAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA;MAAA;MACA,IAAAmC,OAAA;QACA,IAAAE,gCAAA;UACArC,SAAA,EAAAA,SAAA;UACA9D,WAAA,EAAAiG,OAAA,CAAAjG,WAAA;UACAC,MAAA,EAAAgG,OAAA,CAAAhG,MAAA;UACAC,KAAA,EAAA+F,OAAA,CAAA7E,WAAA;UACAjB,SAAA,EAAA8F,OAAA,CAAA9F;QACA,GAAAgD,IAAA;UACA6C,MAAA,CAAArC,QAAA,CAAAW,OAAA;UACA0B,MAAA,CAAAI,eAAA,CAAAtC,SAAA;QACA,GAAAR,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,aAAAA,KAAA;UACAyC,MAAA,CAAArC,QAAA,CAAAJ,KAAA,gBAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA;IACA;IAEA;IACA8F,eAAA,WAAAA,gBAAAtC,SAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,cAAA,YAAAA,cAAA;QACA,IAAAC,wBAAA,EAAA1C,SAAA,EAAAX,IAAA,WAAAC,QAAA;UACA,IAAAqD,QAAA,GAAArD,QAAA,CAAAjE,IAAA;UACA,IAAA8G,OAAA,GAAAK,MAAA,CAAApF,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;YAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA;UAAA;UACA,IAAAmC,OAAA;YACAA,OAAA,CAAA5E,cAAA,GAAAoF,QAAA,CAAApF,cAAA;YACA4E,OAAA,CAAA9E,MAAA,GAAAsF,QAAA,CAAAtF,MAAA;YAEA,IAAAsF,QAAA,CAAAtF,MAAA;cACAiB,UAAA,CAAAmE,cAAA;YACA,WAAAE,QAAA,CAAAtF,MAAA;cACAmF,MAAA,CAAA3C,QAAA,CAAAW,OAAA,IAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA;YACA,WAAA0C,QAAA,CAAAtF,MAAA;cACAmF,MAAA,CAAA3C,QAAA,CAAAJ,KAAA,IAAAa,MAAA,CAAA6B,OAAA,CAAAlC,WAAA;YACA;UACA;QACA,GAAAT,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;QACA;MACA;MACAgD,cAAA;IACA;IAIA;IACAG,WAAA,WAAAA,YAAAT,OAAA;MACA,KAAAtG,cAAA,GAAAsG,OAAA;MACA,KAAAU,mBAAA,CAAAV,OAAA,CAAAnF,EAAA;MACA,KAAAxB,wBAAA;IACA;IAEA;IACAqH,mBAAA,WAAAA,oBAAA7C,SAAA;MAAA,IAAA8C,MAAA;MACA;MACA,SAAAC,qBAAA,SAAAA,qBAAA,CAAA/C,SAAA;QACA,KAAAlE,eAAA,QAAAiH,qBAAA,CAAA/C,SAAA;QACA,KAAAH,QAAA,CAAAW,OAAA,sBAAAF,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;QACAC,OAAA,CAAAC,GAAA,uBAAA3C,eAAA;QACA;MACA;;MAEA;MACA,IAAAkH,wBAAA,EAAAhD,SAAA,EAAAX,IAAA,WAAAC,QAAA;QACAwD,MAAA,CAAAhH,eAAA,GAAAwD,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAjE,IAAA;QACA,IAAAyH,MAAA,CAAAhH,eAAA,CAAAyC,MAAA;UACA;UACAuE,MAAA,CAAAG,gBAAA,CAAAjD,SAAA;QACA;MACA,GAAAR,KAAA,WAAAC,KAAA;QAAA,IAAAyD,eAAA,EAAAC,eAAA;QACA3E,OAAA,CAAAiB,KAAA,qBAAAA,KAAA;;QAEA;QACA,IAAAA,KAAA,CAAAG,IAAA,aAAAsD,eAAA,GAAAzD,KAAA,CAAAjD,OAAA,cAAA0G,eAAA,eAAAA,eAAA,CAAAjF,QAAA,WAAAkF,eAAA,GAAA1D,KAAA,CAAAjD,OAAA,cAAA2G,eAAA,eAAAA,eAAA,CAAAlF,QAAA;UACA6E,MAAA,CAAAjD,QAAA,CAAAC,OAAA;QACA;;QAEA;QACAgD,MAAA,CAAAG,gBAAA,CAAAjD,SAAA;MACA;IACA;IAEA;IACAiD,gBAAA,WAAAA,iBAAAjD,SAAA;MACAxB,OAAA,CAAAC,GAAA,wBAAAuB,SAAA;MAEA,IAAAoD,YAAA;QACA;QAAA;QACA;UACApG,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,GACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,EACA;QACA;QAAA;QACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,EACA;QACA;QAAA;QACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,GACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA;MAEA;MAEA,KAAA1B,eAAA,GAAAsH,YAAA,CAAApD,SAAA;MACA,KAAAH,QAAA,CAAAW,OAAA,sBAAAF,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;IACA;IAEA;IACA0D,yBAAA,WAAAA,0BAAAjC,SAAA;MAAA,IAAAwD,MAAA;MACAhF,OAAA,CAAAC,GAAA,wBAAAuB,SAAA;;MAEA;MACA,IAAAmC,OAAA,QAAA/E,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;QAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA,IAAAK,GAAA,CAAArD,EAAA,KAAAgD,SAAA;MAAA;MACA,KAAAmC,OAAA;QACA3D,OAAA,CAAAC,GAAA;QACA;MACA;;MAEA;MACA,UAAAsE,qBAAA;QACA,KAAAA,qBAAA;MACA;MACA,UAAAA,qBAAA,CAAA/C,SAAA;QACA,KAAA+C,qBAAA,CAAA/C,SAAA;MACA;MAEAmC,OAAA,CAAA9E,MAAA;;MAEA;MACA,IAAAoG,kBAAA,YAAAA,mBAAA;QAAA,IAAAC,KAAA,YAAAA,MAAAC,CAAA,EACA;UACArF,UAAA;YACA;YACA,IAAAsF,UAAA,GAAAJ,MAAA,CAAAK,mBAAA,CAAA1B,OAAA,EAAAwB,CAAA;;YAEA;YACAH,MAAA,CAAAT,qBAAA,CAAA/C,SAAA,EAAA8D,IAAA,CAAAF,UAAA;;YAEA;YACAzB,OAAA,CAAA5E,cAAA,GAAAoG,CAAA;YAEAH,MAAA,CAAA3D,QAAA,CAAAW,OAAA,wBAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA,IAAAkC,OAAA,CAAA/G,IAAA,gCAAAkF,MAAA,CAAAqD,CAAA;;YAEA;YACA,IAAAA,CAAA,KAAAxB,OAAA,CAAA7E,WAAA;cACA6E,OAAA,CAAA9E,MAAA;cACAmG,MAAA,CAAA3D,QAAA,CAAAW,OAAA,qCAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA,IAAAkC,OAAA,CAAA/G,IAAA,wDAAAkF,MAAA,CAAA6B,OAAA,CAAA5E,cAAA;;cAEA;cACAiG,MAAA,CAAAO,2BAAA;YACA;UACA,GAAAJ,CAAA;QACA;QAtBA,SAAAA,CAAA,MAAAA,CAAA,IAAAxB,OAAA,CAAA7E,WAAA,EAAAqG,CAAA;UAAAD,KAAA,CAAAC,CAAA;QAAA;MAuBA;;MAEA;MACArF,UAAA,CAAAmF,kBAAA;IACA;IAEA;IACAI,mBAAA,WAAAA,oBAAA1B,OAAA,EAAA6B,KAAA;MACA,IAAAC,eAAA,GAAA9B,OAAA,CAAA9F,SAAA;;MAEA;MACA,YAAA6H,oBAAA,CAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;IACA;IAEA;IACAE,+BAAA,WAAAA,gCAAAC,QAAA,EAAAjC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,QAAAG,QAAA;QACA;UAAA;UACA,YAAAF,oBAAA,CAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;QACA;UACA,YAAAI,yBAAA,CAAAlC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;UAAA;UACA,YAAAK,0BAAA,CAAAnC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;QACA;UACA,YAAAM,qBAAA,CAAApC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;UAAA;UACA,YAAAO,sBAAA,CAAArC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAAQ,gBAAA,IACA,SACA,WACA,QACA,QACA,QACA,UACA,SACA,SACA;MAEA,IAAAC,cAAA,OAAApE,MAAA,CACA6B,OAAA,CAAAjG,WAAA,gkBAKA;MAEA,IAAAyI,aAAA,GAAAF,gBAAA,CAAAG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAL,gBAAA,CAAAlG,MAAA;MACA,IAAA1B,OAAA,MAAAyD,MAAA,CAAAqE,aAAA,EAAArE,MAAA,CAAA6B,OAAA,CAAAhG,MAAA;;MAEA;MACA,OAAAU,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;QACA,IAAAc,QAAA,GAAAL,cAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAJ,cAAA,CAAAnG,MAAA;QACA1B,OAAA,IAAAkI,QAAA;QACA,IAAAlI,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;MACA;MAEA,YAAAe,mBAAA,CAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAwH,yBAAA,WAAAA,0BAAAlC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAAgB,SAAA;MACA,IAAAC,cAAA,OAAA5E,MAAA,CACA6B,OAAA,CAAAjG,WAAA,2BAAAoE,MAAA,CAAA2E,SAAA,CAAAL,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAG,SAAA,CAAA1G,MAAA,4SAKA;MAEA,IAAA1B,OAAA,GAAAqI,cAAA,CAAAlB,KAAA,GAAAkB,cAAA,CAAA3G,MAAA;;MAEA;MACA,IAAA4G,SAAA,GAAAP,IAAA,CAAAlI,GAAA,CAAAuH,eAAA;MACA,IAAApH,OAAA,CAAA0B,MAAA,GAAA4G,SAAA;QACAtI,OAAA,OAAAyD,MAAA,CAAA6B,OAAA,CAAAhG,MAAA;MACA;MAEA,YAAA6I,mBAAA,CAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAyH,0BAAA,WAAAA,2BAAAnC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAAmB,MAAA;MACA,IAAAC,mBAAA,IACA,iBACA,UACA,WACA,SACA,SACA;MAEA,IAAAxI,OAAA,MAAAyD,MAAA,CAAA+E,mBAAA,CAAArB,KAAA,GAAAqB,mBAAA,CAAA9G,MAAA,GAAA+B,MAAA,CAAA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA;MACA1B,OAAA,OAAAyD,MAAA,CAAA6B,OAAA,CAAAjG,WAAA,oDAAAoE,MAAA,CAAA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA;;MAEA;MACA,IAAA+G,QAAA,qCAAAhF,MAAA,CACA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA,kDAAA+B,MAAA,CACA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA,kDAAA+B,MAAA,CACA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA,kDAAA+B,MAAA,CACA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA,IACA;MAEA+G,QAAA,CAAAC,OAAA,WAAAC,OAAA;QACA3I,OAAA,OAAAyD,MAAA,CAAAkF,OAAA;MACA;MAEA3I,OAAA,SAAAyD,MAAA,CAAA6B,OAAA,CAAAhG,MAAA,cAAAmE,MAAA,CAAA8E,MAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,MAAA,CAAA7G,MAAA;MAEA,YAAAyG,mBAAA,CAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACA0H,qBAAA,WAAAA,sBAAApC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAAwB,WAAA;MACA,IAAAC,KAAA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAA7I,OAAA,gDAAAyD,MAAA,CAAA6B,OAAA,CAAAjG,WAAA,qBAAAoE,MAAA,CAAAmF,WAAA,CAAAzB,KAAA,GAAAyB,WAAA,CAAAlH,MAAA;MACA1B,OAAA;MACAA,OAAA,OAAAyD,MAAA,CAAA6B,OAAA,CAAAhG,MAAA;;MAEA;MACAwJ,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAH,OAAA,WAAAM,GAAA;QACA,IAAAjB,IAAA,CAAAE,MAAA;UAAA;UACAjI,OAAA,GAAAA,OAAA,CAAAiJ,OAAA,CAAAD,GAAA,EAAAH,KAAA,CAAAG,GAAA;QACA;MACA;MAEA,YAAAb,mBAAA,CAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACA2H,sBAAA,WAAAA,uBAAArC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAA8B,aAAA,oBAAAzF,MAAA,CACA6B,OAAA,CAAAjG,WAAA,sKAAAoE,MAAA,CACA6B,OAAA,CAAAjG,WAAA,8OAEA;MAEA,IAAAW,OAAA,GAAAkJ,aAAA,CAAA/B,KAAA,GAAA+B,aAAA,CAAAxH,MAAA;;MAEA;MACA,OAAA1B,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;QACApH,OAAA,qCAAAyD,MAAA,CAAA6B,OAAA,CAAAhG,MAAA;QACA,IAAAU,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;MACA;MAEA,YAAAe,mBAAA,CAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAmI,mBAAA,WAAAA,oBAAA7C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA,EAAAmJ,IAAA;MACA,IAAApC,UAAA;QACA5G,EAAA,EAAA4E,IAAA,CAAAC,GAAA,KAAAmC,KAAA;QACAX,SAAA,EAAAzB,IAAA,CAAAC,GAAA,KAAAmC,KAAA;QACAhE,SAAA,EAAAmC,OAAA,CAAAnC,SAAA,IAAAmC,OAAA,CAAAnF,EAAA;QACAH,OAAA,EAAAA,OAAA;QACAK,KAAA,oBAAAoD,MAAA,CAAA0F,IAAA,aAAA1F,MAAA,CAAA0D,KAAA;QACA3H,SAAA,EAAAQ,OAAA,CAAA0B,MAAA;QACA+E,aAAA;QACAjG,MAAA;QACAkG,YAAA,OAAAqB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACAtH,UAAA,MAAAoE,IAAA,GAAAE,cAAA;MACA;MAEAtD,OAAA,CAAAC,GAAA,sBAAA6B,MAAA,CAAA0D,KAAA,YAAA1D,MAAA,CAAA0F,IAAA,oBAAA1F,MAAA,CAAAzD,OAAA,CAAA0B,MAAA,eAAAqF,UAAA;MACA,OAAAA,UAAA;IACA;IAEA;IACAG,2BAAA,WAAAA,4BAAA;MACA;QACAkC,YAAA,CAAAC,OAAA,0BAAAC,IAAA,CAAAC,SAAA,MAAArD,qBAAA;QACAvE,OAAA,CAAAC,GAAA;MACA,SAAAgB,KAAA;QACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;MACA;IACA;IAEA;IACArB,6BAAA,WAAAA,8BAAA;MACA;QACA,IAAAiI,MAAA,GAAAJ,YAAA,CAAAK,OAAA;QACA,IAAAD,MAAA;UACA,KAAAtD,qBAAA,GAAAoD,IAAA,CAAAI,KAAA,CAAAF,MAAA;UACA7H,OAAA,CAAAC,GAAA,+BAAAsE,qBAAA;QACA;UACA,KAAAA,qBAAA;QACA;MACA,SAAAtD,KAAA;QACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;QACA,KAAAsD,qBAAA;MACA;IACA;IACA;IACAyD,YAAA,WAAAA,aAAArE,OAAA;MACA,KAAAtG,cAAA,GAAAsG,OAAA;MACA,KAAAvF,kBAAA;QACAX,KAAA;QACAC,WAAA,EAAAiG,OAAA,CAAAjG,WAAA;QACAC,MAAA,EAAAgG,OAAA,CAAAhG,MAAA;QACAC,KAAA;QACAS,OAAA;MACA;MACA,KAAAtB,2BAAA;IACA;IAEA;IACAkL,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAtF,KAAA,CAAAxE,kBAAA,CAAAyE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAoF,MAAA,CAAAhL,MAAA;UAEA,IAAAiL,WAAA;YACA3G,SAAA,EAAA0G,MAAA,CAAA7K,cAAA,CAAAmE,SAAA;YACAE,KAAA,EAAAwG,MAAA,CAAA9J,kBAAA,CAAAX,KAAA;YACAC,WAAA,EAAAwK,MAAA,CAAA9J,kBAAA,CAAAV,WAAA;YACAC,MAAA,EAAAuK,MAAA,CAAA9J,kBAAA,CAAAT,MAAA;YACAC,KAAA,EAAAsK,MAAA,CAAA9J,kBAAA,CAAAR,KAAA;YACAS,OAAA,EAAA6J,MAAA,CAAA9J,kBAAA,CAAAC;UACA;UAEA,IAAA+J,uBAAA,EAAAD,WAAA,EAAAtH,IAAA,WAAAC,QAAA;YACAoH,MAAA,CAAA7G,QAAA,CAAAW,OAAA,CAAAkG,MAAA,CAAA9J,kBAAA,CAAAX,KAAA,8BAAAqE,MAAA,CACAoG,MAAA,CAAA9J,kBAAA,CAAAR,KAAA;YACAsK,MAAA,CAAAnL,2BAAA;YACAmL,MAAA,CAAA7D,mBAAA,CAAA6D,MAAA,CAAA7K,cAAA,CAAAmE,SAAA;;YAEA;YACA0G,MAAA,CAAA7K,cAAA,CAAA0B,cAAA,IAAAmJ,MAAA,CAAA9J,kBAAA,CAAAX,KAAA,GACAyK,MAAA,CAAA9J,kBAAA,CAAAR,KAAA;UACA,GAAAoD,KAAA,WAAAC,KAAA;YACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;YACAiH,MAAA,CAAA7G,QAAA,CAAAJ,KAAA,cAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;UACA,GAAAqK,OAAA;YACAH,MAAA,CAAAhL,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAoL,iBAAA,WAAAA,kBAAA3E,OAAA;MAAA,IAAA4E,MAAA;MACA,KAAAC,QAAA;QACAhG,iBAAA;QACAiG,gBAAA;QACAjB,IAAA;MACA,GAAA3G,IAAA;QACA,IAAAyH,8BAAA,EAAA3E,OAAA,CAAAnC,SAAA,EAAAX,IAAA;UACA8C,OAAA,CAAA9E,MAAA;UACA8E,OAAA,CAAA5E,cAAA;UACAwJ,MAAA,CAAAlH,QAAA,CAAAW,OAAA;UACAuG,MAAA,CAAAzE,eAAA,CAAAH,OAAA,CAAAnC,SAAA;QACA,GAAAR,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;UACAsH,MAAA,CAAAlH,QAAA,CAAAJ,KAAA,cAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA;IACA;IAEA;IACA0K,aAAA,WAAAA,cAAA/E,OAAA;MAAA,IAAAgF,OAAA;MACA,KAAAH,QAAA;QACAhG,iBAAA;QACAiG,gBAAA;QACAjB,IAAA;MACA,GAAA3G,IAAA;QACA,IAAA+H,uBAAA,GAAAjF,OAAA,CAAAnC,SAAA,GAAAX,IAAA;UACA8H,OAAA,CAAAtH,QAAA,CAAAW,OAAA;UACA2G,OAAA,CAAA9I,eAAA;QACA,GAAAmB,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACA0H,OAAA,CAAAtH,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACA2H,OAAA,CAAAtH,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAqF,WAAA,WAAAA,YAAAxK,OAAA;MACA,KAAAiE,MAAA,CAAAjE,OAAA,CAAAA,OAAA;QACAmE,iBAAA;MACA;IACA;IAEA;IACAsG,WAAA,WAAAA,YAAAzK,OAAA;MAAA,IAAA0K,OAAA;MACAC,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAA7K,OAAA,CAAAA,OAAA,EAAAwC,IAAA;QACAkI,OAAA,CAAA1H,QAAA,CAAAW,OAAA;MACA,GAAAhB,KAAA;QACA+H,OAAA,CAAA1H,QAAA,CAAAJ,KAAA;MACA;IACA;IAEA;IACAkI,WAAA,WAAAA,YAAA9K,OAAA;MAAA,IAAA+K,OAAA;MACA,KAAAC,OAAA;QACA7G,iBAAA;QACAiG,gBAAA;QACAa,SAAA;QACAC,UAAA,EAAAlL,OAAA,CAAAA;MACA,GAAAwC,IAAA,WAAA2I,IAAA;QAAA,IAAAC,KAAA,GAAAD,IAAA,CAAAC,KAAA;QACA,IAAAC,UAAA;UACA7E,SAAA,EAAAxG,OAAA,CAAAwG,SAAA;UACAxG,OAAA,EAAAoL,KAAA;UACA5L,SAAA,EAAA4L,KAAA,CAAA1J;QACA;QAEA,IAAA4J,0BAAA,EAAAD,UAAA,EAAA7I,IAAA;UACAxC,OAAA,CAAAA,OAAA,GAAAoL,KAAA;UACApL,OAAA,CAAAR,SAAA,GAAA4L,KAAA,CAAA1J,MAAA;UACAqJ,OAAA,CAAA/H,QAAA,CAAAW,OAAA;QACA,GAAAhB,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACAmI,OAAA,CAAA/H,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACAoI,OAAA,CAAA/H,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAoG,aAAA,WAAAA,cAAAvL,OAAA;MAAA,IAAAwL,OAAA;MACA,KAAArB,QAAA;QACAhG,iBAAA;QACAiG,gBAAA;QACAjB,IAAA;MACA,GAAA3G,IAAA;QACA,IAAAiJ,uBAAA,GAAAzL,OAAA,CAAAwG,SAAA,GAAAhE,IAAA;UACAgJ,OAAA,CAAAxI,QAAA,CAAAW,OAAA;UACA6H,OAAA,CAAAxF,mBAAA,CAAAwF,OAAA,CAAAxM,cAAA,CAAAmE,SAAA;UACAqI,OAAA,CAAAxM,cAAA,CAAA0B,cAAA;QACA,GAAAiC,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACA4I,OAAA,CAAAxI,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACA6I,OAAA,CAAAxI,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAuG,aAAA,WAAAA,cAAApG,OAAA;MACA,IAAAtF,OAAA,8BAAAyD,MAAA,CAAA6B,OAAA,CAAA/G,IAAA;MACAyB,OAAA,qCAAAyD,MAAA,CAAA6B,OAAA,CAAA3E,UAAA;MACAX,OAAA,yBAAAyD,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;MAEA,KAAAzC,eAAA,CAAAyJ,OAAA,WAAAzH,IAAA,EAAAkG,KAAA;QACAnH,OAAA,OAAAyD,MAAA,CAAA0D,KAAA,YAAA1D,MAAA,CAAAxC,IAAA,CAAAjB,OAAA;QACAA,OAAA,wCAAAyD,MAAA,CAAAxC,IAAA,CAAAN,UAAA;MACA;MAEA,IAAAgL,IAAA,OAAAC,IAAA,EAAA5L,OAAA;QAAAmJ,IAAA;MAAA;MACA,IAAA0C,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;MACA,IAAAK,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,CAAA,CAAAI,QAAA,MAAA3I,MAAA,CAAA6B,OAAA,CAAA/G,IAAA;MACAyN,CAAA,CAAAK,KAAA;MACAP,GAAA,CAAAQ,eAAA,CAAAT,GAAA;MAEA,KAAA7I,QAAA,CAAAW,OAAA;IACA;IACA;IACA4I,aAAA,WAAAA,cAAA/L,MAAA;MACA,IAAAgM,SAAA;QACAC,OAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACA,OAAAJ,SAAA,CAAAhM,MAAA,KAAAA,MAAA;IACA;IAEA;IACAqM,cAAA,WAAAA,eAAArM,MAAA;MACA,IAAAsM,QAAA;QACAL,OAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACA,OAAAE,QAAA,CAAAtM,MAAA;IACA;EACA;AACA", "ignoreList": []}]}