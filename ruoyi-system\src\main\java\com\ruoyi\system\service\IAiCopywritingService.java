package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.AiCopywritingLibrary;
import com.ruoyi.system.domain.AiCopywritingContent;

/**
 * AI文案生成Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IAiCopywritingService 
{
    /**
     * 查询文案库列表
     * 
     * @param aiCopywritingLibrary 文案库
     * @return 文案库集合
     */
    public List<AiCopywritingLibrary> selectLibraryList(AiCopywritingLibrary aiCopywritingLibrary);

    /**
     * 查询文案库详细信息
     * 
     * @param libraryId 文案库主键
     * @return 文案库
     */
    public AiCopywritingLibrary selectLibraryById(Long libraryId);

    /**
     * 创建文案库
     * 
     * @param aiCopywritingLibrary 文案库
     * @return 结果
     */
    public int createLibrary(AiCopywritingLibrary aiCopywritingLibrary);

    /**
     * 修改文案库
     * 
     * @param aiCopywritingLibrary 文案库
     * @return 结果
     */
    public int updateLibrary(AiCopywritingLibrary aiCopywritingLibrary);

    /**
     * 批量删除文案库
     * 
     * @param libraryIds 需要删除的文案库主键集合
     * @return 结果
     */
    public int deleteLibraryByIds(Long[] libraryIds);

    /**
     * 异步生成文案
     * 
     * @param aiCopywritingLibrary 文案库信息
     */
    public void generateCopywritingAsync(AiCopywritingLibrary aiCopywritingLibrary);

    /**
     * 查询文案内容列表
     * 
     * @param libraryId 文案库ID
     * @return 文案内容集合
     */
    public List<AiCopywritingContent> selectContentByLibraryId(Long libraryId);

    /**
     * 新增文案内容
     * 
     * @param aiCopywritingContent 文案内容
     * @return 结果
     */
    public int insertContent(AiCopywritingContent aiCopywritingContent);

    /**
     * 生成单条文案内容
     * 
     * @param aiCopywritingContent 文案内容
     * @return 结果
     */
    public int generateSingleContent(AiCopywritingContent aiCopywritingContent);

    /**
     * 修改文案内容
     * 
     * @param aiCopywritingContent 文案内容
     * @return 结果
     */
    public int updateContent(AiCopywritingContent aiCopywritingContent);

    /**
     * 批量删除文案内容
     * 
     * @param contentIds 需要删除的文案内容主键集合
     * @return 结果
     */
    public int deleteContentByIds(Long[] contentIds);

    /**
     * 获取生成进度
     * 
     * @param libraryId 文案库ID
     * @return 进度信息
     */
    public Map<String, Object> getGenerationProgress(Long libraryId);

    /**
     * 重新生成文案库
     * 
     * @param libraryId 文案库ID
     */
    public void regenerateLibrary(Long libraryId);
}
