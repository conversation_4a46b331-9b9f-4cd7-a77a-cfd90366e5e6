<template>
  <div class="diy-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="small">返回</el-button>
        <span class="page-title">推广页面DIY编辑器</span>
      </div>
      <div class="toolbar-right">
        <el-button @click="previewPage" type="primary" icon="el-icon-view" size="small">预览</el-button>
        <el-button @click="savePage" type="success" icon="el-icon-check" size="small">保存</el-button>
      </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧：编辑面板 -->
      <div class="edit-panel">
        <el-tabs v-model="activeTab" tab-position="top">
          <!-- Banner设置 -->
          <el-tab-pane label="Banner设置" name="banner">
            <div class="setting-section">
              <h4>Banner背景图片</h4>
              <div class="image-upload">
                <el-upload
                  class="banner-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="handleBannerUpload"
                  accept="image/*"
                >
                  <img v-if="pageConfig.banner.backgroundImage" :src="pageConfig.banner.backgroundImage" class="banner-preview">
                  <i v-else class="el-icon-plus banner-uploader-icon"></i>
                </el-upload>
                <p class="upload-tip">建议尺寸：16:9 (如 800x450px)</p>
              </div>

              <h4>Banner文字</h4>
              <el-form-item label="主标题">
                <el-input v-model="pageConfig.banner.title" placeholder="碰一碰领福利"></el-input>
              </el-form-item>
              <el-form-item label="副标题">
                <el-input v-model="pageConfig.banner.subtitle" placeholder="请点击进行操作吧"></el-input>
              </el-form-item>
              <el-form-item label="标题颜色">
                <el-color-picker v-model="pageConfig.banner.titleColor"></el-color-picker>
              </el-form-item>
              <el-form-item label="副标题颜色">
                <el-color-picker v-model="pageConfig.banner.subtitleColor"></el-color-picker>
              </el-form-item>

              <!-- 测试按钮 -->
              <el-form-item>
                <el-button type="primary" size="small" @click="testBannerUpdate">测试Banner更新</el-button>
                <el-button type="success" size="small" @click="resetBanner">重置Banner</el-button>
              </el-form-item>
            </div>
          </el-tab-pane>

          <!-- 门店信息设置 -->
          <el-tab-pane label="门店信息" name="store">
            <div class="setting-section">
              <h4>门店Logo</h4>
              <div class="image-upload">
                <el-upload
                  class="logo-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="handleLogoUpload"
                  accept="image/*"
                >
                  <img v-if="pageConfig.store.logo" :src="pageConfig.store.logo" class="logo-preview">
                  <i v-else class="el-icon-plus logo-uploader-icon"></i>
                </el-upload>
              </div>

              <el-form-item label="门店名称">
                <el-input v-model="pageConfig.store.name"></el-input>
              </el-form-item>
              <el-form-item label="门店名称颜色">
                <el-color-picker v-model="pageConfig.store.nameColor"></el-color-picker>
              </el-form-item>
              <el-form-item label="促销文字">
                <el-input v-model="pageConfig.store.promotionText"></el-input>
              </el-form-item>
              <el-form-item label="促销文字颜色">
                <el-color-picker v-model="pageConfig.store.promotionColor"></el-color-picker>
              </el-form-item>
            </div>
          </el-tab-pane>

          <!-- 平台图标设置 -->
          <el-tab-pane label="平台图标" name="platforms">
            <div class="setting-section">
              <div class="section-header">
                <h4>短视频平台</h4>
                <div>
                  <el-checkbox v-model="pageConfig.videoPlatforms.enabled">显示短视频平台</el-checkbox>
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="addPlatform('videoPlatforms')">
                    添加平台
                  </el-button>
                </div>
              </div>
              <div v-if="pageConfig.videoPlatforms.enabled" v-for="(platform, index) in pageConfig.videoPlatforms.items" :key="'video-' + index" class="platform-item">
                <div class="platform-header">
                  <span>{{ platform.displayName || platform.name }}</span>
                  <div class="platform-actions">
                    <el-switch v-model="platform.enabled" size="mini"></el-switch>
                    <el-button type="text" size="mini" icon="el-icon-edit" @click="editPlatform('videoPlatforms', index)"></el-button>
                    <el-button type="text" size="mini" icon="el-icon-delete" @click="removePlatform('videoPlatforms', index)"></el-button>
                  </div>
                </div>
                <div class="platform-settings" v-if="platform.enabled">
                  <div class="image-upload-small">
                    <el-upload
                      class="platform-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => handlePlatformIconUpload(file, 'videoPlatforms', index)"
                      accept="image/*"
                    >
                      <img v-if="platform.icon" :src="platform.icon" class="platform-icon-preview">
                      <i v-else class="el-icon-plus platform-uploader-icon"></i>
                    </el-upload>
                  </div>
                  <el-input v-model="platform.displayName" placeholder="显示名称" size="small"></el-input>
                  <el-color-picker v-model="platform.color" size="small"></el-color-picker>
                </div>
              </div>

              <div class="section-header">
                <h4>图文/点评平台</h4>
                <div>
                  <el-checkbox v-model="pageConfig.reviewPlatforms.enabled">显示图文平台</el-checkbox>
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="addPlatform('reviewPlatforms')">
                    添加平台
                  </el-button>
                </div>
              </div>
              <div v-if="pageConfig.reviewPlatforms.enabled" v-for="(platform, index) in pageConfig.reviewPlatforms.items" :key="'review-' + index" class="platform-item">
                <div class="platform-header">
                  <span>{{ platform.displayName || platform.name }}</span>
                  <div class="platform-actions">
                    <el-switch v-model="platform.enabled" size="mini"></el-switch>
                    <el-button type="text" size="mini" icon="el-icon-edit" @click="editPlatform('reviewPlatforms', index)"></el-button>
                    <el-button type="text" size="mini" icon="el-icon-delete" @click="deletePlatform('reviewPlatforms', index)"></el-button>
                  </div>
                </div>
                <div class="platform-settings" v-if="platform.enabled">
                  <div class="image-upload-small">
                    <el-upload
                      class="platform-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => handlePlatformIconUpload(file, 'reviewPlatforms', index)"
                      accept="image/*"
                    >
                      <img v-if="platform.icon" :src="platform.icon" class="platform-icon-preview">
                      <i v-else class="el-icon-plus platform-uploader-icon"></i>
                    </el-upload>
                  </div>
                  <el-input v-model="platform.displayName" placeholder="显示名称" size="small"></el-input>
                  <el-color-picker v-model="platform.color" size="small"></el-color-picker>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 产品设置 -->
          <el-tab-pane label="产品信息" name="product">
            <div class="setting-section">
              <div class="section-header">
                <h4>产品展示</h4>
                <div>
                  <el-checkbox v-model="pageConfig.products.enabled">显示产品区域</el-checkbox>
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">
                    添加产品
                  </el-button>
                </div>
              </div>

              <div v-if="pageConfig.products.enabled">
                <el-form-item label="自动切换">
                  <el-checkbox v-model="pageConfig.products.autoSwitch">启用自动切换</el-checkbox>
                </el-form-item>
                <el-form-item v-if="pageConfig.products.autoSwitch" label="切换间隔(秒)">
                  <el-input-number v-model="pageConfig.products.switchInterval" :min="2" :max="10"></el-input-number>
                </el-form-item>

                <div v-for="(product, index) in pageConfig.products.items" :key="'product-' + index" class="product-item">
                  <div class="product-header">
                    <span>产品 {{ index + 1 }}: {{ product.name }}</span>
                    <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeProduct(index)" circle></el-button>
                  </div>

                  <div class="product-settings">
                    <div class="image-upload">
                      <el-upload
                        class="product-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="(file) => handleProductUpload(file, index)"
                        accept="image/*"
                      >
                        <img v-if="product.image" :src="product.image" class="product-preview">
                        <i v-else class="el-icon-plus product-uploader-icon"></i>
                      </el-upload>
                    </div>

                    <el-form-item label="产品名称">
                      <el-input v-model="product.name"></el-input>
                    </el-form-item>
                    <el-form-item label="产品描述">
                      <el-input v-model="product.description" type="textarea" :rows="2"></el-input>
                    </el-form-item>
                    <el-form-item label="原价">
                      <el-input v-model="product.originalPrice" placeholder="￥99.00"></el-input>
                    </el-form-item>
                    <el-form-item label="现价">
                      <el-input v-model="product.currentPrice" placeholder="￥79.00"></el-input>
                    </el-form-item>
                    <el-form-item label="团购链接">
                      <el-input v-model="product.groupBuyUrl" placeholder="https://..."></el-input>
                    </el-form-item>
                    <el-form-item label="状态标签">
                      <el-input v-model="product.status" placeholder="🔥 热销中"></el-input>
                    </el-form-item>
                    <el-form-item label="按钮文字">
                      <el-input v-model="product.buttonText" placeholder="立即购买"></el-input>
                    </el-form-item>

                    <div class="color-settings">
                      <el-form-item label="名称颜色">
                        <el-color-picker v-model="product.nameColor"></el-color-picker>
                      </el-form-item>
                      <el-form-item label="描述颜色">
                        <el-color-picker v-model="product.descriptionColor"></el-color-picker>
                      </el-form-item>
                      <el-form-item label="价格颜色">
                        <el-color-picker v-model="product.priceColor"></el-color-picker>
                      </el-form-item>
                      <el-form-item label="状态颜色">
                        <el-color-picker v-model="product.statusColor"></el-color-picker>
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- WiFi设置 -->
          <el-tab-pane label="WiFi信息" name="wifi">
            <div class="setting-section">
              <el-form-item>
                <el-checkbox v-model="pageConfig.wifi.enabled">启用WiFi信息</el-checkbox>
              </el-form-item>
              
              <div v-if="pageConfig.wifi.enabled">
                <el-form-item label="WiFi名称">
                  <el-input v-model="pageConfig.wifi.name"></el-input>
                </el-form-item>
                <el-form-item label="WiFi密码">
                  <el-input v-model="pageConfig.wifi.password"></el-input>
                </el-form-item>
                <el-form-item label="按钮文字">
                  <el-input v-model="pageConfig.wifi.buttonText"></el-input>
                </el-form-item>
                <el-form-item label="文字颜色">
                  <el-color-picker v-model="pageConfig.wifi.textColor"></el-color-picker>
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 右侧：预览面板 -->
      <div class="preview-panel">
        <div class="phone-frame">
          <div class="volume-buttons"></div>
          <div class="phone-screen">
            <div class="preview-content" :style="previewStyles">
              <!-- Banner区域 -->
              <div class="preview-banner" :style="bannerStyles">
                <div class="banner-content">
                  <h1 :style="{ color: pageConfig.banner.titleColor }">{{ pageConfig.banner.title }}</h1>
                  <p :style="{ color: pageConfig.banner.subtitleColor }">{{ pageConfig.banner.subtitle }}</p>
                </div>
              </div>

              <!-- 门店信息 -->
              <div class="preview-store">
                <img :src="pageConfig.store.logo" class="store-logo-preview" alt="门店logo">
                <span :style="{ color: pageConfig.store.nameColor }">{{ pageConfig.store.name }}</span>
                <div class="promotion-badge" :style="{ color: pageConfig.store.promotionColor }">
                  {{ pageConfig.store.promotionText }}
                </div>
              </div>

              <!-- 产品信息 -->
              <div v-if="pageConfig.products.enabled && pageConfig.products.items.length > 0" class="preview-products">
                <div class="product-carousel">
                  <div
                    v-for="(product, index) in pageConfig.products.items"
                    :key="'preview-product-' + index"
                    v-show="index === currentProductIndex"
                    class="product-slide"
                    @click="openGroupBuy(product.groupBuyUrl)"
                  >
                    <div class="product-image-container">
                      <img :src="product.image" class="product-image-preview" alt="产品图片">
                      <div class="product-status" :style="{ color: product.statusColor }">
                        {{ product.status }}
                      </div>
                    </div>
                    <div class="product-info-preview">
                      <div class="product-name" :style="{ color: product.nameColor }">{{ product.name }}</div>
                      <div class="product-description" :style="{ color: product.descriptionColor }">{{ product.description }}</div>
                      <div class="product-price">
                        <span class="current-price" :style="{ color: product.priceColor }">{{ product.currentPrice }}</span>
                        <span class="original-price">{{ product.originalPrice }}</span>
                      </div>
                      <button class="buy-button" :style="{ backgroundColor: product.priceColor }">
                        {{ product.buttonText }}
                      </button>
                    </div>
                  </div>

                  <!-- 轮播指示器 -->
                  <div v-if="pageConfig.products.items.length > 1" class="carousel-indicators">
                    <span
                      v-for="(product, index) in pageConfig.products.items"
                      :key="'indicator-' + index"
                      :class="{ active: index === currentProductIndex }"
                      @click="currentProductIndex = index"
                    ></span>
                  </div>
                </div>
              </div>

              <!-- 平台图标 -->
              <div class="preview-platforms">
                <div v-if="pageConfig.videoPlatforms.enabled && enabledVideoPlatforms.length > 0" class="platform-section">
                  <h4>短视频平台</h4>
                  <div class="platform-grid">
                    <div v-for="platform in enabledVideoPlatforms" :key="platform.name" class="platform-item-preview">
                      <img v-if="platform.icon" :src="platform.icon" :alt="platform.displayName">
                      <div v-else class="platform-icon-placeholder" :style="{ backgroundColor: platform.color }">
                        {{ platform.displayName.charAt(0) }}
                      </div>
                      <span :style="{ color: platform.color }">{{ platform.displayName }}</span>
                    </div>
                  </div>
                </div>

                <div v-if="pageConfig.reviewPlatforms.enabled && enabledReviewPlatforms.length > 0" class="platform-section">
                  <h4>图文/点评平台</h4>
                  <div class="platform-grid">
                    <div v-for="platform in enabledReviewPlatforms" :key="platform.name" class="platform-item-preview">
                      <img v-if="platform.icon" :src="platform.icon" :alt="platform.displayName">
                      <div v-else class="platform-icon-placeholder" :style="{ backgroundColor: platform.color }">
                        {{ platform.displayName.charAt(0) }}
                      </div>
                      <span :style="{ color: platform.color }">{{ platform.displayName }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- WiFi信息 -->
              <div v-if="pageConfig.wifi.enabled" class="preview-wifi">
                <div :style="{ color: pageConfig.wifi.textColor }">
                  <div>Wi-Fi: {{ pageConfig.wifi.name }}</div>
                  <div>密码: {{ pageConfig.wifi.password }}</div>
                  <button class="wifi-btn">{{ pageConfig.wifi.buttonText }}</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionDIY',
  data() {
    return {
      activeTab: 'banner',
      currentProductIndex: 0,
      productTimer: null,
      pageConfig: {
        banner: {
          backgroundImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=450&fit=crop',
          title: '碰一碰领福利',
          subtitle: '请点击进行操作吧',
          titleColor: '#ffffff',
          subtitleColor: '#f0f0f0'
        },
        store: {
          logo: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          name: '星巴克咖啡店',
          nameColor: '#333333',
          promotionText: '🔥 新店开业，全场8折优惠',
          promotionColor: '#ff4757'
        },
        products: {
          enabled: true,
          autoSwitch: true,
          switchInterval: 3,
          items: [
            {
              image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=200&fit=crop',
              name: '经典美式咖啡',
              description: '精选优质咖啡豆，香醇浓郁',
              originalPrice: '￥35.00',
              currentPrice: '￥28.00',
              status: '🔥 热销中',
              buttonText: '立即购买',
              groupBuyUrl: 'https://example.com/coffee',
              nameColor: '#333333',
              descriptionColor: '#666666',
              priceColor: '#ff4757',
              statusColor: '#ff6b6b'
            },
            {
              image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=200&fit=crop',
              name: '拿铁咖啡',
              description: '丝滑奶泡与浓郁咖啡的完美结合',
              originalPrice: '￥38.00',
              currentPrice: '￥30.00',
              status: '⭐ 推荐',
              buttonText: '立即购买',
              groupBuyUrl: 'https://example.com/latte',
              nameColor: '#333333',
              descriptionColor: '#666666',
              priceColor: '#ff4757',
              statusColor: '#ffa502'
            }
          ]
        },
        videoPlatforms: {
          enabled: true,
          items: [
            { name: 'douyin', displayName: '抖音', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2ZlMmM1NSIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuaKlzwvdGV4dD48L3N2Zz4=', color: '#fe2c55', enabled: true },
            { name: 'kuaishou', displayName: '快手', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2ZmNjM0OCIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuW/qzwvdGV4dD48L3N2Zz4=', color: '#ff6348', enabled: true },
            { name: 'xiaohongshu', displayName: '小红书', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2ZmNDc1NyIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWwj+e6ojwvdGV4dD48L3N2Zz4=', color: '#ff4757', enabled: true },
            { name: 'shipinhao', displayName: '视频号', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzA3YzE2MCIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuinhumihjwvdGV4dD48L3N2Zz4=', color: '#07c160', enabled: true }
          ]
        },
        reviewPlatforms: {
          enabled: true,
          items: [
            { name: 'meituan', displayName: '美团', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2ZmYzEwNyIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMiIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiPue+jzwvdGV4dD48L3N2Zz4=', color: '#ffc107', enabled: true },
            { name: 'dianping', displayName: '大众点评', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI2ZmNjYwMCIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPueCuei+hzwvdGV4dD48L3N2Zz4=', color: '#ff6600', enabled: true },
            { name: 'gaode', displayName: '高德', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwYTZmYiIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPumrmzwvdGV4dD48L3N2Zz4=', color: '#00a6fb', enabled: true },
            { name: 'baidu', displayName: '百度', icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzI5MzJlMSIvPjx0ZXh0IHg9IjEyIiB5PSIxNiIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuecvzwvdGV4dD48L3N2Zz4=', color: '#2932e1', enabled: true }
          ]
        },
        wifi: {
          enabled: true,
          name: 'Starbucks_Free_WiFi',
          password: 'coffee2024',
          buttonText: '一键连接WiFi',
          textColor: '#333333'
        }
      }
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },
    previewStyles() {
      return {
        fontFamily: 'Helvetica Neue, Arial, sans-serif'
      }
    },
    bannerStyles() {
      return {
        backgroundImage: `url(${this.pageConfig.banner.backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        aspectRatio: '16/9',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        position: 'relative'
      }
    },
    enabledVideoPlatforms() {
      return this.pageConfig.videoPlatforms.items.filter(p => p.enabled)
    },
    enabledReviewPlatforms() {
      return this.pageConfig.reviewPlatforms.items.filter(p => p.enabled)
    }
  },
  mounted() {
    console.log('DIY页面初始配置:', this.pageConfig)
    this.loadPageConfig()
    this.startProductCarousel()
    // 强制触发响应式更新
    this.$nextTick(() => {
      console.log('DIY页面最终配置:', this.pageConfig)
      console.log('Banner配置:', this.pageConfig.banner)
      console.log('Banner背景图:', this.pageConfig.banner.backgroundImage)
      this.$forceUpdate()
    })
  },
  beforeDestroy() {
    this.stopProductCarousel()
  },
  methods: {
    // 加载页面配置
    loadPageConfig() {
      const configKey = `promotion_config_${this.storeId}`
      const savedConfig = localStorage.getItem(configKey)

      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig)
          this.pageConfig = { ...this.pageConfig, ...parsedConfig }
        } catch (error) {
          console.error('加载配置失败:', error)
        }
      }
    },

    // 保存页面配置
    savePage() {
      const configKey = `promotion_config_${this.storeId}`
      localStorage.setItem(configKey, JSON.stringify(this.pageConfig))
      this.$message.success('页面配置已保存')
    },

    // 预览页面
    previewPage() {
      this.savePage()
      const previewUrl = `/promotion/${this.storeId}`
      window.open(this.$router.resolve({ path: previewUrl }).href, '_blank')
    },

    // 返回
    goBack() {
      this.$router.push('/storer/store')
    },

    // 处理Banner图片上传
    handleBannerUpload(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.pageConfig.banner.backgroundImage = e.target.result
      }
      reader.readAsDataURL(file)
      return false // 阻止自动上传
    },

    // 处理Logo上传
    handleLogoUpload(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.pageConfig.store.logo = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    // 处理产品图片上传
    handleProductUpload(file, index) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.pageConfig.products.items[index].image = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    // 添加产品
    addProduct() {
      const newProduct = {
        image: '',
        name: '新产品',
        description: '产品描述',
        originalPrice: '￥99.00',
        currentPrice: '￥79.00',
        status: '🔥 热销中',
        buttonText: '立即购买',
        groupBuyUrl: '',
        nameColor: '#333333',
        descriptionColor: '#666666',
        priceColor: '#ff4757',
        statusColor: '#ff6b6b'
      }
      this.pageConfig.products.items.push(newProduct)
      this.$message.success('产品添加成功')
    },

    // 删除产品
    removeProduct(index) {
      if (this.pageConfig.products.items.length <= 1) {
        this.$message.warning('至少需要保留一个产品')
        return
      }

      this.$confirm('确定要删除这个产品吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageConfig.products.items.splice(index, 1)
        // 调整当前显示的产品索引
        if (this.currentProductIndex >= this.pageConfig.products.items.length) {
          this.currentProductIndex = 0
        }
        this.$message.success('产品删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 开始产品轮播
    startProductCarousel() {
      this.stopProductCarousel()
      if (this.pageConfig.products.autoSwitch && this.pageConfig.products.items.length > 1) {
        this.productTimer = setInterval(() => {
          this.currentProductIndex = (this.currentProductIndex + 1) % this.pageConfig.products.items.length
        }, this.pageConfig.products.switchInterval * 1000)
      }
    },

    // 停止产品轮播
    stopProductCarousel() {
      if (this.productTimer) {
        clearInterval(this.productTimer)
        this.productTimer = null
      }
    },

    // 打开团购链接
    openGroupBuy(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.info('请先设置团购链接')
      }
    },

    // 测试Banner更新
    testBannerUpdate() {
      this.pageConfig.banner.title = '测试标题 - ' + new Date().getTime()
      this.pageConfig.banner.subtitle = '测试副标题 - ' + new Date().getTime()
      this.$forceUpdate()
      this.$message.success('Banner已更新')
    },

    // 重置Banner
    resetBanner() {
      this.pageConfig.banner.title = '碰一碰领福利'
      this.pageConfig.banner.subtitle = '请点击进行操作吧'
      this.$forceUpdate()
      this.$message.success('Banner已重置')
    },

    // 处理平台图标上传
    handlePlatformIconUpload(file, platformType, index) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.pageConfig[platformType].items[index].icon = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    // 添加平台
    addPlatform(platformType) {
      const newPlatform = {
        name: `platform_${Date.now()}`,
        displayName: '新平台',
        icon: '',
        color: '#409eff',
        enabled: true
      }
      this.pageConfig[platformType].items.push(newPlatform)
      this.$message.success('平台添加成功')
    },

    // 删除平台
    deletePlatform(platformType, index) {
      this.$confirm('确定要删除这个平台吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageConfig[platformType].items.splice(index, 1)
        this.$message.success('平台删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 删除平台（别名方法）
    removePlatform(platformType, index) {
      this.deletePlatform(platformType, index)
    },

    // 编辑平台
    editPlatform(platformType, index) {
      const platform = this.pageConfig[platformType].items[index]
      this.$prompt('请输入平台名称', '编辑平台', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: platform.displayName
      }).then(({ value }) => {
        platform.displayName = value
        this.$message.success('平台名称修改成功')
      }).catch(() => {
        this.$message.info('已取消编辑')
      })
    }
  },
  watch: {
    'pageConfig.products.autoSwitch'() {
      this.startProductCarousel()
    },
    'pageConfig.products.switchInterval'() {
      this.startProductCarousel()
    }
  }
}
</script>

<style lang="scss" scoped>
.diy-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

// 工具栏样式
.toolbar {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 12px;
  }
}

// 编辑器布局
.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧编辑面板
.edit-panel {
  width: 400px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;

  .el-tabs {
    height: 100%;

    ::v-deep .el-tabs__content {
      height: calc(100% - 55px);
      overflow-y: auto;
      padding: 20px;
    }
  }
}

// 设置区域
.setting-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .el-form-item {
    margin-bottom: 16px;
  }
}

// 图片上传样式
.image-upload {
  margin-bottom: 20px;
}

.banner-uploader, .logo-uploader, .product-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
    }
  }
}

.banner-uploader {
  ::v-deep .el-upload {
    width: 320px;
    height: 180px;
  }
}

.logo-uploader {
  ::v-deep .el-upload {
    width: 80px;
    height: 80px;
  }
}

.product-uploader {
  ::v-deep .el-upload {
    width: 120px;
    height: 120px;
  }
}

.banner-preview, .logo-preview, .product-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-uploader-icon, .logo-uploader-icon, .product-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-tip {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #999;
}

// 产品设置样式
.product-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  background: #fafafa;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 600;
    color: #303133;
  }

  .product-settings {
    .color-settings {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
    }
  }
}

// 平台设置样式
.platform-item {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;

  .platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 600;
  }

  .platform-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .platform-settings {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.image-upload-small {
  .platform-uploader {
    ::v-deep .el-upload {
      width: 40px;
      height: 40px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .platform-icon-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .platform-uploader-icon {
    font-size: 16px;
    color: #8c939d;
  }
}

// 右侧预览面板
.preview-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 手机框架 - iPhone 16 样式
.phone-frame {
  width: 390px;
  height: 844px;
  background: #000;
  border-radius: 50px;
  padding: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  position: relative;
  border: 1px solid #222;
  overflow: hidden;

  // 动态岛
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 35px;
    background: #000;
    border-radius: 20px;
    z-index: 10;
  }

  // 侧边按钮
  &::after {
    content: '';
    position: absolute;
    top: 120px;
    right: -2px;
    width: 4px;
    height: 80px;
    background: #222;
    border-radius: 2px 0 0 2px;
  }

  // 音量按钮
  .volume-buttons {
    position: absolute;
    top: 150px;
    left: -2px;
    width: 4px;
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    &::before, &::after {
      content: '';
      width: 4px;
      height: 40px;
      background: #222;
      border-radius: 0 2px 2px 0;
    }
  }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

// 预览内容样式
.preview-content {
  height: 100%;
  overflow-y: auto;
}

.preview-banner {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
  }

  .banner-content {
    position: relative;
    z-index: 1;
    padding: 40px 20px;

    h1 {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 8px 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }

    p {
      font-size: 16px;
      margin: 0;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }
  }
}

.preview-store {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255,255,255,0.95);

  .store-logo-preview {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
  }

  .promotion-badge {
    margin-left: auto;
    padding: 4px 12px;
    background: rgba(255,71,87,0.1);
    border-radius: 12px;
    font-size: 12px;
  }
}

.preview-products {
  padding: 20px;
  background: #f8f9fa;

  .product-carousel {
    position: relative;

    .product-slide {
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }
    }

    .product-image-container {
      position: relative;

      .product-image-preview {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
      }

      .product-status {
        position: absolute;
        top: 4px;
        right: 4px;
        background: rgba(255,255,255,0.9);
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 600;
      }
    }

    .product-info-preview {
      flex: 1;

      .product-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .product-description {
        font-size: 12px;
        margin-bottom: 8px;
        opacity: 0.8;
      }

      .product-price {
        margin-bottom: 12px;

        .current-price {
          font-size: 18px;
          font-weight: bold;
          margin-right: 8px;
        }

        .original-price {
          font-size: 14px;
          text-decoration: line-through;
          opacity: 0.6;
        }
      }

      .buy-button {
        background: #ff4757;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .carousel-indicators {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 16px;

      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ddd;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #ff4757;
          transform: scale(1.2);
        }
      }
    }
  }
}

.preview-platforms {
  padding: 20px;

  .platform-section {
    margin-bottom: 20px;

    h4 {
      font-size: 16px;
      margin: 0 0 12px 0;
      color: #303133;
    }

    .platform-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .platform-item-preview {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      img, .platform-icon-placeholder {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        object-fit: cover;
      }

      .platform-icon-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: bold;
      }

      span {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

.preview-wifi {
  padding: 20px;
  background: rgba(240,240,240,0.5);
  text-align: center;

  div {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .wifi-btn {
    background: #409eff;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
</script>
