<template>
  <div class="quick-test-container">
    <div class="test-card">
      <h2>🚀 百度AI快速测试</h2>
      <p>测试百度智能云v2版本DeepSeek接口</p>
      
      <div class="test-section">
        <h3>1. 配置验证</h3>
        <el-button type="primary" @click="validateConfig" :loading="validating">
          验证API配置
        </el-button>
        <div v-if="configResult" class="result-box" :class="configResult.type">
          {{ configResult.message }}
        </div>
      </div>

      <div class="test-section">
        <h3>2. 快速生成测试</h3>
        <el-input 
          v-model="testPrompt" 
          placeholder="输入测试提示词..."
          style="margin-bottom: 10px;"
        ></el-input>
        <el-button type="success" @click="quickTest" :loading="testing">
          快速测试生成
        </el-button>
        <div v-if="testResult" class="result-box success">
          <strong>生成结果：</strong><br>
          {{ testResult }}
        </div>
      </div>

      <div class="test-section">
        <h3>3. 模型信息</h3>
        <el-button type="info" @click="getModelInfo" :loading="loadingModel">
          获取模型信息
        </el-button>
        <div v-if="modelInfo" class="result-box info">
          <div v-for="(value, key) in modelInfo" :key="key">
            <strong>{{ key }}:</strong> {{ value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'QuickTest',
  data() {
    return {
      validating: false,
      testing: false,
      loadingModel: false,
      configResult: null,
      testResult: '',
      modelInfo: null,
      testPrompt: '生成一条简单的咖啡店推广文案'
    }
  },
  methods: {
    validateConfig() {
      this.validating = true
      this.configResult = null
      
      request({
        url: '/ai/test/validate-config',
        method: 'get'
      }).then(response => {
        this.configResult = {
          type: 'success',
          message: '✅ ' + (response.msg || '配置验证成功')
        }
      }).catch(error => {
        this.configResult = {
          type: 'error',
          message: '❌ ' + (error.msg || error.message || '配置验证失败')
        }
      }).finally(() => {
        this.validating = false
      })
    },

    quickTest() {
      this.testing = true
      this.testResult = ''
      
      request({
        url: '/ai/test/generate',
        method: 'post',
        data: {
          prompt: this.testPrompt,
          shopDetails: '测试咖啡店：温馨舒适的环境，提供精品咖啡和手工甜点'
        },
        timeout: 30000
      }).then(response => {
        this.testResult = response.data.generatedContent
        this.$message.success('生成成功！')
      }).catch(error => {
        this.$message.error('生成失败：' + (error.msg || error.message))
        this.testResult = '生成失败：' + (error.msg || error.message)
      }).finally(() => {
        this.testing = false
      })
    },

    getModelInfo() {
      this.loadingModel = true
      this.modelInfo = null
      
      request({
        url: '/ai/test/model-info',
        method: 'get'
      }).then(response => {
        this.modelInfo = response.data
      }).catch(error => {
        this.$message.error('获取模型信息失败：' + (error.msg || error.message))
      }).finally(() => {
        this.loadingModel = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.quick-test-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  max-width: 600px;
  width: 100%;

  h2 {
    text-align: center;
    color: #333;
    margin-bottom: 10px;
  }

  p {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
  }
}

.test-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  h3 {
    color: #333;
    margin-bottom: 15px;
  }
}

.result-box {
  margin-top: 15px;
  padding: 15px;
  border-radius: 6px;
  line-height: 1.6;

  &.success {
    background: #f0f9ff;
    border: 1px solid #67c23a;
    color: #67c23a;
  }

  &.error {
    background: #fef0f0;
    border: 1px solid #f56c6c;
    color: #f56c6c;
  }

  &.info {
    background: #f4f4f5;
    border: 1px solid #909399;
    color: #606266;
  }
}

@media (max-width: 768px) {
  .test-card {
    padding: 20px;
    margin: 10px;
  }
}
</style>
