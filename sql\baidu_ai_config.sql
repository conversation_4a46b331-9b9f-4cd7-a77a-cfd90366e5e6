-- 百度AI配置表
CREATE TABLE IF NOT EXISTS `baidu_ai_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_key` varchar(255) NOT NULL COMMENT 'API Key',
  `secret_key` varchar(255) NOT NULL COMMENT 'Secret Key',
  `model_name` varchar(100) DEFAULT 'ERNIE-Bot-turbo' COMMENT '模型名称',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `max_tokens` int(11) DEFAULT 2000 COMMENT '最大token数',
  `temperature` decimal(3,2) DEFAULT 0.95 COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT 0.8 COMMENT 'top_p参数',
  `penalty_score` decimal(3,2) DEFAULT 1.0 COMMENT '惩罚分数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='百度AI配置表';

-- 插入默认配置（请替换为真实的API Key和Secret Key）
INSERT INTO `baidu_ai_config` (`api_key`, `secret_key`, `model_name`, `is_enabled`, `create_by`, `remark`) 
VALUES ('your_api_key_here', 'your_secret_key_here', 'ERNIE-Bot-turbo', 1, 'admin', '默认百度AI配置，请修改为真实的API Key');

-- AI文案库表
CREATE TABLE IF NOT EXISTS `ai_copywriting_library` (
  `library_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文案库ID',
  `library_name` varchar(200) NOT NULL COMMENT '文案库名称',
  `use_ai` tinyint(1) DEFAULT 1 COMMENT '是否使用AI生成',
  `shop_details` text COMMENT '店铺详情',
  `prompt` text COMMENT 'AI提示词',
  `target_count` int(11) DEFAULT 10 COMMENT '目标生成条数',
  `generated_count` int(11) DEFAULT 0 COMMENT '已生成条数',
  `word_count` int(11) DEFAULT 200 COMMENT '文案字数要求',
  `status` varchar(20) DEFAULT 'pending' COMMENT '生成状态',
  `error_message` text COMMENT '错误信息',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `store_id` bigint(20) DEFAULT NULL COMMENT '店铺ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`library_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI文案库表';

-- AI文案内容表
CREATE TABLE IF NOT EXISTS `ai_copywriting_content` (
  `content_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `library_id` bigint(20) NOT NULL COMMENT '文案库ID',
  `content` text NOT NULL COMMENT '文案内容',
  `title` varchar(200) DEFAULT NULL COMMENT '文案标题',
  `word_count` int(11) DEFAULT 0 COMMENT '字数统计',
  `is_ai_generated` tinyint(1) DEFAULT 1 COMMENT '是否AI生成',
  `status` varchar(20) DEFAULT 'active' COMMENT '生成状态',
  `quality_score` int(11) DEFAULT 0 COMMENT '质量评分',
  `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `copy_count` int(11) DEFAULT 0 COMMENT '复制次数',
  `export_count` int(11) DEFAULT 0 COMMENT '导出次数',
  `use_ai` tinyint(1) DEFAULT 1 COMMENT '是否使用AI',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`content_id`),
  KEY `idx_library_id` (`library_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_content_library` FOREIGN KEY (`library_id`) REFERENCES `ai_copywriting_library` (`library_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI文案内容表';

-- 插入示例数据
INSERT INTO `ai_copywriting_library` (`library_name`, `use_ai`, `shop_details`, `prompt`, `target_count`, `word_count`, `create_by`) 
VALUES 
('美食推广文案库', 1, '一家温馨的咖啡厅，主营手工咖啡和精致甜点，位于市中心，环境优雅，适合商务洽谈和休闲聚会', '请为我的咖啡厅生成吸引人的推广文案，突出手工制作、环境优雅、适合多种场合的特点', 5, 150, 'admin'),
('服装店宣传文案', 1, '时尚女装店，主营韩式风格服装，面向20-35岁都市女性，注重品质和设计感', '为时尚女装店生成宣传文案，突出韩式风格、品质设计、适合都市女性的特点', 3, 120, 'admin');

INSERT INTO `ai_copywriting_content` (`library_id`, `content`, `title`, `word_count`, `is_ai_generated`, `create_by`) 
VALUES 
(1, '☕️ 在这个快节奏的都市里，给自己一个慢下来的理由。我们的手工咖啡，每一杯都是匠心独运的艺术品。精选优质咖啡豆，专业烘焙师精心调制，只为那一口醇香回甘。优雅的环境，轻柔的音乐，无论是商务洽谈还是朋友小聚，这里都是您的理想选择。', '匠心手工咖啡，品味慢生活', 89, 1, 'admin'),
(1, '🍰 甜点不只是味蕾的享受，更是心灵的慰藉。我们的每一款甜点都由资深糕点师手工制作，选用进口优质原料，无添加剂，健康美味。从经典提拉米苏到创意水果塔，每一口都是幸福的味道。来这里，让甜蜜治愈您的疲惫。', '手工甜点，治愈系美味', 78, 1, 'admin'),
(2, '👗 韩式优雅，都市新选择！精选韩国设计师作品，每一件都是时尚与品质的完美结合。面料舒适，剪裁精致，让您在职场与生活中都能展现最美的自己。20-35岁的都市女性，这里有属于您的时尚密码。', '韩式时尚，优雅都市女性首选', 72, 1, 'admin');
