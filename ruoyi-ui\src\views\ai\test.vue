<template>
  <div class="ai-test-container">
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-cpu"></i>
        百度AI接口测试
      </h1>
      <p class="page-description">测试百度智能云DeepSeek V3接口配置和功能</p>
    </div>

    <!-- 配置验证 -->
    <div class="test-section">
      <h3>
        <i class="el-icon-setting"></i>
        配置验证
      </h3>
      <div class="test-actions">
        <el-button type="primary" @click="validateConfig" :loading="validating">
          验证API配置
        </el-button>
        <el-button type="info" @click="getModelInfo" :loading="loadingModel">
          获取模型信息
        </el-button>
        <el-button type="warning" @click="getAccessToken" :loading="loadingToken">
          测试访问令牌
        </el-button>
      </div>
      <div class="test-result" v-if="configResult">
        <el-alert 
          :title="configResult.title" 
          :type="configResult.type" 
          :description="configResult.message"
          show-icon
        ></el-alert>
      </div>
    </div>

    <!-- 单条生成测试 -->
    <div class="test-section">
      <h3>
        <i class="el-icon-edit"></i>
        单条文案生成测试
      </h3>
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="店铺详情">
          <el-input 
            v-model="testForm.shopDetails" 
            type="textarea" 
            :rows="3"
            placeholder="请输入店铺详情"
          ></el-input>
        </el-form-item>
        <el-form-item label="生成要求">
          <el-input 
            v-model="testForm.prompt" 
            type="textarea" 
            :rows="2"
            placeholder="请输入文案生成要求"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testGenerate" :loading="generating">
            生成文案
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="test-result" v-if="generateResult">
        <h4>生成结果：</h4>
        <div class="generated-content">{{ generateResult }}</div>
        <div class="content-info">
          <span>字数：{{ generateResult.length }}</span>
          <el-button size="mini" type="primary" @click="copyResult">复制</el-button>
        </div>
      </div>
    </div>

    <!-- 批量生成测试 -->
    <div class="test-section">
      <h3>
        <i class="el-icon-document-copy"></i>
        批量文案生成测试
      </h3>
      <el-form :model="batchForm" label-width="120px">
        <el-form-item label="店铺详情">
          <el-input 
            v-model="batchForm.shopDetails" 
            type="textarea" 
            :rows="2"
            placeholder="请输入店铺详情"
          ></el-input>
        </el-form-item>
        <el-form-item label="生成要求">
          <el-input 
            v-model="batchForm.prompt" 
            type="textarea" 
            :rows="2"
            placeholder="请输入文案生成要求"
          ></el-input>
        </el-form-item>
        <el-form-item label="生成数量">
          <el-input-number 
            v-model="batchForm.count" 
            :min="1" 
            :max="10"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testBatchGenerate" :loading="batchGenerating">
            批量生成
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="test-result" v-if="batchResults.length > 0">
        <h4>批量生成结果：</h4>
        <div v-for="(result, index) in batchResults" :key="index" class="batch-item">
          <div class="batch-header">
            <span class="batch-index">第{{ index + 1 }}条</span>
            <span class="batch-length">{{ result.length }}字</span>
          </div>
          <div class="batch-content">{{ result }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'AiTest',
  data() {
    return {
      validating: false,
      loadingModel: false,
      loadingToken: false,
      generating: false,
      batchGenerating: false,
      configResult: null,
      generateResult: '',
      batchResults: [],
      testForm: {
        shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',
        prompt: '生成一条吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围'
      },
      batchForm: {
        shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',
        prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',
        count: 3
      }
    }
  },
  methods: {
    // 验证API配置
    validateConfig() {
      this.validating = true
      request({
        url: '/ai/test/validate-config',
        method: 'get'
      }).then(response => {
        this.configResult = {
          title: '配置验证成功',
          type: 'success',
          message: response.msg || '百度AI配置验证通过'
        }
      }).catch(error => {
        this.configResult = {
          title: '配置验证失败',
          type: 'error',
          message: error.msg || error.message || '请检查API Key和Secret Key配置'
        }
      }).finally(() => {
        this.validating = false
      })
    },

    // 获取模型信息
    getModelInfo() {
      this.loadingModel = true
      request({
        url: '/ai/test/model-info',
        method: 'get'
      }).then(response => {
        const info = response.data
        this.configResult = {
          title: '模型信息获取成功',
          type: 'success',
          message: `模型：${info.model}，提供商：${info.provider}，最大Token：${info.maxTokens}`
        }
      }).catch(error => {
        this.configResult = {
          title: '获取模型信息失败',
          type: 'error',
          message: error.msg || error.message
        }
      }).finally(() => {
        this.loadingModel = false
      })
    },

    // 获取访问令牌
    getAccessToken() {
      this.loadingToken = true
      request({
        url: '/ai/test/access-token',
        method: 'get'
      }).then(response => {
        const data = response.data
        this.configResult = {
          title: '访问令牌获取成功',
          type: 'success',
          message: `令牌长度：${data.length}，前缀：${data.token}`
        }
      }).catch(error => {
        this.configResult = {
          title: '获取访问令牌失败',
          type: 'error',
          message: error.msg || error.message
        }
      }).finally(() => {
        this.loadingToken = false
      })
    },

    // 测试生成文案
    testGenerate() {
      this.generating = true
      request({
        url: '/ai/test/generate',
        method: 'post',
        data: this.testForm,
        timeout: 30000
      }).then(response => {
        this.generateResult = response.data.generatedContent
        this.$message.success('文案生成成功')
      }).catch(error => {
        this.$message.error('生成失败：' + (error.msg || error.message))
      }).finally(() => {
        this.generating = false
      })
    },

    // 测试批量生成
    testBatchGenerate() {
      this.batchGenerating = true
      request({
        url: '/ai/test/batch-generate',
        method: 'post',
        data: this.batchForm,
        timeout: 60000
      }).then(response => {
        this.batchResults = response.data.results
        this.$message.success('批量生成成功')
      }).catch(error => {
        this.$message.error('批量生成失败：' + (error.msg || error.message))
      }).finally(() => {
        this.batchGenerating = false
      })
    },

    // 复制结果
    copyResult() {
      navigator.clipboard.writeText(this.generateResult).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-test-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      margin-right: 12px;
      color: #409eff;
    }
  }

  .page-description {
    color: #7f8c8d;
    margin: 0;
  }
}

.test-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;

    i {
      margin-right: 12px;
      color: #409eff;
    }
  }

  .test-actions {
    margin-bottom: 20px;

    .el-button {
      margin-right: 12px;
    }
  }

  .test-result {
    margin-top: 20px;

    h4 {
      margin: 0 0 12px 0;
      color: #2c3e50;
    }

    .generated-content {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 6px;
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .content-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #7f8c8d;
    }

    .batch-item {
      border: 1px solid #e9ecef;
      border-radius: 6px;
      margin-bottom: 12px;
      overflow: hidden;

      .batch-header {
        background: #f8f9fa;
        padding: 8px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .batch-index {
          font-weight: 600;
          color: #409eff;
        }

        .batch-length {
          color: #7f8c8d;
        }
      }

      .batch-content {
        padding: 16px;
        line-height: 1.6;
      }
    }
  }
}
</style>
