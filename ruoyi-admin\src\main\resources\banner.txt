  ____              __   __ _       _____  _                        _      _             ____   _
 |  _ \  _   _   ___\ \ / /(_)     |  ___|| |  ___ __      __ __ _ | |__  | |  ___      |  _ \ | | _   _  ___
 | |_) || | | | / _ \\ V / | |     | |_   | | / _ \\ \ /\ / // _` || '_ \ | | / _ \     | |_) || || | | |/ __|
 |  _ < | |_| || (_) || |  | |     |  _|  | || (_) |\ V  V /| (_| || |_) || ||  __/     |  __/ | || |_| |\__ \
 |_| \_\ \__,_| \___/ |_|  |_|     |_|    |_| \___/  \_/\_/  \__,_||_.__/ |_| \___|     |_|    |_| \__,_||___/

${AnsiColor.GREEN} :: RuoYi :: ${AnsiColor.BLUE} RuoYi-Flowable-Plus:${AnsiColor.RED}${spring.profiles.active}${AnsiColor.BRIGHT_WHITE}        :: Application Version: ${ruoyi-flowable-plus.version} ${spring-boot.formatted-version}
