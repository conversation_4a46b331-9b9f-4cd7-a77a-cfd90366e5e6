Write-Host "=== 简单检查 ===" -ForegroundColor Cyan

Write-Host "`n1. 检查关键文件..." -ForegroundColor Yellow

$files = @(
    "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoAiTestController.java",
    "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoDirectService.java",
    "ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DoubaoAiServiceImpl.java",
    "ruoyi-system/src/main/java/com/ruoyi/system/service/IAiService.java"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

Write-Host "`n2. 检查配置..." -ForegroundColor Yellow
$configFile = "ruoyi-admin/src/main/resources/application-dev.yml"
if (Test-Path $configFile) {
    Write-Host "✅ 配置文件存在" -ForegroundColor Green
    $content = Get-Content $configFile -Raw
    if ($content -match "doubao:") {
        Write-Host "✅ 包含Doubao配置" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺少Doubao配置" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 配置文件不存在" -ForegroundColor Red
}

Write-Host "`n3. 检查端口8078..." -ForegroundColor Yellow
$portResult = netstat -an | Select-String ":8078"
if ($portResult) {
    Write-Host "⚠️ 端口8078被占用" -ForegroundColor Yellow
} else {
    Write-Host "✅ 端口8078可用" -ForegroundColor Green
}

Write-Host "`n=== 检查完成 ===" -ForegroundColor Cyan
