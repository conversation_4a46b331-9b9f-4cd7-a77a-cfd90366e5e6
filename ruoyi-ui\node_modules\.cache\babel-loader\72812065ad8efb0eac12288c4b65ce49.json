{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\utils\\request.js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\utils\\request.js", "mtime": 1754552191424}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1753759488589}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_elementUi", "_store", "_auth", "_errorCode", "_ruoyi", "_cache", "_fileSaver", "_error<PERSON><PERSON><PERSON>", "downloadLoadingInstance", "is<PERSON><PERSON>gin", "exports", "show", "isRefreshing", "failedQueue", "processQueue", "error", "token", "arguments", "length", "undefined", "for<PERSON>ach", "prom", "reject", "resolve", "refreshToken", "Promise", "currentToken", "getToken", "Error", "axios", "defaults", "headers", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "isToken", "isRepeatSubmit", "repeatSubmit", "method", "params", "url", "tansParams", "slice", "requestObj", "data", "_typeof2", "default", "JSON", "stringify", "time", "Date", "getTime", "session<PERSON>bj", "cache", "session", "getJSON", "setJSON", "s_url", "s_data", "s_time", "interval", "message", "console", "warn", "concat", "log", "response", "res", "code", "msg", "errorCode", "responseType", "includes", "then", "newToken", "setToken", "catch", "push", "err", "showSafeError", "showSafeNotification", "substr", "status", "download", "filename", "Loading", "text", "spinner", "background", "post", "_objectSpread2", "transformRequest", "_ref", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "isBlob", "blob", "resText", "rspObj", "errMsg", "w", "_context", "n", "blobValidate", "Blob", "saveAs", "v", "parse", "Message", "close", "a", "_x", "apply", "r", "_default"], "sources": ["E:/ry-vue-flowable-xg-main/ruoyi-ui/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { Notification, MessageBox, Message, Loading } from 'element-ui'\nimport store from '@/store'\nimport { getToken, setToken } from '@/utils/auth'\nimport errorCode from '@/utils/errorCode'\nimport { tansParams, blobValidate } from \"@/utils/ruoyi\";\nimport cache from '@/plugins/cache'\nimport { saveAs } from 'file-saver'\nimport { showSafeError, showSafeNotification, isLoginPage } from '@/utils/errorHandler'\n\nlet downloadLoadingInstance;\n// 是否显示重新登录\nexport let isRelogin = { show: false };\n// token刷新相关\nlet isRefreshing = false;\nlet failedQueue = [];\n\nconst processQueue = (error, token = null) => {\n  failedQueue.forEach(prom => {\n    if (error) {\n      prom.reject(error);\n    } else {\n      prom.resolve(token);\n    }\n  });\n  \n  failedQueue = [];\n};\n\n// 刷新token的方法\nconst refreshToken = () => {\n  return new Promise((resolve, reject) => {\n    // 这里可以调用刷新token的API\n    // 暂时使用简单的token延长逻辑\n    const currentToken = getToken();\n    if (currentToken) {\n      // 可以在这里调用后端刷新token的接口\n      // 暂时直接返回当前token\n      resolve(currentToken);\n    } else {\n      reject(new Error('No token to refresh'));\n    }\n  });\n};\n\naxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'\n// 对应国际化资源文件后缀\naxios.defaults.headers['Content-Language'] = 'zh_CN'\n// 创建axios实例\nconst service = axios.create({\n  // axios中请求配置有baseURL选项，表示请求URL公共部分\n  baseURL: process.env.VUE_APP_BASE_API,\n  // 超时\n  timeout: 60000\n})\n\n// request拦截器\nservice.interceptors.request.use(config => {\n  // 是否需要设置 token\n  const isToken = (config.headers || {}).isToken === false\n  // 是否需要防止数据重复提交\n  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false\n  if (getToken() && !isToken) {\n    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改\n  }\n  // get请求映射params参数\n  if (config.method === 'get' && config.params) {\n    let url = config.url + '?' + tansParams(config.params);\n    url = url.slice(0, -1);\n    config.params = {};\n    config.url = url;\n  }\n  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {\n    const requestObj = {\n      url: config.url,\n      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,\n      time: new Date().getTime()\n    }\n    const sessionObj = cache.session.getJSON('sessionObj')\n    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {\n      cache.session.setJSON('sessionObj', requestObj)\n    } else {\n      const s_url = sessionObj.url;                  // 请求地址\n      const s_data = sessionObj.data;                // 请求数据\n      const s_time = sessionObj.time;                // 请求时间\n      const interval = 1000;                         // 间隔时间(ms)，小于此时间视为重复提交\n      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {\n        const message = '数据正在处理，请勿重复提交';\n        console.warn(`[${s_url}]: ` + message)\n        return Promise.reject(new Error(message))\n      } else {\n        cache.session.setJSON('sessionObj', requestObj)\n      }\n    }\n  }\n  return config\n}, error => {\n    console.log(error)\n    Promise.reject(error)\n})\n\n// 响应拦截器\nservice.interceptors.response.use(res => {\n    // 未设置状态码则默认成功状态\n    const code = res.data.code || 200;\n    // 获取错误信息\n    const msg = errorCode[code] || res.data.msg || errorCode['default']\n    // 二进制数据则直接返回\n    if (res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer') {\n      return res.data\n    }\n    if (code === 401) {\n      // 检查是否是token过期\n      if (res.data.msg && res.data.msg.includes('token')) {\n        // 尝试刷新token\n        if (!isRefreshing) {\n          isRefreshing = true;\n          refreshToken().then(newToken => {\n            setToken(newToken);\n            processQueue(null, newToken);\n            isRefreshing = false;\n            // 重试当前请求\n            return service(res.config);\n          }).catch(error => {\n            processQueue(error, null);\n            isRefreshing = false;\n            // 刷新失败，静默处理（不显示登录过期提示框）\n            console.log('登录状态已过期，但继续使用演示模式')\n            isRelogin.show = false;\n          });\n        } else {\n          // 如果正在刷新，将请求加入队列\n          return new Promise((resolve, reject) => {\n            failedQueue.push({ resolve, reject });\n          }).then(token => {\n            res.config.headers['Authorization'] = 'Bearer ' + token;\n            return service(res.config);\n          }).catch(err => {\n            return Promise.reject(err);\n          });\n        }\n      } else {\n        // 其他401错误，静默处理（不显示登录过期提示框）\n        console.log('检测到401错误，但继续使用演示模式')\n        isRelogin.show = false;\n      }\n      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')\n    } else if (code === 500) {\n      showSafeError(msg)\n      return Promise.reject(new Error(msg))\n    } else if (code === 601) {\n      showSafeError(msg, 'warning')\n      return Promise.reject('error')\n    } else if (code !== 200) {\n      // 避免在登录页面显示错误通知\n      showSafeNotification(msg)\n      return Promise.reject('error')\n    } else {\n      return res.data\n    }\n  },\n  error => {\n    console.log('err' + error)\n    let { message } = error;\n    if (message == \"Network Error\") {\n      message = \"后端接口连接异常\";\n    } else if (message.includes(\"timeout\")) {\n      message = \"系统接口请求超时\";\n    } else if (message.includes(\"Request failed with status code\")) {\n      message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\n    }\n    \n    // 处理401错误（网络层面的）\n    if (error.response && error.response.status === 401) {\n      // 静默处理401错误（不显示登录过期提示框）\n      console.log('网络层401错误，但继续使用演示模式')\n      isRelogin.show = false;\n    } else {\n      // 避免在登录页面显示网络错误提示\n      showSafeError(message, 'error', 5000)\n    }\n    return Promise.reject(error)\n  }\n)\n\n// 通用下载方法\nexport function download(url, params, filename, config) {\n  downloadLoadingInstance = Loading.service({ text: \"正在下载数据，请稍候\", spinner: \"el-icon-loading\", background: \"rgba(0, 0, 0, 0.7)\", })\n  return service.post(url, params, {\n    transformRequest: [(params) => { return tansParams(params) }],\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n    responseType: 'blob',\n    ...config\n  }).then(async (data) => {\n    const isBlob = blobValidate(data);\n    if (isBlob) {\n      const blob = new Blob([data])\n      saveAs(blob, filename)\n    } else {\n      const resText = await data.text();\n      const rspObj = JSON.parse(resText);\n      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']\n      Message.error(errMsg);\n    }\n    downloadLoadingInstance.close();\n  }).catch((r) => {\n    console.error(r)\n    Message.error('下载文件出现错误，请联系管理员！')\n    downloadLoadingInstance.close();\n  })\n}\n\nexport default service\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AAEA,IAAIS,uBAAuB;AAC3B;AACO,IAAIC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EAAEE,IAAI,EAAE;AAAM,CAAC;AACtC;AACA,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,WAAW,GAAG,EAAE;AAEpB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAmB;EAAA,IAAjBC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACvCJ,WAAW,CAACO,OAAO,CAAC,UAAAC,IAAI,EAAI;IAC1B,IAAIN,KAAK,EAAE;MACTM,IAAI,CAACC,MAAM,CAACP,KAAK,CAAC;IACpB,CAAC,MAAM;MACLM,IAAI,CAACE,OAAO,CAACP,KAAK,CAAC;IACrB;EACF,CAAC,CAAC;EAEFH,WAAW,GAAG,EAAE;AAClB,CAAC;;AAED;AACA,IAAMW,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EACzB,OAAO,IAAIC,OAAO,CAAC,UAACF,OAAO,EAAED,MAAM,EAAK;IACtC;IACA;IACA,IAAMI,YAAY,GAAG,IAAAC,cAAQ,EAAC,CAAC;IAC/B,IAAID,YAAY,EAAE;MAChB;MACA;MACAH,OAAO,CAACG,YAAY,CAAC;IACvB,CAAC,MAAM;MACLJ,MAAM,CAAC,IAAIM,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC1C;EACF,CAAC,CAAC;AACJ,CAAC;AAEDC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;AACzE;AACAF,cAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO;AACpD;AACA,IAAMC,OAAO,GAAGH,cAAK,CAACI,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EACzC;EACA,IAAMC,OAAO,GAAG,CAACD,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEY,OAAO,KAAK,KAAK;EACxD;EACA,IAAMC,cAAc,GAAG,CAACF,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEc,YAAY,KAAK,KAAK;EACpE,IAAI,IAAAlB,cAAQ,EAAC,CAAC,IAAI,CAACgB,OAAO,EAAE;IAC1BD,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,IAAAJ,cAAQ,EAAC,CAAC,EAAC;EAC3D;EACA;EACA,IAAIe,MAAM,CAACI,MAAM,KAAK,KAAK,IAAIJ,MAAM,CAACK,MAAM,EAAE;IAC5C,IAAIC,GAAG,GAAGN,MAAM,CAACM,GAAG,GAAG,GAAG,GAAG,IAAAC,iBAAU,EAACP,MAAM,CAACK,MAAM,CAAC;IACtDC,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBR,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC;IAClBL,MAAM,CAACM,GAAG,GAAGA,GAAG;EAClB;EACA,IAAI,CAACJ,cAAc,KAAKF,MAAM,CAACI,MAAM,KAAK,MAAM,IAAIJ,MAAM,CAACI,MAAM,KAAK,KAAK,CAAC,EAAE;IAC5E,IAAMK,UAAU,GAAG;MACjBH,GAAG,EAAEN,MAAM,CAACM,GAAG;MACfI,IAAI,EAAE,IAAAC,QAAA,CAAAC,OAAA,EAAOZ,MAAM,CAACU,IAAI,MAAK,QAAQ,GAAGG,IAAI,CAACC,SAAS,CAACd,MAAM,CAACU,IAAI,CAAC,GAAGV,MAAM,CAACU,IAAI;MACjFK,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;IAC3B,CAAC;IACD,IAAMC,UAAU,GAAGC,cAAK,CAACC,OAAO,CAACC,OAAO,CAAC,YAAY,CAAC;IACtD,IAAIH,UAAU,KAAKzC,SAAS,IAAIyC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE,EAAE;MACxEC,cAAK,CAACC,OAAO,CAACE,OAAO,CAAC,YAAY,EAAEb,UAAU,CAAC;IACjD,CAAC,MAAM;MACL,IAAMc,KAAK,GAAGL,UAAU,CAACZ,GAAG,CAAC,CAAkB;MAC/C,IAAMkB,MAAM,GAAGN,UAAU,CAACR,IAAI,CAAC,CAAgB;MAC/C,IAAMe,MAAM,GAAGP,UAAU,CAACH,IAAI,CAAC,CAAgB;MAC/C,IAAMW,QAAQ,GAAG,IAAI,CAAC,CAAyB;MAC/C,IAAIF,MAAM,KAAKf,UAAU,CAACC,IAAI,IAAID,UAAU,CAACM,IAAI,GAAGU,MAAM,GAAGC,QAAQ,IAAIH,KAAK,KAAKd,UAAU,CAACH,GAAG,EAAE;QACjG,IAAMqB,OAAO,GAAG,eAAe;QAC/BC,OAAO,CAACC,IAAI,CAAC,IAAAC,MAAA,CAAIP,KAAK,WAAQI,OAAO,CAAC;QACtC,OAAO5C,OAAO,CAACH,MAAM,CAAC,IAAIM,KAAK,CAACyC,OAAO,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLR,cAAK,CAACC,OAAO,CAACE,OAAO,CAAC,YAAY,EAAEb,UAAU,CAAC;MACjD;IACF;EACF;EACA,OAAOT,MAAM;AACf,CAAC,EAAE,UAAA3B,KAAK,EAAI;EACRuD,OAAO,CAACG,GAAG,CAAC1D,KAAK,CAAC;EAClBU,OAAO,CAACH,MAAM,CAACP,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAiB,OAAO,CAACO,YAAY,CAACmC,QAAQ,CAACjC,GAAG,CAAC,UAAAkC,GAAG,EAAI;EACrC;EACA,IAAMC,IAAI,GAAGD,GAAG,CAACvB,IAAI,CAACwB,IAAI,IAAI,GAAG;EACjC;EACA,IAAMC,GAAG,GAAGC,kBAAS,CAACF,IAAI,CAAC,IAAID,GAAG,CAACvB,IAAI,CAACyB,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;EACnE;EACA,IAAIH,GAAG,CAACnC,OAAO,CAACuC,YAAY,KAAM,MAAM,IAAIJ,GAAG,CAACnC,OAAO,CAACuC,YAAY,KAAM,aAAa,EAAE;IACvF,OAAOJ,GAAG,CAACvB,IAAI;EACjB;EACA,IAAIwB,IAAI,KAAK,GAAG,EAAE;IAChB;IACA,IAAID,GAAG,CAACvB,IAAI,CAACyB,GAAG,IAAIF,GAAG,CAACvB,IAAI,CAACyB,GAAG,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClD;MACA,IAAI,CAACpE,YAAY,EAAE;QACjBA,YAAY,GAAG,IAAI;QACnBY,YAAY,CAAC,CAAC,CAACyD,IAAI,CAAC,UAAAC,QAAQ,EAAI;UAC9B,IAAAC,cAAQ,EAACD,QAAQ,CAAC;UAClBpE,YAAY,CAAC,IAAI,EAAEoE,QAAQ,CAAC;UAC5BtE,YAAY,GAAG,KAAK;UACpB;UACA,OAAOoB,OAAO,CAAC2C,GAAG,CAACjC,MAAM,CAAC;QAC5B,CAAC,CAAC,CAAC0C,KAAK,CAAC,UAAArE,KAAK,EAAI;UAChBD,YAAY,CAACC,KAAK,EAAE,IAAI,CAAC;UACzBH,YAAY,GAAG,KAAK;UACpB;UACA0D,OAAO,CAACG,GAAG,CAAC,mBAAmB,CAAC;UAChChE,SAAS,CAACE,IAAI,GAAG,KAAK;QACxB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,IAAIc,OAAO,CAAC,UAACF,OAAO,EAAED,MAAM,EAAK;UACtCT,WAAW,CAACwE,IAAI,CAAC;YAAE9D,OAAO,EAAPA,OAAO;YAAED,MAAM,EAANA;UAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC2D,IAAI,CAAC,UAAAjE,KAAK,EAAI;UACf2D,GAAG,CAACjC,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGf,KAAK;UACvD,OAAOgB,OAAO,CAAC2C,GAAG,CAACjC,MAAM,CAAC;QAC5B,CAAC,CAAC,CAAC0C,KAAK,CAAC,UAAAE,GAAG,EAAI;UACd,OAAO7D,OAAO,CAACH,MAAM,CAACgE,GAAG,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL;MACAhB,OAAO,CAACG,GAAG,CAAC,oBAAoB,CAAC;MACjChE,SAAS,CAACE,IAAI,GAAG,KAAK;IACxB;IACA,OAAOc,OAAO,CAACH,MAAM,CAAC,sBAAsB,CAAC;EAC/C,CAAC,MAAM,IAAIsD,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAW,2BAAa,EAACV,GAAG,CAAC;IAClB,OAAOpD,OAAO,CAACH,MAAM,CAAC,IAAIM,KAAK,CAACiD,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAID,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAW,2BAAa,EAACV,GAAG,EAAE,SAAS,CAAC;IAC7B,OAAOpD,OAAO,CAACH,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM,IAAIsD,IAAI,KAAK,GAAG,EAAE;IACvB;IACA,IAAAY,kCAAoB,EAACX,GAAG,CAAC;IACzB,OAAOpD,OAAO,CAACH,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOqD,GAAG,CAACvB,IAAI;EACjB;AACF,CAAC,EACD,UAAArC,KAAK,EAAI;EACPuD,OAAO,CAACG,GAAG,CAAC,KAAK,GAAG1D,KAAK,CAAC;EAC1B,IAAMsD,OAAO,GAAKtD,KAAK,CAAjBsD,OAAO;EACb,IAAIA,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACW,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtCX,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACW,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9DX,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACoB,MAAM,CAACpB,OAAO,CAACnD,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9D;;EAEA;EACA,IAAIH,KAAK,CAAC2D,QAAQ,IAAI3D,KAAK,CAAC2D,QAAQ,CAACgB,MAAM,KAAK,GAAG,EAAE;IACnD;IACApB,OAAO,CAACG,GAAG,CAAC,oBAAoB,CAAC;IACjChE,SAAS,CAACE,IAAI,GAAG,KAAK;EACxB,CAAC,MAAM;IACL;IACA,IAAA4E,2BAAa,EAAClB,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;EACvC;EACA,OAAO5C,OAAO,CAACH,MAAM,CAACP,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACO,SAAS4E,QAAQA,CAAC3C,GAAG,EAAED,MAAM,EAAE6C,QAAQ,EAAElD,MAAM,EAAE;EACtDlC,uBAAuB,GAAGqF,kBAAO,CAAC7D,OAAO,CAAC;IAAE8D,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,iBAAiB;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAAC;EAChI,OAAOhE,OAAO,CAACiE,IAAI,CAACjD,GAAG,EAAED,MAAM,MAAAmD,cAAA,CAAA5C,OAAA;IAC7B6C,gBAAgB,EAAE,CAAC,UAACpD,MAAM,EAAK;MAAE,OAAO,IAAAE,iBAAU,EAACF,MAAM,CAAC;IAAC,CAAC,CAAC;IAC7DhB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEgD,YAAY,EAAE;EAAM,GACjBrC,MAAM,CACV,CAAC,CAACuC,IAAI;IAAA,IAAAmB,IAAA,OAAAC,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,CAAC,SAAAC,QAAOpD,IAAI;MAAA,IAAAqD,MAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;MAAA,WAAAP,aAAA,CAAAhD,OAAA,IAAAwD,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YACXP,MAAM,GAAG,IAAAQ,mBAAY,EAAC7D,IAAI,CAAC;YAAA,KAC7BqD,MAAM;cAAAM,QAAA,CAAAC,CAAA;cAAA;YAAA;YACFN,IAAI,GAAG,IAAIQ,IAAI,CAAC,CAAC9D,IAAI,CAAC,CAAC;YAC7B,IAAA+D,iBAAM,EAACT,IAAI,EAAEd,QAAQ,CAAC;YAAAmB,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OAEA5D,IAAI,CAAC0C,IAAI,CAAC,CAAC;UAAA;YAA3Ba,OAAO,GAAAI,QAAA,CAAAK,CAAA;YACPR,MAAM,GAAGrD,IAAI,CAAC8D,KAAK,CAACV,OAAO,CAAC;YAC5BE,MAAM,GAAG/B,kBAAS,CAAC8B,MAAM,CAAChC,IAAI,CAAC,IAAIgC,MAAM,CAAC/B,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;YAC3EwC,kBAAO,CAACvG,KAAK,CAAC8F,MAAM,CAAC;UAAC;YAExBrG,uBAAuB,CAAC+G,KAAK,CAAC,CAAC;UAAC;YAAA,OAAAR,QAAA,CAAAS,CAAA;QAAA;MAAA,GAAAhB,OAAA;IAAA,CACjC;IAAA,iBAAAiB,EAAA;MAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAzG,SAAA;IAAA;EAAA,IAAC,CAACmE,KAAK,CAAC,UAACuC,CAAC,EAAK;IACdrD,OAAO,CAACvD,KAAK,CAAC4G,CAAC,CAAC;IAChBL,kBAAO,CAACvG,KAAK,CAAC,kBAAkB,CAAC;IACjCP,uBAAuB,CAAC+G,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;AACJ;AAAC,IAAAK,QAAA,GAAAlH,OAAA,CAAA4C,OAAA,GAEctB,OAAO", "ignoreList": []}]}