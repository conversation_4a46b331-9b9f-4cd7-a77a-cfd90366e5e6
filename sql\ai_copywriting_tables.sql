-- AI文案生成相关表结构

-- 1. AI文案库表
DROP TABLE IF EXISTS `ai_copywriting_library`;
CREATE TABLE `ai_copywriting_library` (
  `library_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文案库ID',
  `library_name` varchar(100) NOT NULL COMMENT '文案库名称',
  `use_ai` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否使用AI生成(0否 1是)',
  `shop_details` text COMMENT '店铺详情',
  `prompt` text COMMENT 'AI提示词',
  `target_count` int(11) DEFAULT '0' COMMENT '目标生成条数',
  `generated_count` int(11) DEFAULT '0' COMMENT '已生成条数',
  `word_count` int(11) DEFAULT '100' COMMENT '文案字数要求',
  `status` varchar(20) DEFAULT 'pending' COMMENT '生成状态(pending未开始 generating生成中 completed已完成 failed失败)',
  `error_message` text COMMENT '错误信息',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `store_id` bigint(20) DEFAULT NULL COMMENT '店铺ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`library_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI文案库表';

-- 2. AI文案内容表
DROP TABLE IF EXISTS `ai_copywriting_content`;
CREATE TABLE `ai_copywriting_content` (
  `content_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `library_id` bigint(20) NOT NULL COMMENT '文案库ID',
  `content` text NOT NULL COMMENT '文案内容',
  `title` varchar(200) DEFAULT NULL COMMENT '文案标题',
  `word_count` int(11) DEFAULT '0' COMMENT '字数统计',
  `is_ai_generated` tinyint(1) DEFAULT '1' COMMENT '是否AI生成(0否 1是)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active正常 deleted已删除)',
  `quality_score` int(11) DEFAULT '0' COMMENT '质量评分(0-100)',
  `use_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `copy_count` int(11) DEFAULT '0' COMMENT '复制次数',
  `export_count` int(11) DEFAULT '0' COMMENT '导出次数',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`content_id`),
  KEY `idx_library_id` (`library_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_quality_score` (`quality_score`),
  CONSTRAINT `fk_content_library` FOREIGN KEY (`library_id`) REFERENCES `ai_copywriting_library` (`library_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI文案内容表';

-- 3. AI生成任务表
DROP TABLE IF EXISTS `ai_generation_task`;
CREATE TABLE `ai_generation_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `library_id` bigint(20) NOT NULL COMMENT '文案库ID',
  `task_type` varchar(50) DEFAULT 'batch_generate' COMMENT '任务类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态(pending等待 running运行中 completed完成 failed失败)',
  `progress` int(11) DEFAULT '0' COMMENT '进度百分比',
  `total_count` int(11) DEFAULT '0' COMMENT '总数量',
  `completed_count` int(11) DEFAULT '0' COMMENT '已完成数量',
  `failed_count` int(11) DEFAULT '0' COMMENT '失败数量',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text COMMENT '错误信息',
  `config_json` text COMMENT '配置JSON',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  KEY `idx_library_id` (`library_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_task_library` FOREIGN KEY (`library_id`) REFERENCES `ai_copywriting_library` (`library_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI生成任务表';

-- 4. AI使用统计表
DROP TABLE IF EXISTS `ai_usage_statistics`;
CREATE TABLE `ai_usage_statistics` (
  `stat_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `api_calls` int(11) DEFAULT '0' COMMENT 'API调用次数',
  `tokens_used` int(11) DEFAULT '0' COMMENT '使用的token数',
  `content_generated` int(11) DEFAULT '0' COMMENT '生成的文案数',
  `success_rate` decimal(5,2) DEFAULT '0.00' COMMENT '成功率',
  `cost_amount` decimal(10,2) DEFAULT '0.00' COMMENT '费用金额',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`stat_id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`date`),
  KEY `idx_date` (`date`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- 插入示例数据
INSERT INTO `ai_copywriting_library` (`library_name`, `use_ai`, `shop_details`, `prompt`, `target_count`, `generated_count`, `word_count`, `status`, `user_id`, `store_id`, `create_by`, `create_time`) VALUES
('美食探店文案库', 1, '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。', '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围', 20, 20, 100, 'completed', 1, 1, 'admin', NOW()),
('时尚服装推广库', 1, '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。', '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议', 30, 15, 150, 'generating', 1, 2, 'admin', NOW()),
('手动创建文案库', 0, '', '', 10, 8, 200, 'completed', 1, 1, 'admin', NOW());

INSERT INTO `ai_copywriting_content` (`library_id`, `content`, `title`, `word_count`, `is_ai_generated`, `quality_score`, `use_count`, `like_count`, `copy_count`, `export_count`, `user_id`, `create_by`, `create_time`) VALUES
(1, '今天给大家推荐一道超级下饭的川菜——水煮鱼！鲜嫩的鱼肉配上麻辣的汤底，每一口都是满满的幸福感。来我们店里品尝正宗川菜，让你的味蕾来一场麻辣之旅！', 'AI生成-水煮鱼推广', 68, 1, 85, 12, 8, 5, 2, 1, 'admin', NOW()),
(1, '想要品尝最正宗的宫保鸡丁吗？我们的大厨有着20年的川菜制作经验，每一道菜都是用心调制。酸甜适中的口感，嫩滑的鸡肉，让你一吃就停不下来！', 'AI生成-宫保鸡丁推广', 72, 1, 90, 8, 6, 3, 1, 1, 'admin', NOW()),
(2, '这件连衣裙采用高品质真丝面料，版型修身显瘦，适合职场女性穿着。优雅的设计让你在任何场合都能成为焦点。', 'AI生成-连衣裙推广', 52, 1, 88, 5, 4, 2, 0, 1, 'admin', NOW()),
(3, '手动创建的文案内容示例，展示了如何手动添加高质量的营销文案。', '手动文案示例', 32, 0, 75, 3, 2, 1, 0, 1, 'admin', NOW());

-- 添加菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('AI文案管理', 0, 6, 'ai', NULL, 1, 0, 'M', '0', '0', NULL, 'edit', 'admin', NOW(), '', NULL, 'AI文案生成管理菜单'),
('文案库管理', (SELECT menu_id FROM sys_menu WHERE menu_name = 'AI文案管理'), 1, 'copywriting', 'ai/copywriting/index', 1, 0, 'C', '0', '0', 'ai:copywriting:list', 'documentation', 'admin', NOW(), '', NULL, '文案库管理菜单'),
('文案库查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 1, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:query', '#', 'admin', NOW(), '', NULL, ''),
('文案库新增', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 2, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:add', '#', 'admin', NOW(), '', NULL, ''),
('文案库修改', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 3, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:edit', '#', 'admin', NOW(), '', NULL, ''),
('文案库删除', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 4, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:remove', '#', 'admin', NOW(), '', NULL, ''),
('文案生成', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 5, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:generate', '#', 'admin', NOW(), '', NULL, ''),
('文案导出', (SELECT menu_id FROM sys_menu WHERE menu_name = '文案库管理'), 6, '', '', 1, 0, 'F', '0', '0', 'ai:copywriting:export', '#', 'admin', NOW(), '', NULL, '');
