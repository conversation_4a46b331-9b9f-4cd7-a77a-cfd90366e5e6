# 测试应用启动和接口可用性

Write-Host "=== 火山引擎Doubao应用测试 ===" -ForegroundColor Cyan

# 检查Java环境
Write-Host "`n1. 检查Java环境..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "Java版本: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java环境未找到，请确保已安装Java 8+" -ForegroundColor Red
    exit 1
}

# 检查Maven环境
Write-Host "`n2. 检查Maven环境..." -ForegroundColor Yellow
try {
    $mavenVersion = mvn -version 2>&1 | Select-String "Apache Maven"
    Write-Host "Maven版本: $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Maven未找到，将尝试使用mvnw" -ForegroundColor Yellow
}

# 编译项目
Write-Host "`n3. 编译项目..." -ForegroundColor Yellow
try {
    if (Test-Path "mvnw.cmd") {
        Write-Host "使用Maven Wrapper编译..." -ForegroundColor Cyan
        $compileResult = & "./mvnw.cmd" clean compile -DskipTests -q
    } else {
        Write-Host "使用Maven编译..." -ForegroundColor Cyan
        $compileResult = mvn clean compile -DskipTests -q
    }
    Write-Host "✅ 项目编译成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 项目编译失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查项目依赖和配置" -ForegroundColor Yellow
    exit 1
}

# 检查配置文件
Write-Host "`n4. 检查配置文件..." -ForegroundColor Yellow
$configFiles = @(
    "ruoyi-admin/src/main/resources/application.yml",
    "ruoyi-admin/src/main/resources/application-dev.yml"
)

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        Write-Host "✅ 找到配置文件: $configFile" -ForegroundColor Green
        
        # 检查Doubao配置
        $content = Get-Content $configFile -Raw
        if ($content -match "doubao:") {
            Write-Host "  ✅ 包含Doubao配置" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ 未找到Doubao配置" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ 配置文件不存在: $configFile" -ForegroundColor Red
    }
}

# 检查关键类文件
Write-Host "`n5. 检查关键类文件..." -ForegroundColor Yellow
$keyFiles = @(
    "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoAiTestController.java",
    "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoDirectService.java",
    "ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DoubaoAiServiceImpl.java",
    "ruoyi-system/src/main/java/com/ruoyi/system/service/IAiService.java"
)

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
Write-Host "`n📋 下一步操作建议:" -ForegroundColor Yellow
Write-Host "1. 启动应用: mvn spring-boot:run -pl ruoyi-admin" -ForegroundColor White
Write-Host "2. 等待应用启动完成" -ForegroundColor White
Write-Host "3. 测试健康检查: http://localhost:8078/ai/test/health" -ForegroundColor White
Write-Host "4. 测试Doubao接口: http://localhost:8078/ai/test/test-doubao-direct" -ForegroundColor White

Write-Host "`n🔧 如果遇到问题:" -ForegroundColor Yellow
Write-Host "- 检查端口8078是否被占用" -ForegroundColor White
Write-Host "- 检查数据库连接配置" -ForegroundColor White
Write-Host "- 检查Redis连接配置" -ForegroundColor White
Write-Host "- 查看应用启动日志" -ForegroundColor White
