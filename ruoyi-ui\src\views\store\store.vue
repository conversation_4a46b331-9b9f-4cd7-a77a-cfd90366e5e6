<template>
  <div class="store-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-shop"></i>
          门店营销管理
        </h1>
        <p class="page-description">管理门店信息、NFC路径、推广二维码及各平台发布状态</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="addStore" class="add-btn">
          新增门店
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshData" class="refresh-btn">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="门店名称">
          <el-input
            v-model="searchForm.storeName"
            placeholder="请输入门店名称"
            clearable
            prefix-icon="el-icon-search"
            @keyup.enter.native="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态筛选">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="营销中" :value="1"></el-option>
            <el-option label="已暂停" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="素材状态">
          <el-select v-model="searchForm.materialStatus" placeholder="素材重复状态" clearable>
            <el-option label="无重复" :value="0"></el-option>
            <el-option label="有重复" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="storeList"
        style="width: 100%;"
        class="store-table"
        :header-cell-style="{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: '#fff',
          fontWeight: 'bold',
          fontSize: '14px',
          textAlign: 'center',
          borderColor: '#5a67d8'
        }"
        border
        stripe
        row-key="id"
      >
        <!-- 门店名称 -->
        <el-table-column label="门店名称" width="200" fixed="left">
          <template slot-scope="scope">
            <div class="store-info">
              <el-avatar :size="45" :src="scope.row.avatar" class="store-avatar">
                <i class="el-icon-shop"></i>
              </el-avatar>
              <div class="store-details">
                <div class="store-name">{{ scope.row.storeName }}</div>
                <div class="store-phone">
                  <i class="el-icon-phone"></i>
                  {{ scope.row.phone }}
                </div>
                <div class="store-address">
                  <i class="el-icon-location"></i>
                  {{ scope.row.address }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- NFC路径 -->
        <el-table-column label="NFC路径" width="180" align="center">
          <template slot-scope="scope">
            <div class="nfc-path">
              <div class="nfc-url">
                <el-input
                  :value="scope.row.nfcPath"
                  size="mini"
                  readonly
                  class="nfc-input"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-document-copy"
                    @click="copyNfcPath(scope.row.nfcPath)"
                    size="mini"
                  ></el-button>
                </el-input>
              </div>
              <div class="nfc-status">
                <el-tag :type="scope.row.nfcStatus ? 'success' : 'danger'" size="mini">
                  {{ scope.row.nfcStatus ? 'NFC已激活' : 'NFC未激活' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 推广二维码 -->
        <el-table-column label="推广二维码" width="120" align="center">
          <template slot-scope="scope">
            <div class="qrcode-section">
              <div class="qrcode-preview" @click="showQrcode(scope.row)">
                <img :src="scope.row.qrcodeUrl" alt="二维码" class="qrcode-img" />
              </div>
              <div class="qrcode-actions">
                <el-button type="text" size="mini" @click="showQrcode(scope.row)">
                  <i class="el-icon-view"></i> 查看
                </el-button>
                <el-button type="text" size="mini" @click="downloadQrcode(scope.row)">
                  <i class="el-icon-download"></i> 下载
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 素材状态 -->
        <el-table-column label="素材状态" width="180" align="center">
          <template slot-scope="scope">
            <div class="material-status">
              <div class="material-item">
                <span class="material-label">视频重复</span>
                <el-switch
                  v-model="scope.row.videoDuplicate"
                  size="mini"
                  @change="handleMaterialChange(scope.row, 'video', $event)"
                ></el-switch>
              </div>
              <div class="material-item">
                <span class="material-label">图片重复</span>
                <el-switch
                  v-model="scope.row.imageDuplicate"
                  size="mini"
                  @change="handleMaterialChange(scope.row, 'image', $event)"
                ></el-switch>
              </div>
              <div class="material-item">
                <span class="material-label">文案重复</span>
                <el-switch
                  v-model="scope.row.textDuplicate"
                  size="mini"
                  @change="handleMaterialChange(scope.row, 'text', $event)"
                ></el-switch>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column label="营销状态" width="120" align="center">
          <template slot-scope="scope">
            <div class="status-control">
              <el-switch
                v-model="scope.row.marketingStatus"
                :active-value="1"
                :inactive-value="0"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
              <div class="status-text" :class="scope.row.marketingStatus ? 'active' : 'inactive'">
                {{ scope.row.marketingStatus ? '营销中' : '已暂停' }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 短视频平台 -->
        <el-table-column label="短视频平台" width="200" align="center">
          <template slot-scope="scope">
            <div class="video-platforms">
              <div class="platform-grid">
                <div
                  v-for="platform in scope.row.videoPlatforms"
                  :key="platform.name"
                  class="platform-item"
                  :class="{ 'bound': platform.bound }"
                  @click="configurePlatform(scope.row, platform, 'video')"
                >
                  <i :class="platform.icon" :style="{ color: platform.color }"></i>
                  <span class="platform-name">{{ platform.displayName }}</span>
                  <i class="status-icon" :class="platform.bound ? 'el-icon-check' : 'el-icon-close'"></i>
                </div>
              </div>
              <div class="platform-summary">
                已绑定: {{ getBoundCount(scope.row.videoPlatforms) }}/{{ scope.row.videoPlatforms.length }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 图文/点评发布 -->
        <el-table-column label="图文/点评发布" width="200" align="center">
          <template slot-scope="scope">
            <div class="review-platforms">
              <div class="platform-grid">
                <div
                  v-for="platform in scope.row.reviewPlatforms"
                  :key="platform.name"
                  class="platform-item"
                  :class="{ 'bound': platform.bound }"
                  @click="configurePlatform(scope.row, platform, 'review')"
                >
                  <i :class="platform.icon" :style="{ color: platform.color }"></i>
                  <span class="platform-name">{{ platform.displayName }}</span>
                  <i class="status-icon" :class="platform.bound ? 'el-icon-check' : 'el-icon-close'"></i>
                </div>
              </div>
              <div class="platform-summary">
                已绑定: {{ getBoundCount(scope.row.reviewPlatforms) }}/{{ scope.row.reviewPlatforms.length }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button type="primary" size="mini" icon="el-icon-edit" @click="editStore(scope.row)">
                编辑
              </el-button>
              <el-button type="success" size="mini" icon="el-icon-view" @click="previewStore(scope.row)">
                预览
              </el-button>
              <el-button type="warning" size="mini" icon="el-icon-brush" @click="diyStore(scope.row)">
                DIY
              </el-button>
              <el-dropdown @command="handleCommand" trigger="click">
                <el-button type="info" size="mini">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{action: 'copy', row: scope.row}" icon="el-icon-document-copy">
                    复制链接
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'config', row: scope.row}" icon="el-icon-setting">
                    平台配置
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', row: scope.row}" icon="el-icon-delete" divided>
                    删除门店
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
      ></el-pagination>
    </div>

    <!-- 二维码对话框 -->
    <el-dialog
      title="推广二维码"
      :visible.sync="qrcodeDialogVisible"
      width="450px"
      center
      class="qrcode-dialog"
    >
      <div class="qrcode-dialog-content">
        <div class="store-info-header">
          <el-avatar :size="50" :src="selectedStore.avatar">
            <i class="el-icon-shop"></i>
          </el-avatar>
          <div class="store-info-text">
            <h3>{{ selectedStore.storeName }}</h3>
            <p>{{ selectedStore.address }}</p>
          </div>
        </div>

        <div class="qrcode-display">
          <div class="qrcode-container">
            <div id="qrcode" ref="qrcode" class="qrcode-image">
              <img :src="selectedStore.qrcodeUrl" alt="推广二维码" />
            </div>
          </div>
          <div class="qrcode-info">
            <p class="qrcode-tip">扫描二维码访问推广页面</p>
            <el-input
              :value="selectedStore.shareUrl"
              readonly
              size="small"
              class="share-url-input"
            >
              <el-button
                slot="append"
                icon="el-icon-document-copy"
                @click="copyShareUrl(selectedStore.shareUrl)"
              ></el-button>
            </el-input>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="qrcodeDialogVisible = false">关闭</el-button>
        <el-button type="primary" icon="el-icon-download" @click="downloadQrcode(selectedStore)">
          下载二维码
        </el-button>
      </span>
    </el-dialog>

    <!-- 短视频平台配置弹窗 -->
    <el-dialog
      title="短视频平台配置"
      :visible.sync="showVideoConfigDialog"
      width="600px"
      @close="resetVideoConfig"
    >
      <div v-if="currentPlatform" class="platform-config">
        <div class="platform-header">
          <i :class="currentPlatform.icon" :style="{ color: currentPlatform.color }"></i>
          <span class="platform-title">{{ currentPlatform.displayName }}配置</span>
        </div>

        <el-form :model="videoConfig" label-width="120px">
          <el-form-item label="视频库">
            <el-select v-model="videoConfig.videoLibrary" placeholder="请选择视频库" style="width: 100%">
              <el-option label="默认视频库" value="default"></el-option>
              <el-option label="产品展示视频库" value="product"></el-option>
              <el-option label="品牌宣传视频库" value="brand"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="文案库">
            <el-select v-model="videoConfig.copywritingLibrary" placeholder="请选择文案库" style="width: 100%">
              <el-option label="朋友圈文案库" value="moments"></el-option>
              <el-option label="营销文案库" value="marketing"></el-option>
              <el-option label="产品文案库" value="product"></el-option>
            </el-select>
          </el-form-item>

          <!-- POI地址配置（抖音需要） -->
          <el-form-item v-if="needsPOI(currentPlatform.name)" label="POI地址">
            <el-input
              v-model="videoConfig.poiAddress"
              placeholder="请输入POI地址"
              style="width: 100%"
            ></el-input>
            <div class="form-tip">请输入准确的门店地址，用于平台定位</div>
          </el-form-item>

          <el-form-item label="场景选择">
            <el-checkbox-group v-model="videoConfig.scenes">
              <el-checkbox label="日常营业">日常营业</el-checkbox>
              <el-checkbox label="促销活动">促销活动</el-checkbox>
              <el-checkbox label="新品发布">新品发布</el-checkbox>
              <el-checkbox label="节日庆典">节日庆典</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showVideoConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveVideoConfig">保存配置</el-button>
      </span>
    </el-dialog>

    <!-- 图文/点评平台配置弹窗 -->
    <el-dialog
      title="图文/点评平台配置"
      :visible.sync="showReviewConfigDialog"
      width="600px"
      @close="resetReviewConfig"
    >
      <div v-if="currentPlatform" class="platform-config">
        <div class="platform-header">
          <i :class="currentPlatform.icon" :style="{ color: currentPlatform.color }"></i>
          <span class="platform-title">{{ currentPlatform.displayName }}配置</span>
        </div>

        <el-form :model="reviewConfig" label-width="120px">
          <el-form-item label="图片库">
            <el-select v-model="reviewConfig.imageLibrary" placeholder="请选择图片库" style="width: 100%">
              <el-option label="默认图片库" value="default"></el-option>
              <el-option label="产品图片库" value="product"></el-option>
              <el-option label="环境图片库" value="environment"></el-option>
              <el-option label="活动图片库" value="activity"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="文案库">
            <el-select v-model="reviewConfig.copywritingLibrary" placeholder="请选择文案库" style="width: 100%">
              <el-option label="朋友圈文案库" value="moments"></el-option>
              <el-option label="点评文案库" value="review"></el-option>
              <el-option label="推广文案库" value="promotion"></el-option>
            </el-select>
          </el-form-item>

          <!-- POI地址配置（需要POI的平台） -->
          <el-form-item v-if="needsPOI(currentPlatform.name)" label="POI地址">
            <el-input
              v-model="reviewConfig.poiAddress"
              placeholder="请输入POI地址"
              style="width: 100%"
            ></el-input>
            <div class="form-tip">请输入准确的门店地址，用于平台定位</div>
          </el-form-item>

          <el-form-item label="场景选择">
            <el-checkbox-group v-model="reviewConfig.scenes">
              <el-checkbox label="产品展示">产品展示</el-checkbox>
              <el-checkbox label="环境展示">环境展示</el-checkbox>
              <el-checkbox label="服务体验">服务体验</el-checkbox>
              <el-checkbox label="优惠活动">优惠活动</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showReviewConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveReviewConfig">保存配置</el-button>
      </span>
    </el-dialog>

    <!-- 视频号特殊配置弹窗 -->
    <el-dialog
      title="视频号配置"
      :visible.sync="showVideoChannelDialog"
      width="600px"
      @close="resetVideoChannelConfig"
    >
      <div class="platform-config">
        <div class="platform-header">
          <i class="el-icon-video-camera-solid" style="color: #07c160"></i>
          <span class="platform-title">视频号配置</span>
        </div>

        <el-form :model="videoChannelConfig" label-width="120px">
          <el-form-item label="视频号ID" required>
            <el-input
              v-model="videoChannelConfig.channelId"
              placeholder="请输入以'sph'开头的视频号ID"
              style="width: 100%"
            ></el-input>
            <div class="form-tip">请在视频号个人中心获取以"sph"开头的ID或者视频号助手获取</div>
          </el-form-item>

          <el-form-item label="视频链接">
            <el-input
              v-model="videoChannelConfig.videoLink"
              placeholder="请输入视频链接"
              style="width: 100%"
            ></el-input>
            <div class="form-tip">请在视频号视频里面点击分享按钮，点击复制到此处</div>
          </el-form-item>

          <el-form-item label="文案库">
            <el-select v-model="videoChannelConfig.copywritingLibrary" placeholder="请选择文案库" style="width: 100%">
              <el-option label="朋友圈文案库" value="moments"></el-option>
              <el-option label="视频号文案库" value="video_channel"></el-option>
              <el-option label="营销文案库" value="marketing"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showVideoChannelDialog = false">取消</el-button>
        <el-button type="primary" @click="saveVideoChannelConfig">保存配置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Store',
  data() {
    return {
      // 搜索表单
      searchForm: {
        storeName: '',
        status: '',
        materialStatus: ''
      },

      // 配置弹窗状态
      showVideoConfigDialog: false,
      showReviewConfigDialog: false,
      showVideoChannelDialog: false,
      currentStore: null,
      currentPlatform: null,
      currentPlatformType: '',

      // 配置表单数据
      videoConfig: {
        videoLibrary: '',
        copywritingLibrary: '',
        scenes: []
      },
      reviewConfig: {
        imageLibrary: '',
        copywritingLibrary: '',
        poiAddress: '',
        scenes: []
      },
      videoChannelConfig: {
        channelId: '',
        videoLink: '',
        copywritingLibrary: ''
      },
      // 门店列表
      storeList: [
        {
          id: 1,
          storeName: '星巴克咖啡店',
          phone: '138****1234',
          address: '北京市朝阳区建国门外大街1号',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          marketingStatus: 1,
          nfcPath: 'https://nfc.demo.com/store/1',
          nfcStatus: true,
          qrcodeUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          videoDuplicate: false,
          imageDuplicate: true,
          textDuplicate: false,
          shareUrl: 'https://marketing.demo.com/store/1/share',
          // 短视频平台
          videoPlatforms: [
            { name: 'douyin', displayName: '抖音', icon: 'el-icon-video-play', color: '#fe2c55', bound: true },
            { name: 'kuaishou', displayName: '快手', icon: 'el-icon-video-camera', color: '#ff6348', bound: true },
            { name: 'xiaohongshu', displayName: '小红书', icon: 'el-icon-star-on', color: '#ff4757', bound: false },
            { name: 'pengyouquan_video', displayName: '朋友圈', icon: 'el-icon-chat-dot-round', color: '#07c160', bound: true },
            { name: 'shipinhao', displayName: '视频号', icon: 'el-icon-video-camera-solid', color: '#07c160', bound: true }
          ],
          // 图文/点评平台
          reviewPlatforms: [
            { name: 'douyin_review', displayName: '抖音点评', icon: 'el-icon-video-play', color: '#fe2c55', bound: true },
            { name: 'meituan', displayName: '美团', icon: 'el-icon-food', color: '#ffc107', bound: true },
            { name: 'dianping', displayName: '大众点评', icon: 'el-icon-star-off', color: '#ff6600', bound: true },
            { name: 'xiaohongshu_text', displayName: '小红书图文', icon: 'el-icon-star-on', color: '#ff4757', bound: false },
            { name: 'pengyouquan_text', displayName: '朋友圈', icon: 'el-icon-chat-dot-round', color: '#07c160', bound: true },
            { name: 'gaode', displayName: '高德', icon: 'el-icon-location', color: '#00a6fb', bound: false },
            { name: 'baidu', displayName: '百度', icon: 'el-icon-search', color: '#2932e1', bound: true }
          ]
        },
        {
          id: 2,
          storeName: '麦当劳快餐店',
          phone: '139****5678',
          address: '上海市浦东新区陆家嘴环路1000号',
          avatar: 'https://wpimg.wallstcn.com/57ed425a-c71e-4201-9428-68760c0537c4.gif',
          marketingStatus: 0,
          nfcPath: 'https://nfc.demo.com/store/2',
          nfcStatus: false,
          qrcodeUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          videoDuplicate: true,
          imageDuplicate: false,
          textDuplicate: true,
          shareUrl: 'https://marketing.demo.com/store/2/share',
          // 短视频平台
          videoPlatforms: [
            { name: 'douyin', displayName: '抖音', icon: 'el-icon-video-play', color: '#fe2c55', bound: false },
            { name: 'kuaishou', displayName: '快手', icon: 'el-icon-video-camera', color: '#ff6348', bound: true },
            { name: 'xiaohongshu', displayName: '小红书', icon: 'el-icon-star-on', color: '#ff4757', bound: true },
            { name: 'pengyouquan_video', displayName: '朋友圈', icon: 'el-icon-chat-dot-round', color: '#07c160', bound: false },
            { name: 'shipinhao', displayName: '视频号', icon: 'el-icon-video-camera-solid', color: '#07c160', bound: false }
          ],
          // 图文/点评平台
          reviewPlatforms: [
            { name: 'douyin_review', displayName: '抖音点评', icon: 'el-icon-video-play', color: '#fe2c55', bound: false },
            { name: 'meituan', displayName: '美团', icon: 'el-icon-food', color: '#ffc107', bound: true },
            { name: 'dianping', displayName: '大众点评', icon: 'el-icon-star-off', color: '#ff6600', bound: false },
            { name: 'xiaohongshu_text', displayName: '小红书图文', icon: 'el-icon-star-on', color: '#ff4757', bound: true },
            { name: 'pengyouquan_text', displayName: '朋友圈', icon: 'el-icon-chat-dot-round', color: '#07c160', bound: false },
            { name: 'gaode', displayName: '高德', icon: 'el-icon-location', color: '#00a6fb', bound: true },
            { name: 'baidu', displayName: '百度', icon: 'el-icon-search', color: '#2932e1', bound: true }
          ]
        }
      ],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 二维码对话框
      qrcodeDialogVisible: false,
      selectedStore: {}
    }
  },
  created() {
    this.loadStoreList()
  },
  methods: {
    loadStoreList() {
      // 加载门店列表
      console.log('加载门店列表')
      this.total = this.storeList.length
    },
    refreshData() {
      this.loadStoreList()
      this.$message.success('数据已刷新')
    },
    handleSearch() {
      this.currentPage = 1
      this.loadStoreList()
    },
    resetSearch() {
      this.searchForm = {
        storeName: '',
        status: '',
        materialStatus: ''
      }
      this.handleSearch()
    },
    handleStatusChange(row) {
      this.$message.success(`${row.storeName} 营销状态已${row.marketingStatus ? '开启' : '关闭'}`)
    },
    getBoundCount(platforms) {
      return platforms.filter(p => p.bound).length
    },
    // 复制NFC路径
    copyNfcPath(path) {
      navigator.clipboard.writeText(path).then(() => {
        this.$message.success('NFC路径已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    // 显示二维码
    showQrcode(row) {
      this.selectedStore = row
      this.qrcodeDialogVisible = true
      this.$nextTick(() => {
        this.generateQrcode(row.shareUrl)
      })
    },
    // 生成二维码
    generateQrcode(url) {
      // 这里可以使用二维码生成库
      console.log('生成二维码:', url)
    },
    // 下载二维码
    downloadQrcode(row) {
      this.$message.success(`正在下载 ${row.storeName} 的二维码`)
    },
    // 配置平台
    configurePlatform(store, platform, type) {
      this.currentStore = store
      this.currentPlatform = platform
      this.currentPlatformType = type

      // 根据平台类型打开不同的配置弹窗
      if (platform.name === 'shipinhao') {
        // 视频号特殊配置
        this.showVideoChannelDialog = true
      } else if (type === 'video') {
        // 短视频平台配置
        this.showVideoConfigDialog = true
      } else if (type === 'review') {
        // 图文/点评平台配置
        this.showReviewConfigDialog = true
      }
    },
    // 预览门店
    previewStore(row) {
      // 打开推广页面预览
      const promotionUrl = `/promotion/${row.id}`
      const fullUrl = window.location.origin + this.$router.resolve({ path: promotionUrl }).href
      window.open(fullUrl, '_blank')
    },

    // DIY编辑门店
    diyStore(row) {
      // 打开DIY编辑器
      const diyUrl = `/promotion/${row.id}/diy`
      this.$router.push(diyUrl)
    },
    // 处理下拉菜单命令
    handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'copy':
          this.copyShareUrl(row.shareUrl)
          break
        case 'config':
          this.showPlatformDialog(row, 'all')
          break
        case 'delete':
          this.deleteStore(row)
          break
      }
    },
    // 复制分享链接
    copyShareUrl(url) {
      navigator.clipboard.writeText(url).then(() => {
        this.$message.success('分享链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    showPlatformDialog(row, type) {
      this.$message.info(`正在配置 ${row.storeName} 的平台设置`)
    },
    addStore() {
      this.$message.info('新增门店功能开发中...')
    },

    // 处理素材状态变化
    handleMaterialChange(row, materialType, value) {
      const materialNames = {
        video: '视频',
        image: '图片',
        text: '文案'
      }
      const statusText = value ? '标记为重复' : '取消重复标记'
      this.$message.success(`${row.storeName} ${materialNames[materialType]}${statusText}`)

      // 这里可以添加API调用来保存状态到后端
      console.log(`门店: ${row.storeName}, 素材类型: ${materialType}, 重复状态: ${value}`)
    },

    // 判断平台是否需要POI地址
    needsPOI(platformName) {
      const poiPlatforms = ['douyin', 'douyin_review', 'gaode', 'baidu', 'meituan', 'dianping']
      return poiPlatforms.includes(platformName)
    },

    // 重置短视频配置
    resetVideoConfig() {
      this.videoConfig = {
        videoLibrary: '',
        copywritingLibrary: '',
        poiAddress: '',
        scenes: []
      }
    },

    // 重置图文/点评配置
    resetReviewConfig() {
      this.reviewConfig = {
        imageLibrary: '',
        copywritingLibrary: '',
        poiAddress: '',
        scenes: []
      }
    },

    // 重置视频号配置
    resetVideoChannelConfig() {
      this.videoChannelConfig = {
        channelId: '',
        videoLink: '',
        copywritingLibrary: ''
      }
    },

    // 保存短视频配置
    saveVideoConfig() {
      if (!this.videoConfig.videoLibrary) {
        this.$message.error('请选择视频库')
        return
      }
      if (!this.videoConfig.copywritingLibrary) {
        this.$message.error('请选择文案库')
        return
      }
      if (this.needsPOI(this.currentPlatform.name) && !this.videoConfig.poiAddress) {
        this.$message.error('请输入POI地址')
        return
      }

      // 绑定平台
      this.currentPlatform.bound = true

      this.$message.success(`${this.currentStore.storeName} ${this.currentPlatform.displayName} 配置保存成功`)
      this.showVideoConfigDialog = false

      // 这里可以调用API保存配置到后端
      console.log('短视频配置:', {
        store: this.currentStore.storeName,
        platform: this.currentPlatform.displayName,
        config: this.videoConfig
      })
    },

    // 保存图文/点评配置
    saveReviewConfig() {
      if (!this.reviewConfig.imageLibrary) {
        this.$message.error('请选择图片库')
        return
      }
      if (!this.reviewConfig.copywritingLibrary) {
        this.$message.error('请选择文案库')
        return
      }
      if (this.needsPOI(this.currentPlatform.name) && !this.reviewConfig.poiAddress) {
        this.$message.error('请输入POI地址')
        return
      }

      // 绑定平台
      this.currentPlatform.bound = true

      this.$message.success(`${this.currentStore.storeName} ${this.currentPlatform.displayName} 配置保存成功`)
      this.showReviewConfigDialog = false

      // 这里可以调用API保存配置到后端
      console.log('图文/点评配置:', {
        store: this.currentStore.storeName,
        platform: this.currentPlatform.displayName,
        config: this.reviewConfig
      })
    },

    // 保存视频号配置
    saveVideoChannelConfig() {
      if (!this.videoChannelConfig.channelId) {
        this.$message.error('请输入视频号ID')
        return
      }
      if (!this.videoChannelConfig.channelId.startsWith('sph')) {
        this.$message.error('视频号ID必须以"sph"开头')
        return
      }
      if (!this.videoChannelConfig.copywritingLibrary) {
        this.$message.error('请选择文案库')
        return
      }

      // 绑定平台
      this.currentPlatform.bound = true

      this.$message.success(`${this.currentStore.storeName} 视频号配置保存成功`)
      this.showVideoChannelDialog = false

      // 这里可以调用API保存配置到后端
      console.log('视频号配置:', {
        store: this.currentStore.storeName,
        platform: this.currentPlatform.displayName,
        config: this.videoChannelConfig
      })
    },
    editStore(row) {
      this.$message.info(`正在编辑门店: ${row.storeName}`)
    },
    deleteStore(row) {
      this.$confirm(`确定要删除门店 "${row.storeName}" 吗？`, '删除门店', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从列表中删除
        const index = this.storeList.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.storeList.splice(index, 1)
          this.total = this.storeList.length
        }
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.loadStoreList()
    },
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadStoreList()
    }
  }
}
</script>

<style lang="scss" scoped>
.store-management {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: #fff;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    position: relative;
    z-index: 1;

    .page-title {
      font-size: 28px;
      font-weight: 700;
      color: #fff;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      text-shadow: 0 2px 4px rgba(0,0,0,0.2);

      i {
        margin-right: 16px;
        font-size: 32px;
        color: #ffd700;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
      }
    }

    .page-description {
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      font-size: 16px;
      font-weight: 300;
    }
  }

  .header-actions {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 12px;

    .add-btn {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border: none;
      box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
      }
    }

    .refresh-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: #fff;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

.search-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

// 表格样式
.store-table {
  border-radius: 12px;
  overflow: hidden;

  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      border-radius: 12px 12px 0 0;
    }
  }

  ::v-deep .el-table__body-wrapper {
    .el-table__row {
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8f0fe 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
      }
    }
  }
}

// 门店信息样式
.store-info {
  display: flex;
  align-items: center;
  padding: 8px 0;

  .store-avatar {
    margin-right: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 3px solid #fff;
  }

  .store-details {
    flex: 1;

    .store-name {
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 6px;
      font-size: 16px;
    }

    .store-phone, .store-address {
      font-size: 12px;
      color: #7f8c8d;
      margin-bottom: 3px;
      display: flex;
      align-items: center;

      i {
        margin-right: 4px;
        color: #667eea;
      }
    }
  }
}

// NFC路径样式
.nfc-path {
  .nfc-input {
    margin-bottom: 8px;

    ::v-deep .el-input__inner {
      font-size: 11px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
    }
  }

  .nfc-status {
    text-align: center;
  }
}

// 二维码样式
.qrcode-section {
  text-align: center;

  .qrcode-preview {
    margin-bottom: 8px;
    cursor: pointer;

    .qrcode-img {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        border-color: #667eea;
        transform: scale(1.05);
      }
    }
  }

  .qrcode-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
}

// 素材状态样式
.material-status {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 4px;
}

.material-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  min-height: 20px;
}

.material-label {
  color: #606266;
  font-size: 11px;
  white-space: nowrap;
  margin-right: 8px;
}

// 状态控制样式
.status-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  .status-text {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;

    &.active {
      color: #27ae60;
      background: #d5f4e6;
    }

    &.inactive {
      color: #e74c3c;
      background: #fadbd8;
    }
  }
}

// 平台样式
.video-platforms, .review-platforms {
  .platform-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    margin-bottom: 8px;
  }

  .platform-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    background: #f8f9fa;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.bound {
      background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
      border-color: #27ae60;

      .status-icon {
        color: #27ae60;
      }
    }

    i:first-child {
      margin-right: 6px;
      font-size: 14px;
    }

    .platform-name {
      flex: 1;
      font-size: 11px;
      font-weight: 600;
    }

    .status-icon {
      font-size: 12px;
      color: #bdc3c7;
    }
  }

  .platform-summary {
    font-size: 11px;
    color: #7f8c8d;
    text-align: center;
    font-weight: 600;
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 3px;
  justify-content: center;
  align-items: stretch;  // 让所有按钮宽度一致
  height: 100%;
  padding: 8px 4px;
  width: 100%;

  .el-button {
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 11px;
    padding: 3px 6px;
    width: 100%;  // 占满容器宽度
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    box-sizing: border-box;
    margin: 0;  // 确保没有外边距

    &:hover {
      transform: translateY(-1px);
    }

    // 隐藏图标，只显示文字
    i {
      display: none;
    }
  }

  .el-dropdown {
    width: 100%;  // 下拉框也占满宽度

    .el-button {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
      border: none;
      color: #fff;
      width: 100%;
      height: 24px;
    }
  }
}

// 分页样式
.pagination-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

// 二维码对话框样式
.qrcode-dialog {
  ::v-deep .el-dialog {
    border-radius: 16px;
    overflow: hidden;
  }

  .qrcode-dialog-content {
    .store-info-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      padding: 16px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;

      .store-info-text {
        margin-left: 16px;

        h3 {
          margin: 0 0 4px 0;
          color: #2c3e50;
          font-weight: 700;
        }

        p {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
        }
      }
    }

    .qrcode-display {
      text-align: center;

      .qrcode-container {
        margin-bottom: 20px;

        .qrcode-image {
          display: inline-block;
          padding: 20px;
          background: #fff;
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

          img {
            width: 200px;
            height: 200px;
            border-radius: 8px;
          }
        }
      }

      .qrcode-info {
        .qrcode-tip {
          margin-bottom: 16px;
          color: #7f8c8d;
          font-size: 14px;
        }

        .share-url-input {
          ::v-deep .el-input__inner {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;

    .header-actions {
      margin-top: 16px;
      width: 100%;
      justify-content: flex-start;
    }
  }

  .store-table {
    ::v-deep .el-table__body-wrapper {
      overflow-x: auto;
    }
  }

  .platform-grid {
    grid-template-columns: 1fr !important;
  }

  .action-buttons {
    flex-direction: column;

    .el-button {
      width: 100%;
      margin: 2px 0;
    }
  }
}

// 平台配置弹窗样式
.platform-config {
  .platform-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;

    i {
      font-size: 24px;
      margin-right: 10px;
    }

    .platform-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.4;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 10px;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>