package com.ruoyi.system.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.IAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.AiCopywritingLibrary;
import com.ruoyi.system.domain.AiCopywritingContent;
import com.ruoyi.system.service.IAiCopywritingService;
import okhttp3.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * AI文案生成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class AiCopywritingServiceImpl implements IAiCopywritingService 
{
    private static final Logger log = LoggerFactory.getLogger(AiCopywritingServiceImpl.class);

    @Autowired
    private IAiService aiService;

    // 模拟数据存储 (实际项目中应该使用数据库)
    private static final Map<Long, AiCopywritingLibrary> libraryStorage = new HashMap<>();
    private static final Map<Long, List<AiCopywritingContent>> contentStorage = new HashMap<>();
    private static Long libraryIdCounter = 1L;
    private static Long contentIdCounter = 1L;

    static {
        // 初始化一些示例数据
        initSampleData();
    }

    private static void initSampleData() {
        // 示例文案库1
        AiCopywritingLibrary library1 = new AiCopywritingLibrary();
        library1.setLibraryId(1L);
        library1.setLibraryName("美食探店文案库");
        library1.setUseAi(true);
        library1.setShopDetails("我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。");
        library1.setPrompt("生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围");
        library1.setTargetCount(20);
        library1.setGeneratedCount(20);
        library1.setWordCount(100);
        library1.setStatus("completed");
        library1.setCreateTime(new Date());
        libraryStorage.put(1L, library1);

        // 示例文案库2
        AiCopywritingLibrary library2 = new AiCopywritingLibrary();
        library2.setLibraryId(2L);
        library2.setLibraryName("时尚服装推广库");
        library2.setUseAi(true);
        library2.setShopDetails("时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。");
        library2.setPrompt("创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议");
        library2.setTargetCount(30);
        library2.setGeneratedCount(15);
        library2.setWordCount(150);
        library2.setStatus("generating");
        library2.setCreateTime(new Date());
        libraryStorage.put(2L, library2);

        // 示例文案库3 - 用户创建的测试库
        AiCopywritingLibrary library3 = new AiCopywritingLibrary();
        library3.setLibraryId(3L);
        library3.setLibraryName("用户测试文案库");
        library3.setUseAi(true);
        library3.setShopDetails("测试商户：一家温馨的咖啡厅，主营手工咖啡和精致甜点");
        library3.setPrompt("生成吸引人的美食推广文案，要求语言生动、有食欲感");
        library3.setTargetCount(5);
        library3.setGeneratedCount(3);
        library3.setWordCount(100);
        library3.setStatus("completed");
        library3.setCreateTime(new Date());
        library3.setCreateBy("system");
        libraryStorage.put(3L, library3);

        libraryIdCounter = 4L;

        // 示例文案内容
        List<AiCopywritingContent> contents1 = new ArrayList<>();
        AiCopywritingContent content1 = new AiCopywritingContent();
        content1.setContentId(1L);
        content1.setLibraryId(1L);
        content1.setContent("今天给大家推荐一道超级下饭的川菜——水煮鱼！鲜嫩的鱼肉配上麻辣的汤底，每一口都是满满的幸福感。来我们店里品尝正宗川菜，让你的味蕾来一场麻辣之旅！");
        content1.setTitle("AI生成-水煮鱼推广");
        content1.setWordCount(68);
        content1.setIsAiGenerated(true);
        content1.setStatus("active");
        content1.setQualityScore(85);
        content1.setCreateTime(new Date());
        contents1.add(content1);

        AiCopywritingContent content2 = new AiCopywritingContent();
        content2.setContentId(2L);
        content2.setLibraryId(1L);
        content2.setContent("想要品尝最正宗的宫保鸡丁吗？我们的大厨有着20年的川菜制作经验，每一道菜都是用心调制。酸甜适中的口感，嫩滑的鸡肉，让你一吃就停不下来！");
        content2.setTitle("AI生成-宫保鸡丁推广");
        content2.setWordCount(72);
        content2.setIsAiGenerated(true);
        content2.setStatus("active");
        content2.setQualityScore(90);
        content2.setCreateTime(new Date());
        contents1.add(content2);

        contentStorage.put(1L, contents1);

        // 为文案库3添加示例内容
        List<AiCopywritingContent> contents3 = new ArrayList<>();

        AiCopywritingContent content3_1 = new AiCopywritingContent();
        content3_1.setContentId(3L);
        content3_1.setLibraryId(3L);
        content3_1.setContent("☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。快来体验我们的温馨服务吧！");
        content3_1.setTitle("AI生成-咖啡厅推广文案1");
        content3_1.setWordCount(78);
        content3_1.setIsAiGenerated(true);
        content3_1.setStatus("active");
        content3_1.setQualityScore(88);
        content3_1.setCreateTime(new Date());
        contents3.add(content3_1);

        AiCopywritingContent content3_2 = new AiCopywritingContent();
        content3_2.setContentId(4L);
        content3_2.setLibraryId(3L);
        content3_2.setContent("🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱！");
        content3_2.setTitle("AI生成-咖啡厅推广文案2");
        content3_2.setWordCount(71);
        content3_2.setIsAiGenerated(true);
        content3_2.setStatus("active");
        content3_2.setQualityScore(92);
        content3_2.setCreateTime(new Date());
        contents3.add(content3_2);

        AiCopywritingContent content3_3 = new AiCopywritingContent();
        content3_3.setContentId(5L);
        content3_3.setLibraryId(3L);
        content3_3.setContent("💫 邂逅美好时光！在这里，每一杯咖啡都承载着匠心，每一块甜点都饱含温情。快来我们的咖啡厅，让味蕾与心灵同时得到满足！");
        content3_3.setTitle("AI生成-咖啡厅推广文案3");
        content3_3.setWordCount(75);
        content3_3.setIsAiGenerated(true);
        content3_3.setStatus("active");
        content3_3.setQualityScore(90);
        content3_3.setCreateTime(new Date());
        contents3.add(content3_3);

        contentStorage.put(3L, contents3);
        contentIdCounter = 6L;
    }

    /**
     * 查询文案库列表
     */
    @Override
    public List<AiCopywritingLibrary> selectLibraryList(AiCopywritingLibrary aiCopywritingLibrary)
    {
        List<AiCopywritingLibrary> result = new ArrayList<>(libraryStorage.values());
        
        // 简单的筛选逻辑
        if (aiCopywritingLibrary != null) {
            if (aiCopywritingLibrary.getLibraryName() != null && !aiCopywritingLibrary.getLibraryName().isEmpty()) {
                result = result.stream()
                    .filter(lib -> lib.getLibraryName().contains(aiCopywritingLibrary.getLibraryName()))
                    .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
            }
            if (aiCopywritingLibrary.getStatus() != null && !aiCopywritingLibrary.getStatus().isEmpty()) {
                result = result.stream()
                    .filter(lib -> lib.getStatus().equals(aiCopywritingLibrary.getStatus()))
                    .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
            }
        }
        
        // 按创建时间倒序排列
        result.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        return result;
    }

    /**
     * 查询文案库详细信息
     */
    @Override
    public AiCopywritingLibrary selectLibraryById(Long libraryId)
    {
        return libraryStorage.get(libraryId);
    }

    /**
     * 创建文案库
     */
    @Override
    public int createLibrary(AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            aiCopywritingLibrary.setLibraryId(libraryIdCounter++);
            aiCopywritingLibrary.setGeneratedCount(0);
            aiCopywritingLibrary.setStatus(aiCopywritingLibrary.getUseAi() ? "pending" : "completed");
            aiCopywritingLibrary.setCreateTime(new Date());

            // 安全获取用户名，如果获取失败则使用默认值
            try {
                aiCopywritingLibrary.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                aiCopywritingLibrary.setCreateBy("system");
                log.warn("无法获取当前用户名，使用默认值: system");
            }

            libraryStorage.put(aiCopywritingLibrary.getLibraryId(), aiCopywritingLibrary);
            contentStorage.put(aiCopywritingLibrary.getLibraryId(), new ArrayList<>());

            log.info("创建文案库成功，ID: {}, 名称: {}", aiCopywritingLibrary.getLibraryId(), aiCopywritingLibrary.getLibraryName());

            // 如果使用AI生成，启动异步生成任务
            if (aiCopywritingLibrary.getUseAi()) {
                log.info("启动AI文案生成任务，文案库ID: {}", aiCopywritingLibrary.getLibraryId());
                generateCopywritingAsync(aiCopywritingLibrary);
            }

            return 1;
        } catch (Exception e) {
            log.error("创建文案库失败", e);
            return 0;
        }
    }

    /**
     * 修改文案库
     */
    @Override
    public int updateLibrary(AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            AiCopywritingLibrary existing = libraryStorage.get(aiCopywritingLibrary.getLibraryId());
            if (existing != null) {
                existing.setLibraryName(aiCopywritingLibrary.getLibraryName());
                existing.setShopDetails(aiCopywritingLibrary.getShopDetails());
                existing.setPrompt(aiCopywritingLibrary.getPrompt());
                existing.setTargetCount(aiCopywritingLibrary.getTargetCount());
                existing.setWordCount(aiCopywritingLibrary.getWordCount());
                existing.setUpdateTime(new Date());

                // 安全获取用户名，如果获取失败则使用默认值
                try {
                    existing.setUpdateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    existing.setUpdateBy("system");
                    log.warn("无法获取当前用户名，使用默认值: system");
                }
                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("修改文案库失败", e);
            return 0;
        }
    }

    /**
     * 批量删除文案库
     */
    @Override
    public int deleteLibraryByIds(Long[] libraryIds)
    {
        try {
            int count = 0;
            for (Long libraryId : libraryIds) {
                if (libraryStorage.remove(libraryId) != null) {
                    contentStorage.remove(libraryId);
                    count++;
                }
            }
            return count;
        } catch (Exception e) {
            log.error("删除文案库失败", e);
            return 0;
        }
    }

    /**
     * 异步生成文案
     */
    @Override
    public void generateCopywritingAsync(AiCopywritingLibrary aiCopywritingLibrary)
    {
        CompletableFuture.runAsync(() -> {
            try {
                AiCopywritingLibrary library = libraryStorage.get(aiCopywritingLibrary.getLibraryId());
                if (library == null) {
                    log.error("文案库不存在: {}", aiCopywritingLibrary.getLibraryId());
                    return;
                }

                library.setStatus("generating");
                
                List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(library.getLibraryId(), k -> new ArrayList<>());
                
                for (int i = 0; i < library.getTargetCount(); i++) {
                    try {
                        // 调用火山引擎Doubao AI生成文案
                        String generatedContent = aiService.generateCopywriting(
                            library.getPrompt(),
                            library.getShopDetails(),
                            library.getWordCount() * 2
                        );
                        
                        // 创建文案内容记录
                        AiCopywritingContent content = new AiCopywritingContent();
                        content.setContentId(contentIdCounter++);
                        content.setLibraryId(library.getLibraryId());
                        content.setContent(generatedContent);
                        content.setTitle("AI生成-" + (i + 1));
                        content.setWordCount(generatedContent.length());
                        content.setIsAiGenerated(true);
                        content.setStatus("active");
                        content.setQualityScore(80 + new Random().nextInt(20)); // 随机质量评分
                        content.setCreateTime(new Date());
                        
                        contents.add(content);
                        library.setGeneratedCount(library.getGeneratedCount() + 1);
                        
                        // 模拟生成间隔
                        Thread.sleep(2000);
                        
                    } catch (Exception e) {
                        log.error("生成第{}条文案失败", i + 1, e);
                        library.setStatus("failed");
                        library.setErrorMessage("生成失败: " + e.getMessage());
                        return;
                    }
                }
                
                library.setStatus("completed");
                log.info("文案库 {} 生成完成，共生成 {} 条文案", library.getLibraryName(), library.getGeneratedCount());
                
            } catch (Exception e) {
                log.error("异步生成文案失败", e);
            }
        });
    }

    /**
     * 查询文案内容列表
     */
    @Override
    public List<AiCopywritingContent> selectContentByLibraryId(Long libraryId)
    {
        List<AiCopywritingContent> contents = contentStorage.get(libraryId);
        return contents != null ? new ArrayList<>(contents) : new ArrayList<>();
    }

    /**
     * 新增文案内容
     */
    @Override
    public int insertContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            aiCopywritingContent.setContentId(contentIdCounter++);
            aiCopywritingContent.setIsAiGenerated(false);
            aiCopywritingContent.setStatus("active");
            aiCopywritingContent.setWordCount(aiCopywritingContent.getContent().length());
            aiCopywritingContent.setCreateTime(new Date());
            aiCopywritingContent.setCreateBy(SecurityUtils.getUsername());

            List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(aiCopywritingContent.getLibraryId(), k -> new ArrayList<>());
            contents.add(aiCopywritingContent);

            // 更新文案库的生成计数
            AiCopywritingLibrary library = libraryStorage.get(aiCopywritingContent.getLibraryId());
            if (library != null) {
                library.setGeneratedCount(library.getGeneratedCount() + 1);
            }

            return 1;
        } catch (Exception e) {
            log.error("新增文案内容失败", e);
            return 0;
        }
    }

    /**
     * 生成单条文案内容
     */
    @Override
    public int generateSingleContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            AiCopywritingLibrary library = libraryStorage.get(aiCopywritingContent.getLibraryId());
            if (library == null) {
                throw new RuntimeException("文案库不存在");
            }

            if (aiCopywritingContent.getUseAi()) {
                // 使用AI生成
                for (int i = 0; i < aiCopywritingContent.getCount(); i++) {
                    String generatedContent = aiService.generateCopywriting(
                        aiCopywritingContent.getPrompt() != null ? aiCopywritingContent.getPrompt() : library.getPrompt(),
                        aiCopywritingContent.getShopDetails() != null ? aiCopywritingContent.getShopDetails() : library.getShopDetails(),
                        library.getWordCount() * 2
                    );

                    AiCopywritingContent content = new AiCopywritingContent();
                    content.setContentId(contentIdCounter++);
                    content.setLibraryId(aiCopywritingContent.getLibraryId());
                    content.setContent(generatedContent);
                    content.setTitle("AI生成-" + System.currentTimeMillis());
                    content.setWordCount(generatedContent.length());
                    content.setIsAiGenerated(true);
                    content.setStatus("active");
                    content.setQualityScore(80 + new Random().nextInt(20));
                    content.setCreateTime(new Date());
                    content.setCreateBy(SecurityUtils.getUsername());

                    List<AiCopywritingContent> contents = contentStorage.computeIfAbsent(aiCopywritingContent.getLibraryId(), k -> new ArrayList<>());
                    contents.add(content);

                    library.setGeneratedCount(library.getGeneratedCount() + 1);
                }
            } else {
                // 手动添加
                return insertContent(aiCopywritingContent);
            }

            return 1;
        } catch (Exception e) {
            log.error("生成单条文案内容失败", e);
            throw new RuntimeException("生成文案失败: " + e.getMessage());
        }
    }

    /**
     * 修改文案内容
     */
    @Override
    public int updateContent(AiCopywritingContent aiCopywritingContent)
    {
        try {
            List<AiCopywritingContent> contents = contentStorage.get(aiCopywritingContent.getLibraryId());
            if (contents != null) {
                for (AiCopywritingContent content : contents) {
                    if (content.getContentId().equals(aiCopywritingContent.getContentId())) {
                        content.setContent(aiCopywritingContent.getContent());
                        content.setTitle(aiCopywritingContent.getTitle());
                        content.setWordCount(aiCopywritingContent.getContent().length());
                        content.setUpdateTime(new Date());
                        content.setUpdateBy(SecurityUtils.getUsername());
                        return 1;
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("修改文案内容失败", e);
            return 0;
        }
    }

    /**
     * 批量删除文案内容
     */
    @Override
    public int deleteContentByIds(Long[] contentIds)
    {
        try {
            int count = 0;
            for (Long contentId : contentIds) {
                for (List<AiCopywritingContent> contents : contentStorage.values()) {
                    if (contents.removeIf(content -> content.getContentId().equals(contentId))) {
                        count++;
                        break;
                    }
                }
            }
            return count;
        } catch (Exception e) {
            log.error("删除文案内容失败", e);
            return 0;
        }
    }

    /**
     * 获取生成进度
     */
    @Override
    public Map<String, Object> getGenerationProgress(Long libraryId)
    {
        Map<String, Object> progress = new HashMap<>();
        AiCopywritingLibrary library = libraryStorage.get(libraryId);

        if (library != null) {
            progress.put("libraryId", libraryId);
            progress.put("status", library.getStatus());
            progress.put("targetCount", library.getTargetCount());
            progress.put("generatedCount", library.getGeneratedCount());
            progress.put("progress", library.getTargetCount() > 0 ?
                (library.getGeneratedCount() * 100 / library.getTargetCount()) : 0);
            progress.put("errorMessage", library.getErrorMessage());
        }

        return progress;
    }

    /**
     * 重新生成文案库
     */
    @Override
    public void regenerateLibrary(Long libraryId)
    {
        AiCopywritingLibrary library = libraryStorage.get(libraryId);
        if (library != null) {
            // 清空现有内容
            contentStorage.put(libraryId, new ArrayList<>());
            library.setGeneratedCount(0);
            library.setStatus("pending");
            library.setErrorMessage(null);

            // 重新生成
            generateCopywritingAsync(library);
        }
    }
}
