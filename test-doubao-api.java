import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 火山引擎Doubao API测试类
 * 用于验证API接口是否正常工作
 */
public class TestDoubaoApi {
    
    private static final String API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
    private static final String API_KEY = "5ad57720-913c-410e-b75f-debd2fe836a4z";
    private static final String MODEL = "doubao-seed-1-6-flash-250715";
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final OkHttpClient httpClient = new OkHttpClient();
    
    public static void main(String[] args) {
        TestDoubaoApi test = new TestDoubaoApi();
        test.testApi();
    }
    
    public void testApi() {
        try {
            System.out.println("=== 开始测试火山引擎Doubao API ===");
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", MODEL);
            requestBody.put("max_tokens", 100);
            requestBody.put("stream", false);
            
            // 构建消息
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", "你好，请简单介绍一下自己");
            requestBody.put("messages", new Object[]{message});
            
            // 转换为JSON
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            System.out.println("请求体: " + jsonBody);
            
            // 创建请求体
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json"));
            
            // 创建请求
            Request request = new Request.Builder()
                .url(API_URL)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .post(body)
                .build();
            
            System.out.println("发送HTTP请求到: " + API_URL);
            
            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                System.out.println("响应状态码: " + response.code());
                
                String responseBody = response.body().string();
                System.out.println("响应内容: " + responseBody);
                
                if (response.isSuccessful()) {
                    // 解析响应
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);
                    
                    if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray() &&
                        jsonResponse.get("choices").size() > 0) {
                        
                        JsonNode firstChoice = jsonResponse.get("choices").get(0);
                        if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                            String generatedContent = firstChoice.get("message").get("content").asText();
                            System.out.println("=== API测试成功 ===");
                            System.out.println("生成的内容: " + generatedContent);
                        }
                    }
                } else {
                    System.err.println("API调用失败，状态码: " + response.code());
                    System.err.println("错误响应: " + responseBody);
                }
            }
            
        } catch (IOException e) {
            System.err.println("网络请求失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
