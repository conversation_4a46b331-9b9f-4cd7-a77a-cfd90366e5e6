Write-Host "Testing Doubao API..." -ForegroundColor Green

$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer 5ad57720-913c-410e-b75f-debd2fe836a4z'
}

$body = '{
    "model": "doubao-seed-1-6-flash-250715",
    "messages": [
        {
            "role": "system",
            "content": "You are a helpful assistant."
        },
        {
            "role": "user",
            "content": "Hello!"
        }
    ],
    "max_tokens": 100
}'

try {
    $response = Invoke-RestMethod -Uri "https://ark.cn-beijing.volces.com/api/v3/chat/completions" -Method POST -Headers $headers -Body $body
    Write-Host "SUCCESS: API call worked!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "FAILED: API call failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
