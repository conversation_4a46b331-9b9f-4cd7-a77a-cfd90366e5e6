<template>
  <div class="shipin-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="el-icon-picture"></i>
          小红书文案库
        </h1>
        <p class="page-subtitle">分段清晰，emoji丰富，种草语气</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="showCreateLibraryDialog">
          创建文案库
        </el-button>
        <el-button 
          type="primary" 
          size="small" 
          icon="el-icon-refresh" 
          @click="refreshData"
          style="margin-right: 12px;"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 文案库管理区域 -->
    <div class="main-content">
      <!-- 左侧：文案库列表 -->
      <div class="library-section">
        <div class="section-header">
          <h3>
            <i class="el-icon-folder-opened"></i>
            文案库列表
          </h3>
          <div class="section-filters">
            <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px;">
              <el-option label="全部" value=""></el-option>
              <el-option label="未开始" value="pending"></el-option>
              <el-option label="生成中" value="generating"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="生成失败" value="failed"></el-option>
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文案库..."
              size="small"
              clearable
              style="width: 200px; margin-left: 12px;"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
        </div>

        <div class="library-list">
          <div 
            v-for="library in filteredLibraryList" 
            :key="library.id"
            class="library-card"
            :class="{ active: currentLibrary && currentLibrary.id === library.id }"
            @click="selectLibrary(library)"
          >
            <div class="library-header">
              <div class="library-title">
                <i class="el-icon-picture"></i>
                {{ library.name || library.libraryName }}
              </div>
              <div class="library-status">
                <el-tag 
                  :type="getStatusType(library.status)" 
                  size="mini"
                >
                  {{ getStatusText(library.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="library-info">
              <div class="library-meta">
                <span class="meta-item">
                  <i class="el-icon-document"></i>
                  {{ library.generatedCount || 0 }}/{{ library.targetCount || 0 }}条
                </span>
                <span class="meta-item">
                  <i class="el-icon-time"></i>
                  {{ library.createTime }}
                </span>
              </div>
              
              <div class="library-description">
                {{ library.shopDetails || '暂无描述' }}
              </div>
            </div>

            <!-- 生成进度 -->
            <div class="progress-info" v-if="library.status === 'generating'">
              <el-progress
                :percentage="Math.round((library.generatedCount / library.targetCount) * 100)"
                status="success"
              ></el-progress>
              <div class="progress-text">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>
            </div>

            <div class="library-actions">
              <el-button type="text" size="mini" @click.stop="viewLibraryContents(library)">
                <i class="el-icon-view"></i>
                查看文案
              </el-button>
              <el-button type="text" size="mini" @click.stop="editLibrary(library)">
                <i class="el-icon-edit"></i>
                编辑
              </el-button>
              <el-button type="text" size="mini" @click.stop="deleteLibrary(library)" class="danger">
                <i class="el-icon-delete"></i>
                删除
              </el-button>
            </div>
          </div>

          <div v-if="filteredLibraryList.length === 0" class="empty-state">
            <i class="el-icon-folder-opened"></i>
            <p>暂无文案库</p>
            <el-button type="primary" @click="showCreateLibraryDialog">创建第一个文案库</el-button>
          </div>
        </div>
      </div>

      <!-- 右侧：文案内容 -->
      <div class="content-section">
        <div v-if="!currentLibrary" class="content-placeholder">
          <i class="el-icon-document"></i>
          <p>请选择一个文案库查看内容</p>
        </div>

        <div v-else class="content-detail">
          <div class="content-header">
            <h3>
              <i class="el-icon-picture"></i>
              {{ currentLibrary.name || currentLibrary.libraryName }}
            </h3>
            <div class="content-actions">
              <el-button type="primary" size="small" icon="el-icon-plus" @click="showAddContentDialog">
                新增文案
              </el-button>
              <el-button size="small" icon="el-icon-refresh" @click="loadLibraryContents(currentLibrary.id)">
                刷新
              </el-button>
            </div>
          </div>

          <div class="content-list">
            <div 
              v-for="content in libraryContents" 
              :key="content.id"
              class="content-item xiaohongshu-content"
            >
              <div class="content-header-item">
                <div class="content-title">
                  <i class="el-icon-document"></i>
                  {{ content.title }}
                </div>
                <div class="content-meta">
                  <el-tag v-if="content.isAiGenerated" type="success" size="mini">AI生成</el-tag>
                  <span class="word-count">{{ content.wordCount }}字</span>
                  <span class="quality-score" v-if="content.qualityScore">
                    质量: {{ content.qualityScore }}分
                  </span>
                </div>
              </div>
              
              <div class="content-body xiaohongshu-style">
                <div v-html="formatXiaohongshuContent(content.content)"></div>
              </div>
              
              <div class="content-footer">
                <div class="content-time">
                  <i class="el-icon-time"></i>
                  {{ content.createTime }}
                </div>
                <div class="content-actions">
                  <el-button type="text" size="mini" @click="copyContent(content.content)">
                    <i class="el-icon-document-copy"></i>
                    复制
                  </el-button>
                  <el-button type="text" size="mini" @click="editContent(content)">
                    <i class="el-icon-edit"></i>
                    编辑
                  </el-button>
                  <el-button type="text" size="mini" @click="deleteContent(content)" class="danger">
                    <i class="el-icon-delete"></i>
                    删除
                  </el-button>
                </div>
              </div>
            </div>

            <div v-if="libraryContents.length === 0" class="empty-content">
              <i class="el-icon-document"></i>
              <p>暂无文案内容</p>
              <el-button type="primary" @click="showAddContentDialog">添加第一条文案</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建文案库对话框 -->
    <el-dialog
      title="创建小红书文案库"
      :visible.sync="createLibraryDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="createLibraryForm" :rules="createLibraryRules" ref="createLibraryForm" label-width="120px">
        <el-form-item label="文案库名称" prop="name">
          <el-input
            v-model="createLibraryForm.name"
            placeholder="请输入文案库名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="是否使用AI" prop="useAI">
          <el-switch
            v-model="createLibraryForm.useAI"
            active-text="AI生成"
            inactive-text="手动创建"
          ></el-switch>
          <div class="form-tip">开启后将使用AI生成小红书风格的种草文案</div>
        </el-form-item>

        <template v-if="createLibraryForm.useAI">
          <el-form-item label="店铺详情" prop="shopDetails">
            <el-input
              type="textarea"
              v-model="createLibraryForm.shopDetails"
              placeholder="请详细描述您的店铺信息，如：经营内容、特色、位置等"
              :rows="3"
              maxlength="500"
              show-word-limit
            ></el-input>
            <div class="form-tip">详细的店铺信息有助于AI生成更精准的文案</div>
          </el-form-item>

          <el-form-item label="AI提示词" prop="prompt">
            <el-input
              type="textarea"
              v-model="createLibraryForm.prompt"
              placeholder="请输入AI生成提示词，如：生成小红书种草文案，要分段清晰，emoji丰富"
              :rows="3"
              maxlength="300"
              show-word-limit
            ></el-input>
            <div class="form-tip">
              示例：生成小红书种草文案，要分段清晰，emoji丰富，语气亲切，姐妹分享感
              <el-button type="text" @click="showPromptHelp">查看提示词建议</el-button>
            </div>
          </el-form-item>

          <el-form-item label="生成条数" prop="count">
            <el-input-number
              v-model="createLibraryForm.count"
              :min="1"
              :max="50"
              placeholder="请输入生成条数"
              style="width: 100%"
            ></el-input-number>
            <div class="form-tip">最多可生成50条文案</div>
          </el-form-item>

          <el-form-item label="大约字数" prop="wordCount">
            <el-select v-model="createLibraryForm.wordCount" placeholder="请选择文案字数">
              <el-option label="100字左右" value="100"></el-option>
              <el-option label="150字左右" value="150"></el-option>
              <el-option label="200字左右" value="200"></el-option>
              <el-option label="300字左右" value="300"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer">
        <el-button @click="createLibraryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createLibrary" :loading="creating">
          {{ creating ? '创建中...' : '创建文案库' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLibrary, addLibrary, updateLibrary, delLibrary } from '@/api/ai/copywriting'
import { listContent, addContent, updateContent, delContent } from '@/api/ai/copywriting'

export default {
  name: 'XiaohongshuCopywriting',
  data() {
    return {
      // 文案库列表
      libraryList: [],
      filteredLibraryList: [],
      currentLibrary: null,

      // 文案内容
      libraryContents: [],

      // 搜索和筛选
      searchKeyword: '',
      filterStatus: '',

      // 对话框控制
      createLibraryDialogVisible: false,
      creating: false,

      // 创建文案库表单
      createLibraryForm: {
        name: '',
        useAI: true,
        shopDetails: '',
        prompt: '生成小红书种草文案，要分段清晰，emoji丰富，语气亲切，姐妹分享感，营销感为0',
        count: 10,
        wordCount: '150' // 小红书默认150字
      },

      // 表单验证规则
      createLibraryRules: {
        name: [
          { required: true, message: '请输入文案库名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        shopDetails: [
          { required: true, message: '请输入店铺详情', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        prompt: [
          { required: true, message: '请输入AI提示词', trigger: 'blur' },
          { min: 10, max: 300, message: '长度在 10 到 300 个字符', trigger: 'blur' }
        ],
        count: [
          { required: true, message: '请输入生成条数', trigger: 'blur' },
          { type: 'number', min: 1, max: 50, message: '生成条数在 1 到 50 之间', trigger: 'blur' }
        ]
      },

      // 持久化存储
      libraryContentStorage: {}
    }
  },

  computed: {
    // 过滤后的文案库列表
    filteredLibraryList() {
      let list = this.libraryList

      // 状态筛选
      if (this.filterStatus) {
        list = list.filter(library => library.status === this.filterStatus)
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(library =>
          (library.name && library.name.toLowerCase().includes(keyword)) ||
          (library.libraryName && library.libraryName.toLowerCase().includes(keyword)) ||
          (library.shopDetails && library.shopDetails.toLowerCase().includes(keyword))
        )
      }

      return list
    }
  },

  created() {
    // 初始化持久化存储
    this.loadLibraryContentFromStorage()

    this.loadLibraryList()

    // 备用方案：如果3秒后还没有数据，直接加载模拟数据
    setTimeout(() => {
      if (this.libraryList.length === 0) {
        console.log('3秒后仍无数据，强制加载模拟数据')
        this.loadMockLibraryList()
      }
    }, 3000)
  },

  methods: {
    // 生成小红书种草文案
    generateXiaohongshuContent(library, index) {
      const targetWordCount = library.wordCount || 150
      const emojis = ['✨', '💕', '🌟', '💖', '🎀', '🌸', '💫', '🦋', '🌺', '💐', '🎨', '🌈', '💎', '🎪', '🎭', '🌙', '⭐', '🎊']

      const xiaohongshuStarters = [
        '姐妹们！今天要分享一个宝藏',
        '真的不是我吹',
        '这个真的太好了',
        '终于找到了',
        '姐妹们看过来',
        '集美们注意了',
        '今天发现了个好东西'
      ]

      let content = `${xiaohongshuStarters[index % xiaohongshuStarters.length]}${emojis[Math.floor(Math.random() * emojis.length)]}\n\n`

      // 添加店铺介绍段落
      content += `${library.shopDetails || '这个地方'}真的让我惊喜${emojis[Math.floor(Math.random() * emojis.length)]}\n\n`

      // 添加分段内容
      const segments = [
        `环境超级棒${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `服务态度也很好${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `性价比真的很高${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `强烈推荐给大家${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `真的值得一试${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `下次还会再来${emojis[Math.floor(Math.random() * emojis.length)]}`
      ]

      // 根据目标字数添加段落
      let currentLength = content.length
      let segmentIndex = 0

      while (currentLength < targetWordCount - 30 && segmentIndex < segments.length) {
        content += `${segments[segmentIndex]}\n`
        currentLength = content.length
        segmentIndex++
      }

      // 添加结尾
      content += `\n${library.prompt || '真的值得一试'}${emojis[Math.floor(Math.random() * emojis.length)]}\n`
      content += `有同款的姐妹吗？评论区见${emojis[Math.floor(Math.random() * emojis.length)]}`

      return this.createContentObject(library, index, content, '小红书文案')
    },

    // 格式化小红书文案显示
    formatXiaohongshuContent(content) {
      // 将换行符转换为HTML换行
      return content.replace(/\n/g, '<br>')
    },

    // 显示提示词帮助
    showPromptHelp() {
      this.$alert(`
        <h4>小红书文案提示词建议：</h4>
        <p><strong>核心要求：</strong>分段清晰，emoji丰富，种草语气，姐妹分享感</p>
        <br>
        <h5>📝 推荐提示词模板：</h5>
        <p><strong>1. 美妆护肤：</strong>生成小红书美妆种草文案，分段清晰，emoji丰富，突出产品效果和使用感受，姐妹分享的语气</p>
        <p><strong>2. 时尚穿搭：</strong>生成小红书穿搭分享文案，要有时尚感，分段描述搭配技巧，emoji点缀，种草语气</p>
        <p><strong>3. 美食探店：</strong>生成小红书美食探店文案，分段介绍环境、味道、服务，emoji丰富，真实体验感</p>
        <p><strong>4. 生活好物：</strong>生成小红书好物推荐文案，分段介绍功能和使用体验，emoji点缀，实用性强</p>
        <p><strong>5. 旅行攻略：</strong>生成小红书旅行分享文案，分段描述景点特色和游玩体验，emoji丰富，攻略性强</p>
        <br>
        <h5>✍️ 编写技巧：</h5>
        <p>• <strong>分段清晰：</strong>用换行分段，每段一个重点</p>
        <p>• <strong>emoji丰富：</strong>适当使用表情符号增加活泼感</p>
        <p>• <strong>姐妹语气：</strong>用"姐妹们"、"集美们"等称呼</p>
        <p>• <strong>种草感：</strong>突出推荐和分享的真实感受</p>
      `, '小红书文案提示词指南', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '知道了',
        customClass: 'prompt-help-dialog'
      })
    },

    // 其他方法与抖音页面类似，但使用小红书特色的生成逻辑
    // ... (其他方法代码与dou.vue类似，但调用generateXiaohongshuContent)
  }
}
</script>
