<template>
  <div class="mobile-promotion-page">
    <!-- 移动端推广页面内容 -->
    <div class="mobile-content" :style="pageBackgroundStyle">
          
          <!-- 动态内容区域 -->
          <div v-for="section in sortedSections" :key="section.name" class="section-wrapper">
            
            <!-- Banner区域 -->
            <div v-if="section.name === 'banner' && config.banner.enabled" class="preview-banner" :style="bannerStyle">
              <div class="banner-mask" :style="bannerMaskStyle"></div>
              <div class="banner-content" :style="bannerContentStyle">
                <h1 class="banner-title" :style="bannerTitleStyle">{{ config.banner.title }}</h1>
                <p class="banner-subtitle" :style="bannerSubtitleStyle">{{ config.banner.subtitle }}</p>
              </div>
            </div>

            <!-- 产品信息 -->
            <div v-if="section.name === 'products' && config.products.enabled && config.products.items.length > 0"
                 class="preview-products">
              <div
                v-for="(product, index) in config.products.items"
                :key="'product-' + index"
                v-show="index === currentProductIndex"
                class="product-slide"
                @click="buyProduct(product)"
              >
                <div class="product-card-horizontal">
                  <!-- 左侧图片区域 -->
                  <div class="product-image-section">
                    <div class="product-image-container">
                      <img v-if="product.image" :src="product.image" class="product-image" alt="产品图片">
                      <div v-else class="product-image-placeholder">
                        <i class="el-icon-picture-outline"></i>
                        <span>产品图片</span>
                      </div>
                      <!-- 产品标签 -->
                      <div class="product-tags">
                        <span v-if="product.isHot" class="product-tag hot">🔥爆款</span>
                        <span v-if="product.isBestSeller" class="product-tag bestseller">⭐热卖</span>
                        <span v-if="product.isNew" class="product-tag new">🆕新品</span>
                        <span v-if="product.isLimited" class="product-tag limited">⏰限时</span>
                        <span v-if="product.customTag" class="product-tag custom">{{ product.customTag }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧文字信息区域 -->
                  <div class="product-info-section">
                    <div class="product-content">
                      <div class="product-name">{{ product.name }}</div>
                      <div class="product-description">{{ product.description }}</div>
                      <div class="product-specs">{{ product.specifications }}</div>

                      <div class="product-price-section">
                        <div class="product-price">
                          <span class="current-price">￥{{ product.currentPrice }}</span>
                          <span class="original-price">￥{{ product.originalPrice }}</span>
                        </div>
                        <div v-if="product.discount" class="discount-info">{{ product.discount }}</div>
                      </div>

                      <button class="buy-button">{{ product.buttonText || '立即购买' }}</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 轮播指示器 -->
              <div v-if="config.products.items.length > 1" class="carousel-indicators">
                <span
                  v-for="(product, index) in config.products.items"
                  :key="'indicator-' + index"
                  :class="{ active: index === currentProductIndex }"
                  @click="currentProductIndex = index"
                ></span>
              </div>
            </div>

            <!-- 平台展示区域 -->
            <div v-if="section.name === 'platforms' && config.platforms.enabled" class="preview-platforms">
              <!-- 短视频平台 -->
              <div v-if="config.platforms.videoPlatforms.enabled && getEnabledPlatforms('videoPlatforms').length > 0" class="platform-section">
                <h4>📹 短视频平台</h4>
                <div class="platform-grid" :style="platformGridStyle">
                  <div
                    v-for="(platform, index) in getEnabledPlatforms('videoPlatforms')"
                    :key="'video-platform-' + index"
                    class="platform-item"
                    :style="{ backgroundColor: platform.color }"
                    @click="openPlatformUrl(platform.url)"
                  >
                    <img v-if="platform.icon" :src="platform.icon" class="platform-icon" alt="平台图标">
                    <div class="platform-name">{{ platform.displayName || platform.name }}</div>
                    <div v-if="platform.description" class="platform-description">{{ platform.description }}</div>
                  </div>
                </div>
              </div>

              <!-- 图文/点评平台 -->
              <div v-if="config.platforms.reviewPlatforms.enabled && getEnabledPlatforms('reviewPlatforms').length > 0" class="platform-section">
                <h4>📝 图文/点评平台</h4>
                <div class="platform-grid" :style="platformGridStyle">
                  <div
                    v-for="(platform, index) in getEnabledPlatforms('reviewPlatforms')"
                    :key="'review-platform-' + index"
                    class="platform-item"
                    :style="{ backgroundColor: platform.color }"
                    @click="openPlatformUrl(platform.url)"
                  >
                    <img v-if="platform.icon" :src="platform.icon" class="platform-icon" alt="平台图标">
                    <div class="platform-name">{{ platform.displayName || platform.name }}</div>
                    <div v-if="platform.description" class="platform-description">{{ platform.description }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- WiFi信息 -->
            <div v-if="section.name === 'wifi' && config.wifi.enabled" class="preview-wifi" :style="wifiStyle">
              <div class="wifi-header">
                <i class="el-icon-connection wifi-icon"></i>
                <span class="wifi-title">免费WiFi</span>
              </div>
              <div class="wifi-info">
                <div class="wifi-item">
                  <span class="wifi-label">网络名称：</span>
                  <span class="wifi-value">{{ config.wifi.ssid }}</span>
                </div>
                <div class="wifi-item">
                  <span class="wifi-label">连接密码：</span>
                  <span class="wifi-value">{{ config.wifi.password }}</span>
                </div>
                <div v-if="config.wifi.description" class="wifi-description">{{ config.wifi.description }}</div>
              </div>
              <button class="wifi-button" :style="wifiButtonStyle" @click="connectWifi">
                {{ config.wifi.buttonText }}
              </button>
            </div>

          </div>

        </div>
      </div>
    </div>

    </div>
  </div>
</template>

<script>
import promotionConfigSync from '@/utils/promotionConfigSync'

export default {
  name: 'PromotionPreview',
  data() {
    return {
      storeId: this.$route.params.id || '1',
      currentProductIndex: 0,
      productTimer: null,
      config: {},
      configChangeListener: null
    }
  },

  computed: {



    // 页面背景样式
    pageBackgroundStyle() {
      const bg = this.config.background
      if (!bg) {
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      }

      if (bg.type === 'color') {
        return { backgroundColor: bg.color }
      } else if (bg.type === 'gradient') {
        return {
          background: `linear-gradient(${bg.gradientDirection}, ${bg.gradientStart}, ${bg.gradientEnd})`
        }
      } else if (bg.type === 'image' && bg.image) {
        return {
          backgroundImage: `url(${bg.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }
      }
      return {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }
    },

    // Banner样式
    bannerStyle() {
      const banner = this.config.banner
      if (!banner) return {}

      return {
        backgroundImage: banner.backgroundImage ? `url(${banner.backgroundImage})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        position: 'relative',
        minHeight: '210px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    },

    sortedSections() {
      if (!this.config || !this.config.banner) return []

      const sections = []

      if (this.config.banner && this.config.banner.enabled) {
        sections.push({ name: 'banner', priority: this.config.banner.priority })
      }

      if (this.config.products && this.config.products.enabled) {
        sections.push({ name: 'products', priority: this.config.products.priority })
      }

      if (this.config.platforms && this.config.platforms.enabled) {
        sections.push({ name: 'platforms', priority: this.config.platforms.priority })
      }

      if (this.config.wifi && this.config.wifi.enabled) {
        sections.push({ name: 'wifi', priority: this.config.wifi.priority })
      }

      return sections.sort((a, b) => a.priority - b.priority)
    },

    // Banner遮罩样式
    bannerMaskStyle() {
      const banner = this.config.banner
      if (!banner) return {}

      return {
        backgroundColor: `rgba(0,0,0,${banner.maskOpacity / 100})`
      }
    },

    bannerContentStyle() {
      const banner = this.config.banner
      if (!banner) return {}

      return {
        textAlign: banner.textAlign
      }
    },

    bannerTitleStyle() {
      const banner = this.config.banner
      if (!banner) return {}

      return {
        color: banner.titleColor,
        fontSize: `${banner.titleFontSize}px`,
        fontWeight: banner.titleFontWeight
      }
    },

    bannerSubtitleStyle() {
      const banner = this.config.banner
      if (!banner) return {}

      return {
        color: banner.subtitleColor,
        fontSize: `${banner.subtitleFontSize}px`,
        fontWeight: banner.subtitleFontWeight
      }
    },

    // 平台网格样式
    platformGridStyle() {
      const platforms = this.config.platforms
      if (!platforms) return {}

      return {
        gridTemplateColumns: `repeat(${platforms.itemsPerRow}, 1fr)`
      }
    },

    // WiFi样式
    wifiStyle() {
      const wifi = this.config.wifi
      if (!wifi) return {}

      return {
        backgroundColor: wifi.backgroundColor,
        color: wifi.textColor,
        borderRadius: `${wifi.borderRadius}px`,
        fontSize: `${wifi.fontSize}px`,
        margin: '16px',
        padding: '16px'
      }
    },

    wifiButtonStyle() {
      const wifi = this.config.wifi
      if (!wifi) return {}

      return {
        backgroundColor: wifi.buttonColor,
        borderColor: wifi.buttonColor
      }
    }
  },

  mounted() {
    this.loadConfig()
    this.startProductCarousel()

    // 设置配置变化监听
    this.setupConfigListener()
  },

  beforeDestroy() {
    this.stopProductCarousel()
    this.removeConfigListener()
  },

  methods: {
    // 加载配置
    loadConfig() {
      this.config = promotionConfigSync.loadConfig(this.storeId)
      console.log('预览页面配置加载完成:', this.config)
    },



    // 获取启用的平台
    getEnabledPlatforms(type) {
      return this.config.platforms[type].items.filter(platform => platform.enabled)
    },

    // 打开平台链接
    openPlatformUrl(url) {
      if (url) {
        window.open(url, '_blank')
      }
    },

    // 产品轮播
    startProductCarousel() {
      this.stopProductCarousel()
      if (this.config.products.items.length > 1) {
        this.productTimer = setInterval(() => {
          this.currentProductIndex = (this.currentProductIndex + 1) % this.config.products.items.length
        }, 3000)
      }
    },

    stopProductCarousel() {
      if (this.productTimer) {
        clearInterval(this.productTimer)
        this.productTimer = null
      }
    },

    // 购买产品
    buyProduct(product) {
      if (product.buyUrl) {
        window.open(product.buyUrl, '_blank')
      } else {
        this.$message.info('请先设置团购链接')
      }
    },

    // 连接WiFi
    connectWifi() {
      this.$message.success('WiFi连接功能需要在实际设备上使用')
    },

    // 设置配置变化监听
    setupConfigListener() {
      this.configChangeListener = (newConfig) => {
        this.config = newConfig
        console.log('预览页面配置已同步更新:', newConfig)

        // 重新启动产品轮播
        this.startProductCarousel()
      }

      promotionConfigSync.onConfigChange(this.storeId, this.configChangeListener)
    },

    // 移除配置变化监听
    removeConfigListener() {
      if (this.configChangeListener) {
        promotionConfigSync.offConfigChange(this.storeId, this.configChangeListener)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-promotion-page {
  min-height: 100vh;
  background: #f5f5f5;

  // 移动端适配
  @media (max-width: 768px) {
    padding: 0;
  }
}

// 手机预览框架
.phone-frame {
  position: relative;
  width: 375px;
  height: 812px;
  background: linear-gradient(145deg, #2c3e50, #34495e);
  border-radius: 40px;
  padding: 8px;
  box-shadow:
    0 20px 60px rgba(0,0,0,0.3),
    inset 0 2px 6px rgba(255,255,255,0.1);

  .volume-buttons {
    position: absolute;
    left: -3px;
    top: 120px;
    width: 3px;
    height: 80px;
    background: linear-gradient(145deg, #34495e, #2c3e50);
    border-radius: 2px 0 0 2px;

    &::before {
      content: '';
      position: absolute;
      top: 100px;
      left: 0;
      width: 3px;
      height: 40px;
      background: linear-gradient(145deg, #34495e, #2c3e50);
      border-radius: 2px 0 0 2px;
    }
  }

  .phone-screen {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 32px;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: 140px;
      height: 6px;
      background: #333;
      border-radius: 3px;
      z-index: 10;
    }
  }

  .preview-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-top: 40px;

    &::-webkit-scrollbar {
      width: 2px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255,255,255,0.1);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255,255,255,0.3);
      border-radius: 1px;
    }
  }
}

// NFC和二维码信息
.nfc-info {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .info-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    text-align: center;

    h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 18px;
    }

    .url-display {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 6px;
      font-family: monospace;
      font-size: 12px;
      word-break: break-all;
      margin-bottom: 15px;
      color: #666;
    }

    .qrcode-container {
      margin-bottom: 15px;

      .qrcode {
        border: 1px solid #eee;
        border-radius: 8px;
      }
    }
  }
}

// 预览内容样式（复用DIY页面的样式）
.preview-banner {
  position: relative;
  height: 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 16px;

  .banner-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
  }

  .banner-content {
    position: relative;
    z-index: 2;
    padding: 20px;

    .banner-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 700;
    }

    .banner-subtitle {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }
}

.preview-products {
  padding: 16px;

  .product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    display: flex;
    height: 210px;

    .product-image-section {
      flex: 0 0 65%;
      position: relative;

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .product-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 8px;
        }
      }

      .product-tags {
        position: absolute;
        top: 12px;
        left: 12px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .product-tag {
          padding: 2px 6px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 600;
          color: white;

          &.hot { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
          &.bestseller { background: linear-gradient(135deg, #feca57, #ff9ff3); }
          &.new { background: linear-gradient(135deg, #48dbfb, #0abde3); }
          &.limited { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
          &.custom { background: linear-gradient(135deg, #667eea, #764ba2); }
        }
      }

      .product-indicators {
        position: absolute;
        bottom: 12px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 6px;

        .indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: rgba(255,255,255,0.5);
          cursor: pointer;
          transition: all 0.3s ease;

          &.active {
            background: white;
            transform: scale(1.2);
          }
        }
      }
    }

    .product-info-section {
      flex: 1;
      padding: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .product-content {
        flex: 1;

        .product-name {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 6px;
          color: #333;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .product-description {
          font-size: 11px;
          margin-bottom: 6px;
          opacity: 0.8;
          color: #666;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .product-specs {
          font-size: 9px;
          margin-bottom: 8px;
          opacity: 0.7;
          color: #999;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }

        .product-price-section {
          margin-bottom: 8px;

          .product-price {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 3px;

            .current-price {
              font-size: 16px;
              font-weight: 700;
              color: #ff4757;
            }

            .original-price {
              font-size: 11px;
              text-decoration: line-through;
              opacity: 0.6;
              color: #999;
            }
          }

          .discount-info {
            font-size: 9px;
            background: #ff4757;
            color: white;
            padding: 1px 4px;
            border-radius: 6px;
            font-weight: 600;
            display: inline-block;
          }
        }
      }

      .buy-button {
        width: 100%;
        padding: 8px 12px;
        border: none;
        border-radius: 16px;
        background: #ff4757;
        color: white;
        font-weight: 600;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
          background: #ff3742;
        }
      }
    }
  }
}

.preview-platforms {
  padding: 16px;

  .platform-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    .platform-grid {
      display: grid;
      gap: 12px;

      .platform-item {
        background: white;
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .platform-icon {
          width: 32px;
          height: 32px;
          margin-bottom: 8px;
          border-radius: 6px;
        }

        .platform-name {
          font-size: 12px;
          font-weight: 600;
          color: white;
          margin-bottom: 4px;
        }

        .platform-description {
          font-size: 10px;
          color: rgba(255,255,255,0.9);
          line-height: 1.2;
        }
      }
    }
  }
}

// 产品展示样式
.preview-products {
  margin: 16px;
  position: relative;

  .product-slide {
    cursor: pointer;
  }

  // 水平布局产品卡片
  .product-card-horizontal {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: flex;
    min-height: 120px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    .product-image-section {
      flex: 0 0 120px;
      position: relative;

      .product-image-container {
        width: 100%;
        height: 100%;
        position: relative;

        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .product-image-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
          color: #999;
          font-size: 12px;

          i {
            font-size: 24px;
            margin-bottom: 4px;
          }
        }
      }

      .product-tags {
        position: absolute;
        top: 8px;
        left: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .product-tag {
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 10px;
          color: white;
          font-weight: 600;
          white-space: nowrap;

          &.hot { background: linear-gradient(45deg, #ff6b6b, #ff8e8e); }
          &.bestseller { background: linear-gradient(45deg, #ffd93d, #ffed4e); color: #333; }
          &.new { background: linear-gradient(45deg, #6bcf7f, #8ed99f); }
          &.limited { background: linear-gradient(45deg, #ff9f43, #ffb74d); }
          &.custom { background: linear-gradient(45deg, #a55eea, #c44569); }
        }
      }
    }

    .product-info-section {
      flex: 1;
      padding: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .product-content {
        flex: 1;

        .product-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 6px;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-description {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-specs {
          font-size: 11px;
          color: #999;
          margin-bottom: 8px;
        }

        .product-price-section {
          margin-bottom: 8px;

          .product-price {
            display: flex;
            align-items: baseline;
            gap: 6px;
            margin-bottom: 4px;

            .current-price {
              font-size: 18px;
              font-weight: 700;
              color: #e74c3c;
            }

            .original-price {
              font-size: 12px;
              color: #999;
              text-decoration: line-through;
            }
          }

          .discount-info {
            font-size: 11px;
            color: #e74c3c;
            font-weight: 600;
          }
        }
      }

      .buy-button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        align-self: flex-start;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  // 轮播指示器
  .carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 6px;
    margin-top: 12px;

    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(0,0,0,0.3);
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #667eea;
        transform: scale(1.2);
      }
    }
  }
}

.preview-wifi {
  margin: 16px;
  padding: 16px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);

  .wifi-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;

    .wifi-icon {
      font-size: 20px;
      color: #409EFF;
    }

    .wifi-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .wifi-info {
    margin-bottom: 16px;

    .wifi-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;

      .wifi-label {
        opacity: 0.8;
      }

      .wifi-value {
        font-weight: 600;
      }
    }

    .wifi-description {
      margin-top: 12px;
      font-size: 12px;
      opacity: 0.8;
      font-style: italic;
    }
  }

  .wifi-button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
  }
}

// 移动端内容容器样式
.mobile-content {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;

  // 桌面端居中显示，模拟手机宽度
  @media (min-width: 769px) {
    max-width: 375px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
  }

  // 移动端全屏显示
  @media (max-width: 768px) {
    width: 100vw;
    min-height: 100vh;
  }
}

// 移动端优化
@media (max-width: 768px) {
  .mobile-promotion-page {
    padding: 0;
    background: #f5f5f5;
  }

  .preview-banner {
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .preview-products {
    margin: 16px 8px !important;
  }

  .preview-platforms {
    margin: 16px 8px !important;
  }

  .preview-wifi {
    margin: 16px 8px !important;
  }
}
</style>
