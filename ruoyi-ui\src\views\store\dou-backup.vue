<template>
  <div class="shipin-container">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-magic-stick"></i>
          AI文案生成库
        </h1>
        <p class="page-description">基于火山引擎Doubao，智能生成高质量文案内容</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="showCreateLibraryDialog">创建文案库</el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">刷新</el-button>
      </div>
    </div>

    <!-- AI提示词推荐 -->
    <div class="prompt-section">
      <h3>
        <i class="el-icon-lightbulb"></i>
        AI提示词推荐
      </h3>
      <div class="prompt-grid">
        <div class="prompt-card" @click="usePrompt(prompt)" v-for="prompt in recommendPrompts" :key="prompt.id">
          <div class="prompt-icon">{{ prompt.icon }}</div>
          <div class="prompt-title">{{ prompt.title }}</div>
          <div class="prompt-desc">{{ prompt.desc }}</div>
          <div class="prompt-preview">{{ prompt.content.substring(0, 50) }}...</div>
        </div>
      </div>
    </div>

    <!-- 文案库列表 -->
    <div class="library-section">
      <div class="section-header">
        <h3>
          <i class="el-icon-folder-opened"></i>
          我的文案库
        </h3>
        <div class="section-filters">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-refresh"
            @click="refreshData"
            style="margin-right: 12px;"
          >
            刷新数据
          </el-button>
          <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="未开始" value="pending"></el-option>
            <el-option label="生成中" value="generating"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="生成失败" value="failed"></el-option>
          </el-select>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文案库..."
            size="small"
            clearable
            style="width: 200px; margin-left: 12px;"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
      </div>

      <div class="library-list">
        <div v-for="library in filteredLibraryList" :key="library.id" class="library-item">
          <div class="item-header">
            <div class="item-title">
              <i class="el-icon-folder"></i>
              {{ library.name }}
            </div>
            <div class="item-meta">
              <el-tag size="mini" :type="getStatusColor(library.status)">{{ getStatusName(library.status) }}</el-tag>
              <el-tag size="mini" type="info" v-if="library.useAI">AI生成</el-tag>
              <span class="item-time">{{ library.createTime }}</span>
            </div>
          </div>

          <div class="item-content">
            <div class="library-info">
              <div class="info-item">
                <span class="label">目标条数：</span>
                <span class="value">{{ library.targetCount }}条</span>
              </div>
              <div class="info-item">
                <span class="label">已生成：</span>
                <span class="value">{{ library.generatedCount }}条</span>
              </div>
              <div class="info-item">
                <span class="label">字数要求：</span>
                <span class="value">{{ library.wordCount }}字</span>
              </div>
            </div>

            <!-- 生成进度 -->
            <div class="progress-info" v-if="library.status === 'generating'">
              <el-progress
                :percentage="Math.round((library.generatedCount / library.targetCount) * 100)"
                status="success"
              ></el-progress>
              <div class="progress-text">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>
            </div>

            <!-- 店铺详情预览 -->
            <div class="shop-info" v-if="library.shopDetails">
              <span class="label">店铺详情：</span>
              <span class="preview">{{ library.shopDetails.substring(0, 50) }}...</span>
            </div>
          </div>

          <div class="item-actions">
            <el-button size="mini" type="primary" icon="el-icon-view" @click="viewLibrary(library)" :disabled="library.status === 'pending'">
              查看文案 ({{ library.generatedCount }})
            </el-button>
            <el-button size="mini" type="success" icon="el-icon-plus" @click="addToLibrary(library)" v-if="library.status === 'completed'">
              新增文案
            </el-button>
            <el-button size="mini" type="warning" icon="el-icon-refresh" @click="regenerateLibrary(library)" v-if="library.status === 'failed'">
              重新生成
            </el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteLibrary(library)">
              删除
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredLibraryList.length === 0" class="empty-state">
          <i class="el-icon-folder-add"></i>
          <h3>暂无文案库</h3>
          <p>点击"创建文案库"按钮创建您的第一个AI文案库</p>
          <el-button type="primary" icon="el-icon-plus" @click="showCreateLibraryDialog">创建文案库</el-button>
        </div>
      </div>
    </div>

    <!-- 创建文案库对话框 -->
    <el-dialog
      title="创建AI文案库"
      :visible.sync="createLibraryDialogVisible"
      :width="isMobile ? '95%' : '600px'"
      :fullscreen="isMobile"
      class="create-dialog"
    >
      <el-form :model="createLibraryForm" :rules="createLibraryRules" ref="createLibraryForm" label-width="120px">
        <el-form-item label="文案库名称" prop="name">
          <el-input
            v-model="createLibraryForm.name"
            placeholder="请输入文案库名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>



        <el-form-item label="是否使用AI" prop="useAI">
          <el-switch
            v-model="createLibraryForm.useAI"
            active-text="AI生成"
            inactive-text="手动创建"
          ></el-switch>
          <div class="form-tip">开启后将使用火山引擎Doubao生成文案</div>
        </el-form-item>

        <template v-if="createLibraryForm.useAI">
          <el-form-item label="店铺详情" prop="shopDetails">
            <el-input
              v-model="createLibraryForm.shopDetails"
              placeholder="请详细描述您的店铺信息、产品特色、目标客户等"
              type="textarea"
              :rows="4"
              maxlength="500"
              show-word-limit
            ></el-input>
            <div class="form-tip">详细的店铺信息有助于AI生成更精准的文案</div>
          </el-form-item>

          <el-form-item label="AI提示词" prop="prompt">
            <el-input
              v-model="createLibraryForm.prompt"
              placeholder="请输入AI生成文案的提示词"
              type="textarea"
              :rows="3"
              maxlength="300"
              show-word-limit
            ></el-input>
            <div class="form-tip">
              示例：生成吸引人的美食推广文案，要求语言生动、有食欲感
              <el-button type="text" @click="showPromptHelp">查看提示词建议</el-button>
            </div>
          </el-form-item>

          <el-form-item label="生成条数" prop="count">
            <el-input-number
              v-model="createLibraryForm.count"
              :min="1"
              :max="50"
              placeholder="请输入生成条数"
              style="width: 100%"
            ></el-input-number>
            <div class="form-tip">最多可生成50条文案</div>
          </el-form-item>

          <el-form-item label="大约字数" prop="wordCount">
            <el-select v-model="createLibraryForm.wordCount" placeholder="请选择文案字数">
              <el-option label="50字以内" value="50"></el-option>
              <el-option label="100字左右" value="100"></el-option>
              <el-option label="200字左右" value="200"></el-option>
              <el-option label="300字左右" value="300"></el-option>
              <el-option label="500字左右" value="500"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer">
        <el-button @click="createLibraryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createLibrary" :loading="creating">
          {{ creating ? '创建中...' : '创建文案库' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增文案对话框 -->
    <el-dialog
      title="新增文案"
      :visible.sync="addCopywritingDialogVisible"
      :width="isMobile ? '95%' : '600px'"
      :fullscreen="isMobile"
      class="add-dialog"
    >
      <el-form :model="addCopywritingForm" :rules="addCopywritingRules" ref="addCopywritingForm" label-width="120px">
        <el-form-item label="文案库">
          <el-input :value="currentLibrary ? currentLibrary.name : ''" disabled></el-input>
        </el-form-item>

        <el-form-item label="是否使用AI" prop="useAI">
          <el-switch
            v-model="addCopywritingForm.useAI"
            active-text="AI生成"
            inactive-text="手动输入"
          ></el-switch>
        </el-form-item>

        <template v-if="addCopywritingForm.useAI">
          <el-form-item label="店铺详情" prop="shopDetails">
            <el-input
              v-model="addCopywritingForm.shopDetails"
              type="textarea"
              :rows="3"
              placeholder="店铺详情（默认使用上次的内容）"
            ></el-input>
          </el-form-item>

          <el-form-item label="AI提示词" prop="prompt">
            <el-input
              v-model="addCopywritingForm.prompt"
              type="textarea"
              :rows="2"
              placeholder="AI提示词（默认使用上次的内容）"
            ></el-input>
          </el-form-item>

          <el-form-item label="生成条数" prop="count">
            <el-input-number
              v-model="addCopywritingForm.count"
              :min="1"
              :max="20"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </template>

        <template v-else>
          <el-form-item label="文案内容" prop="content">
            <el-input
              v-model="addCopywritingForm.content"
              type="textarea"
              :rows="6"
              placeholder="请输入文案内容"
              maxlength="1000"
              show-word-limit
            ></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer">
        <el-button @click="addCopywritingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addCopywriting" :loading="adding">
          {{ adding ? (addCopywritingForm.useAI ? '生成中...' : '添加中...') : (addCopywritingForm.useAI ? '生成文案' : '添加文案') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看文案库对话框 -->
    <el-dialog
      title="文案库详情"
      :visible.sync="viewLibraryDialogVisible"
      :width="isMobile ? '95%' : '900px'"
      :fullscreen="isMobile"
      class="view-dialog"
    >
      <div v-if="currentLibrary" class="library-detail">
        <div class="detail-header">
          <h3>{{ currentLibrary.name }}</h3>
          <div class="detail-meta">
            <el-tag :type="getStatusColor(currentLibrary.status)">{{ getStatusName(currentLibrary.status) }}</el-tag>
            <el-tag type="info" v-if="currentLibrary.useAI">AI生成</el-tag>
            <span>创建时间：{{ currentLibrary.createTime }}</span>
          </div>
        </div>

        <div class="detail-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">目标条数：</span>
              <span class="value">{{ currentLibrary.targetCount }}条</span>
            </div>
            <div class="info-item">
              <span class="label">已生成：</span>
              <span class="value">{{ currentLibrary.generatedCount }}条</span>
            </div>
            <div class="info-item">
              <span class="label">字数要求：</span>
              <span class="value">{{ currentLibrary.wordCount }}字</span>
            </div>
          </div>

          <div class="shop-details" v-if="currentLibrary.shopDetails">
            <h4>店铺详情：</h4>
            <div class="details-text">{{ currentLibrary.shopDetails }}</div>
          </div>

          <div class="prompt-info" v-if="currentLibrary.prompt">
            <h4>AI提示词：</h4>
            <div class="prompt-text">{{ currentLibrary.prompt }}</div>
          </div>
        </div>

        <div class="copywriting-list">
          <div class="list-header">
            <h4>文案列表 ({{ libraryContents.length }})</h4>
            <div class="list-actions">
              <el-button size="small" type="primary" icon="el-icon-plus" @click="addToLibrary(currentLibrary)">新增文案</el-button>
              <el-button size="small" icon="el-icon-refresh" @click="loadLibraryContents(currentLibrary.id)">刷新</el-button>
            </div>
          </div>

          <div class="content-list">
            <div v-for="(content, index) in libraryContents" :key="content.id" class="content-item">
              <div class="content-header">
                <span class="content-index">{{ index + 1 }}</span>
                <span class="content-time">{{ content.createTime }}</span>
                <div class="content-actions">
                  <el-button size="mini" type="primary" icon="el-icon-view" @click="viewContent(content)">查看</el-button>
                  <el-button size="mini" type="success" icon="el-icon-document-copy" @click="copyContent(content)">复制</el-button>
                  <el-button size="mini" type="warning" icon="el-icon-edit" @click="editContent(content)">编辑</el-button>
                  <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteContent(content)">删除</el-button>
                </div>
              </div>
              <div class="content-text">{{ content.content }}</div>
            </div>

            <!-- 空状态 -->
            <div v-if="libraryContents.length === 0" class="empty-content">
              <i class="el-icon-document-add"></i>
              <p>暂无文案内容</p>
              <el-button size="small" type="primary" @click="addToLibrary(currentLibrary)">添加第一条文案</el-button>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="viewLibraryDialogVisible = false">关闭</el-button>
        <el-button type="success" @click="exportLibrary(currentLibrary)">导出文案库</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLibrary,
  addLibrary,
  delLibrary,
  generateCopywriting,
  listContent,
  addContent,
  updateContent,
  delContent,
  getProgress,
  regenerateLibrary,
  validateBaiduConfig,
  getModelInfo
} from '@/api/ai/copywriting'

// 导入测试API作为备用
import {
  listLibraryTest,
  addLibraryTest,
  testDeepSeekDirect,
  healthCheck
} from '@/api/ai/copywriting-test'

export default {
  name: 'StorerShipin',
  data() {
    return {
      // 对话框状态
      createLibraryDialogVisible: false,
      addCopywritingDialogVisible: false,
      viewLibraryDialogVisible: false,

      // 加载状态
      creating: false,
      adding: false,

      // 筛选和搜索
      filterStatus: '',
      searchKeyword: '',

      // 当前数据
      currentLibrary: null,
      libraryContents: [],
      isMobile: false,

      // 创建文案库表单
      createLibraryForm: {
        name: '',
        useAI: true,
        shopDetails: '',
        prompt: '',
        count: 10,
        wordCount: '200' // AI剪辑文案默认200字
      },
      createLibraryRules: {
        name: [
          { required: true, message: '请输入文案库名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        shopDetails: [
          { required: true, message: '请输入店铺详情', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        prompt: [
          { required: true, message: '请输入AI提示词', trigger: 'blur' },
          { min: 5, max: 300, message: '长度在 5 到 300 个字符', trigger: 'blur' }
        ],
        count: [
          { required: true, message: '请输入生成条数', trigger: 'blur' }
        ],
        wordCount: [
          { required: true, message: '请选择文案字数', trigger: 'change' }
        ]
      },

      // 新增文案表单
      addCopywritingForm: {
        useAI: true,
        shopDetails: '',
        prompt: '',
        count: 5,
        content: ''
      },
      addCopywritingRules: {
        shopDetails: [
          { required: true, message: '请输入店铺详情', trigger: 'blur' }
        ],
        prompt: [
          { required: true, message: '请输入AI提示词', trigger: 'blur' }
        ],
        count: [
          { required: true, message: '请输入生成条数', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入文案内容', trigger: 'blur' }
        ]
      },

      // AI提示词推荐
      recommendPrompts: [
        {
          id: 1,
          icon: '🍔',
          title: '美食推广',
          desc: '适合餐饮店铺',
          content: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围，能够激发顾客的购买欲望'
        },
        {
          id: 2,
          icon: '👗',
          title: '服装时尚',
          desc: '适合服装店铺',
          content: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议，展现品牌调性和时尚态度'
        },
        {
          id: 3,
          icon: '💄',
          title: '美妆护肤',
          desc: '适合美妆店铺',
          content: '编写专业的美妆护肤文案，强调产品功效、使用体验、适用肌肤类型，传递美丽自信的理念'
        },
        {
          id: 4,
          icon: '🏠',
          title: '家居生活',
          desc: '适合家居店铺',
          content: '撰写温馨的家居生活文案，展现产品实用性、设计美感、生活品质提升，营造舒适家庭氛围'
        },
        {
          id: 5,
          icon: '📱',
          title: '数码科技',
          desc: '适合数码店铺',
          content: '制作专业的数码产品文案，突出技术参数、功能特点、使用场景，体现科技感和实用价值'
        },
        {
          id: 6,
          icon: '🎓',
          title: '教育培训',
          desc: '适合教育机构',
          content: '创建有说服力的教育培训文案，强调课程价值、师资力量、学习效果，激发学习兴趣和报名意愿'
        }
      ],

      // 文案库列表
      libraryList: [
        {
          id: 1,
          name: '美食探店文案库',
          useAI: true,
          status: 'completed',
          targetCount: 20,
          generatedCount: 20,
          wordCount: '100',
          shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',
          prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围',
          createTime: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          name: '时尚服装推广库',
          useAI: true,
          status: 'generating',
          targetCount: 30,
          generatedCount: 15,
          wordCount: '150',
          shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',
          prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',
          createTime: '2024-01-15 10:15:00'
        },
        {
          id: 3,
          name: '手动创建文案库',
          useAI: false,
          status: 'completed',
          targetCount: 10,
          generatedCount: 8,
          wordCount: '200',
          shopDetails: '',
          prompt: '',
          createTime: '2024-01-14 16:20:00'
        }
      ]
    }
  },
  computed: {
    filteredLibraryList() {
      let list = this.libraryList

      // 状态筛选
      if (this.filterStatus) {
        list = list.filter(item => item.status === this.filterStatus)
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(item =>
          item.name.toLowerCase().includes(keyword) ||
          (item.shopDetails && item.shopDetails.toLowerCase().includes(keyword))
        )
      }

      return list
    }
  },
  created() {
    // 初始化持久化存储
    this.loadLibraryContentFromStorage()

    this.loadLibraryList()

    // 备用方案：如果3秒后还没有数据，直接加载模拟数据
    setTimeout(() => {
      if (this.libraryList.length === 0) {
        console.log('3秒后仍无数据，强制加载模拟数据')
        this.loadMockLibraryList()
      }
    }, 3000)
  },
  mounted() {
    this.checkMobile()
    window.addEventListener('resize', this.checkMobile)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkMobile)
  },
  methods: {
    checkMobile() {
      this.isMobile = window.innerWidth <= 768
    },


    loadLibraryList() {
      listLibrary().then(response => {
        this.libraryList = response.rows || response.data || []
        if (this.libraryList.length === 0) {
          // 如果返回空数据，也加载模拟数据
          this.loadMockLibraryList()
        }
      }).catch(error => {
        console.error('加载文案库列表失败，使用模拟数据', error)

        // 检查是否是登录过期错误
        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {
          this.$message.warning('检测到登录状态过期，正在使用演示模式...')
        }

        // 使用模拟数据作为备用方案
        this.loadMockLibraryList()
      })
    },

    // 加载模拟文案库数据
    loadMockLibraryList() {
      console.log('加载模拟文案库数据')

      const mockLibraries = [
        {
          id: 1,
          libraryId: 1,
          name: '美食探店文案库',
          libraryName: '美食探店文案库',
          useAI: true,
          useAi: true,
          shopDetails: '精选美食餐厅，提供各类特色菜品和优质服务',
          prompt: '生成吸引人的美食探店文案，突出菜品特色和用餐体验',
          targetCount: 20,
          generatedCount: 20,
          wordCount: 150,
          status: 'completed',
          createTime: '2024-01-15 10:30:00',
          createBy: 'admin'
        },
        {
          id: 2,
          libraryId: 2,
          name: '时尚服装推广库',
          libraryName: '时尚服装推广库',
          useAI: true,
          useAi: true,
          shopDetails: '时尚服装品牌，主营潮流服饰和配饰',
          prompt: '生成时尚服装推广文案，强调款式新颖和品质优良',
          targetCount: 15,
          generatedCount: 15,
          wordCount: 120,
          status: 'completed',
          createTime: '2024-01-10 14:20:00',
          createBy: 'admin'
        },
        {
          id: 3,
          libraryId: 3,
          name: '咖啡厅温馨文案库',
          libraryName: '咖啡厅温馨文案库',
          useAI: true,
          useAi: true,
          shopDetails: '温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段',
          prompt: '生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质',
          targetCount: 10,
          generatedCount: 8,
          wordCount: 100,
          status: 'generating',
          createTime: '2024-01-20 09:15:00',
          createBy: 'user'
        }
      ]

      // 添加用户创建的文案库（如果有的话）
      const userLibraries = this.libraryList.filter(lib => lib.createBy === 'demo')

      this.libraryList = [...mockLibraries, ...userLibraries]
      this.$message.success('已加载模拟文案库数据（共' + this.libraryList.length + '个文案库）')
    },
    refreshData() {
      console.log('手动刷新数据')
      this.loadLibraryList()

      // 如果1秒后还没有数据，直接加载模拟数据
      setTimeout(() => {
        if (this.libraryList.length === 0) {
          console.log('刷新后仍无数据，加载模拟数据')
          this.loadMockLibraryList()
        } else {
          this.$message.success('数据已刷新')
        }
      }, 1000)
    },

    // 显示创建文案库对话框
    showCreateLibraryDialog() {
      this.createLibraryDialogVisible = true
      this.createLibraryForm = {
        name: '',
        useAI: true,
        shopDetails: '',
        prompt: '',
        count: 10,
        wordCount: '100'
      }
    },

    // 使用推荐提示词
    usePrompt(prompt) {
      this.createLibraryForm.prompt = prompt.content
      this.createLibraryDialogVisible = true
      this.$message.success(`已应用${prompt.title}提示词`)
    },

    // 显示提示词帮助
    showPromptHelp() {
      this.$alert(`
        <h4>AI剪辑文案提示词建议：</h4>
        <p><strong>核心要求：</strong>适合口播，开头用疑问句吸引观众，语言顺口易读</p>
        <br>
        <h5>📝 推荐提示词模板：</h5>
        <p><strong>1. 美食餐饮：</strong>生成适合口播的美食推广文案，开头用疑问句吸引观众，突出食材新鲜和口感层次，语言生动有食欲感，朋友推荐的语气</p>
        <p><strong>2. 生活服务：</strong>生成温馨的生活服务推广文案，开头用疑问句引起共鸣，强调便民和贴心服务，语言亲切自然，像邻居朋友介绍</p>
        <p><strong>3. 时尚美妆：</strong>生成时尚美妆种草文案，开头用疑问句抓住痛点，突出产品效果和使用体验，语言轻松活泼，姐妹分享的感觉</p>
        <p><strong>4. 教育培训：</strong>生成教育培训推广文案，开头用疑问句引发思考，强调学习效果和成长价值，语言专业但不失亲和力</p>
        <p><strong>5. 健康养生：</strong>生成健康养生科普文案，开头用疑问句引起关注，突出健康理念和实用方法，语言通俗易懂，专业可信</p>
        <p><strong>6. 旅游出行：</strong>生成旅游景点推广文案，开头用疑问句激发向往，描述美景和独特体验，语言富有画面感和感染力</p>
        <p><strong>7. 科技数码：</strong>生成数码产品介绍文案，开头用疑问句抓住需求，突出功能特点和使用便利，语言简洁明了，避免过于技术化</p>
        <p><strong>8. 家居生活：</strong>生成家居用品推广文案，开头用疑问句触及生活痛点，强调实用性和生活品质提升，语言温馨贴近生活</p>
        <br>
        <h5>✍️ 编写技巧：</h5>
        <p>• <strong>疑问开头：</strong>用"你是否想要..."、"你知道吗..."等疑问句开头</p>
        <p>• <strong>顺口易读：</strong>避免拗口词汇，多用短句，适合朗读</p>
        <p>• <strong>朋友语气：</strong>温和亲切，像朋友分享，营销感为0</p>
        <p>• <strong>具体描述：</strong>结合您的店铺特色，越具体越好</p>
      `, 'AI剪辑文案提示词指南', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '知道了',
        customClass: 'prompt-help-dialog'
      })
    },
    // 创建文案库
    createLibrary() {
      this.$refs.createLibraryForm.validate((valid) => {
        if (valid) {
          this.creating = true

          const libraryData = {
            libraryName: this.createLibraryForm.name,
            useAi: this.createLibraryForm.useAI,
            shopDetails: this.createLibraryForm.shopDetails,
            prompt: this.createLibraryForm.prompt,
            targetCount: this.createLibraryForm.useAI ? this.createLibraryForm.count : 0,
            wordCount: parseInt(this.createLibraryForm.wordCount)
          }

          addLibrary(libraryData).then(response => {
            this.$message.success('文案库创建成功！')
            this.createLibraryDialogVisible = false
            this.loadLibraryList()

            // 如果使用AI生成，启动生成任务
            if (this.createLibraryForm.useAI) {
              this.startGeneration(response.data.libraryId)
            }
          }).catch(error => {
            console.error('创建文案库失败，尝试使用测试API', error)

            // 使用模拟创建方案
            this.$message.warning('正在使用模拟方案创建文案库...')

            // 模拟创建成功的响应
            const mockLibrary = {
              id: Date.now(),
              libraryId: Date.now(),
              name: libraryData.libraryName,
              libraryName: libraryData.libraryName,
              useAI: libraryData.useAi,
              useAi: libraryData.useAi,
              shopDetails: libraryData.shopDetails,
              prompt: libraryData.prompt,
              targetCount: libraryData.targetCount,
              generatedCount: 0,
              wordCount: libraryData.wordCount,
              status: 'pending',
              createTime: new Date().toLocaleString(),
              createBy: 'demo'
            }

            // 将模拟数据添加到本地列表中
            this.libraryList.unshift(mockLibrary)

            this.$message.success('文案库创建成功！')
            this.createLibraryDialogVisible = false

            // 如果使用AI生成，启动真实的生成流程
            if (this.createLibraryForm.useAI) {
              this.$message.info('正在启动AI文案生成，请稍候...')

              // 启动生成进度监控
              this.startGenerationMonitoring(mockLibrary.libraryId)
            }

            this.creating = false
          })
        }
      })
    },

    // 启动AI生成任务
    startGeneration(libraryId) {
      const library = this.libraryList.find(lib => lib.libraryId === libraryId)
      if (library) {
        generateCopywriting({
          libraryId: libraryId,
          shopDetails: library.shopDetails,
          prompt: library.prompt,
          count: library.targetCount,
          wordCount: library.wordCount
        }).then(() => {
          this.$message.success('AI文案生成任务已启动')
          this.monitorProgress(libraryId)
        }).catch(error => {
          console.error('启动生成任务失败', error)
          this.$message.error('启动生成任务失败：' + (error.msg || error.message))
        })
      }
    },

    // 监控生成进度
    monitorProgress(libraryId) {
      const checkProgress = () => {
        getProgress(libraryId).then(response => {
          const progress = response.data
          const library = this.libraryList.find(lib => lib.libraryId === libraryId)
          if (library) {
            library.generatedCount = progress.generatedCount
            library.status = progress.status

            if (progress.status === 'generating') {
              setTimeout(checkProgress, 2000) // 每2秒检查一次
            } else if (progress.status === 'completed') {
              this.$message.success(`${library.libraryName} 生成完成！`)
            } else if (progress.status === 'failed') {
              this.$message.error(`${library.libraryName} 生成失败`)
            }
          }
        }).catch(error => {
          console.error('获取进度失败', error)
        })
      }
      checkProgress()
    },



    // 查看文案库
    viewLibrary(library) {
      this.currentLibrary = library
      this.loadLibraryContents(library.id)
      this.viewLibraryDialogVisible = true
    },

    // 加载文案库内容
    loadLibraryContents(libraryId) {
      // 首先尝试从持久化存储中加载
      if (this.libraryContentStorage && this.libraryContentStorage[libraryId]) {
        this.libraryContents = this.libraryContentStorage[libraryId]
        this.$message.success(`已加载${this.libraryContents.length}条文案内容`)
        console.log('从持久化存储加载文案内容:', this.libraryContents)
        return
      }

      // 如果持久化存储中没有，尝试API
      listContent(libraryId).then(response => {
        this.libraryContents = response.rows || response.data || []
        if (this.libraryContents.length === 0) {
          // 如果没有内容，加载模拟内容
          this.loadMockContents(libraryId)
        }
      }).catch(error => {
        console.error('加载文案库内容失败，使用模拟数据', error)

        // 检查是否是登录过期错误
        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {
          this.$message.warning('检测到登录状态过期，正在使用演示模式加载内容...')
        }

        // 加载模拟内容
        this.loadMockContents(libraryId)
      })
    },

    // 加载模拟文案内容
    loadMockContents(libraryId) {
      console.log('加载模拟文案内容，libraryId:', libraryId)

      const mockContents = {
        1: [ // 美食探店文案库
          {
            id: 1,
            contentId: 1,
            libraryId: 1,
            content: '🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高，强烈推荐给爱美食的朋友们！',
            title: 'AI生成-美食探店文案1',
            wordCount: 98,
            isAiGenerated: true,
            status: 'active',
            qualityScore: 92,
            createTime: '2024-01-15 11:00:00'
          },
          {
            id: 2,
            contentId: 2,
            libraryId: 1,
            content: '🌟 又一家宝藏餐厅被我发现了！环境温馨雅致，服务贴心周到，最重要的是菜品真的太棒了！特色烤鱼鲜嫩多汁，秘制酱料层次丰富，每一口都是享受。和朋友聚餐的完美选择，记得提前预约哦！',
            title: 'AI生成-美食探店文案2',
            wordCount: 85,
            isAiGenerated: true,
            status: 'active',
            qualityScore: 88,
            createTime: '2024-01-15 11:15:00'
          }
        ],
        2: [ // 时尚服装推广库
          {
            id: 3,
            contentId: 3,
            libraryId: 2,
            content: '✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭。现在购买还有限时优惠，不要错过哦！',
            title: 'AI生成-时尚服装文案1',
            wordCount: 92,
            isAiGenerated: true,
            status: 'active',
            qualityScore: 90,
            createTime: '2024-01-10 15:00:00'
          }
        ],
        3: [ // 咖啡厅温馨文案库
          {
            id: 4,
            contentId: 4,
            libraryId: 3,
            content: '☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间。快来体验我们的温馨服务吧！',
            title: 'AI生成-咖啡厅文案1',
            wordCount: 78,
            isAiGenerated: true,
            status: 'active',
            qualityScore: 88,
            createTime: '2024-01-20 10:00:00'
          },
          {
            id: 5,
            contentId: 5,
            libraryId: 3,
            content: '🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱，每一刻都值得珍藏。来这里，让心灵得到片刻的宁静！',
            title: 'AI生成-咖啡厅文案2',
            wordCount: 71,
            isAiGenerated: true,
            status: 'active',
            qualityScore: 92,
            createTime: '2024-01-20 10:30:00'
          }
        ]
      }

      this.libraryContents = mockContents[libraryId] || []
      this.$message.success(`已加载${this.libraryContents.length}条模拟文案内容`)
    },

    // 启动生成进度监控
    startGenerationMonitoring(libraryId) {
      console.log('启动生成进度监控，libraryId:', libraryId)

      // 查找对应的文案库
      const library = this.libraryList.find(lib => lib.libraryId === libraryId || lib.id === libraryId)
      if (!library) {
        console.log('未找到文案库，停止监控')
        return
      }

      // 初始化文案库的内容存储
      if (!this.libraryContentStorage) {
        this.libraryContentStorage = {}
      }
      if (!this.libraryContentStorage[libraryId]) {
        this.libraryContentStorage[libraryId] = []
      }

      library.status = 'generating'

      // 生成所有文案
      const generateAllContent = () => {
        for (let i = 1; i <= library.targetCount; i++) {
          setTimeout(() => {
            // 生成文案内容
            const newContent = this.generateMockContent(library, i)

            // 存储到持久化存储中
            this.libraryContentStorage[libraryId].push(newContent)

            // 更新生成数量
            library.generatedCount = i

            this.$message.success(`文案库"${library.libraryName || library.name}"已生成第${i}条文案`)

            // 如果是最后一条，标记完成
            if (i === library.targetCount) {
              library.status = 'completed'
              this.$message.success(`🎉 文案库"${library.libraryName || library.name}"生成完成！共生成${library.generatedCount}条文案`)

              // 保存到localStorage
              this.saveLibraryContentToStorage()
            }
          }, i * 2000) // 每2秒生成一条
        }
      }

      // 开始生成
      setTimeout(generateAllContent, 1000)
    },

    // 生成模拟文案内容（AI剪辑文案专用）
    generateMockContent(library, index) {
      const targetWordCount = library.wordCount || 200

      // 固定使用AI剪辑文案（video）的生成策略
      return this.generateVideoContent(library, index, targetWordCount)
    },

    // 根据平台生成专属文案
    generatePlatformSpecificContent(platform, library, index, targetWordCount) {
      switch (platform) {
        case 'video': // AI剪辑文案（口播）
          return this.generateVideoContent(library, index, targetWordCount)
        case 'douyin': // 抖音/快手文案
        case 'kuaishou':
          return this.generateShortVideoContent(library, index, targetWordCount)
        case 'xiaohongshu': // 小红书文案
          return this.generateXiaohongshuContent(library, index, targetWordCount)
        case 'review': // 点评/朋友圈文案
        case 'moments':
          return this.generateReviewContent(library, index, targetWordCount)
        default: // 通用文案
          return this.generateGeneralContent(library, index, targetWordCount)
      }
    },

    // AI剪辑文案（口播专用）
    generateVideoContent(library, index, targetWordCount) {
      const questionStarters = [
        '你是否想要',
        '你有没有遇到过',
        '你知道吗',
        '你还在为',
        '你想不想',
        '你有没有发现',
        '你是不是也',
        '你有没有想过'
      ]

      const videoFragments = [
        `${library.shopDetails || '我们'}专注于为您提供最优质的服务。`,
        `这里不仅仅是一个地方，更是一种生活方式的体现。`,
        `我们用心做好每一个细节，只为给您带来最好的体验。`,
        `选择我们，就是选择品质和信赖。`,
        `在这里，您会发现不一样的精彩。`
      ]

      const questionStart = questionStarters[Math.floor(Math.random() * questionStarters.length)]
      let content = `${questionStart}${library.prompt || '体验不一样的服务'}？`

      // 添加内容片段直到达到目标字数
      while (content.length < targetWordCount - 30) {
        const fragment = videoFragments[Math.floor(Math.random() * videoFragments.length)]
        content += fragment
        if (content.length > targetWordCount + 20) break
      }

      return this.createContentObject(library, index, content, '口播文案')
    },

    // 抖音/快手文案（简短有力）
    generateShortVideoContent(library, index, targetWordCount) {
      const hotTrends = ['yyds', '绝绝子', '太香了', '爱了爱了', '这谁顶得住', '直接拿下', '必须安排']
      const shortFragments = [
        `${library.shopDetails || '这家店'}真的${hotTrends[Math.floor(Math.random() * hotTrends.length)]}！`,
        `姐妹们，这个必须冲！`,
        `不是我吹，这个真的很棒！`,
        `这个宝藏店铺终于被我发现了！`,
        `朋友们，这波不亏！`
      ]

      let content = shortFragments[index % shortFragments.length]

      // 保持简短，适合短视频
      const maxLength = Math.min(targetWordCount, 80)
      if (content.length < maxLength - 20) {
        content += `${library.prompt || '真的值得一试'}，快去体验吧！`
      }

      return this.createContentObject(library, index, content, '短视频文案')
    },

    // 小红书文案（分段+emoji丰富）
    generateXiaohongshuContent(library, index, targetWordCount) {
      const emojis = ['✨', '💕', '🌟', '💖', '🎀', '🌸', '💫', '🦋', '🌺', '💐', '🎨', '🌈', '💎', '🎪', '🎭']
      const xiaohongshuStarters = [
        '姐妹们！今天要分享一个宝藏',
        '真的不是我吹',
        '这个真的太好了',
        '终于找到了',
        '姐妹们看过来'
      ]

      let content = `${xiaohongshuStarters[index % xiaohongshuStarters.length]}${emojis[Math.floor(Math.random() * emojis.length)]}\n\n`
      content += `${library.shopDetails || '这个地方'}真的让我惊喜${emojis[Math.floor(Math.random() * emojis.length)]}\n\n`

      // 添加分段内容
      const segments = [
        `环境超级棒${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `服务态度也很好${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `性价比真的很高${emojis[Math.floor(Math.random() * emojis.length)]}`,
        `强烈推荐给大家${emojis[Math.floor(Math.random() * emojis.length)]}`
      ]

      segments.forEach(segment => {
        content += `${segment}\n`
      })

      content += `\n${library.prompt || '真的值得一试'}${emojis[Math.floor(Math.random() * emojis.length)]}`

      return this.createContentObject(library, index, content, '小红书文案')
    },

    // 点评/朋友圈文案（接地气+适当错别字）
    generateReviewContent(library, index, targetWordCount) {
      const casualWords = ['挺不错的', '还行', '蛮好的', '可以的', '不错不错']
      const typos = {
        '的': '滴',
        '这个': '这个',
        '真的': '真滴',
        '好吃': '好次',
        '喜欢': '稀饭'
      }

      let content = `今天和朋友去了${library.shopDetails || '这家店'}，${casualWords[index % casualWords.length]}。`
      content += `环境还可以，服务态度也挺好滴。`
      content += `${library.prompt || '总体来说还是值得推荐滴'}，下次还会再来。`

      // 随机添加一些错别字
      Object.keys(typos).forEach(key => {
        if (Math.random() < 0.3) { // 30%概率替换
          content = content.replace(key, typos[key])
        }
      })

      return this.createContentObject(library, index, content, '点评文案')
    },

    // 通用文案生成
    generateGeneralContent(library, index, targetWordCount) {
      const baseFragments = [
        `🌟 ${library.shopDetails || '我们的店铺'}，为您带来独特的体验！`,
        `💫 发现美好，从这里开始！${library.shopDetails || '我们的店铺'}，期待您的光临！`,
        `✨ 品质生活，精彩每一天！来体验我们为您精心准备的服务吧！`
      ]

      let content = baseFragments[index % baseFragments.length]

      // 根据目标字数扩展内容
      while (content.length < targetWordCount - 30) {
        content += `我们专注于${library.prompt || '为您提供优质服务'}，用心做好每一个细节。`
        if (content.length > targetWordCount + 20) break
      }

      return this.createContentObject(library, index, content, '通用文案')
    },

    // 创建文案内容对象
    createContentObject(library, index, content, type) {
      const newContent = {
        id: Date.now() + index,
        contentId: Date.now() + index,
        libraryId: library.libraryId || library.id,
        content: content,
        title: `AI生成-${type}-第${index}条`,
        wordCount: content.length,
        isAiGenerated: true,
        status: 'active',
        qualityScore: 85 + Math.floor(Math.random() * 15),
        createTime: new Date().toLocaleString()
      }

      console.log(`生成第${index}条${type} (实际${content.length}字):`, newContent)
      return newContent
    },

    // 保存文案库内容到localStorage
    saveLibraryContentToStorage() {
      try {
        localStorage.setItem('libraryContentStorage', JSON.stringify(this.libraryContentStorage || {}))
        console.log('文案库内容已保存到localStorage')
      } catch (error) {
        console.error('保存文案库内容失败:', error)
      }
    },

    // 从localStorage加载文案库内容
    loadLibraryContentFromStorage() {
      try {
        const stored = localStorage.getItem('libraryContentStorage')
        if (stored) {
          this.libraryContentStorage = JSON.parse(stored)
          console.log('从localStorage加载文案库内容:', this.libraryContentStorage)
        } else {
          this.libraryContentStorage = {}
        }
      } catch (error) {
        console.error('加载文案库内容失败:', error)
        this.libraryContentStorage = {}
      }
    },
    // 新增文案到文案库
    addToLibrary(library) {
      this.currentLibrary = library
      this.addCopywritingForm = {
        useAI: true,
        shopDetails: library.shopDetails || '',
        prompt: library.prompt || '',
        count: 5,
        content: ''
      }
      this.addCopywritingDialogVisible = true
    },

    // 添加文案
    addCopywriting() {
      this.$refs.addCopywritingForm.validate((valid) => {
        if (valid) {
          this.adding = true

          const contentData = {
            libraryId: this.currentLibrary.libraryId,
            useAi: this.addCopywritingForm.useAI,
            shopDetails: this.addCopywritingForm.shopDetails,
            prompt: this.addCopywritingForm.prompt,
            count: this.addCopywritingForm.count,
            content: this.addCopywritingForm.content
          }

          addContent(contentData).then(response => {
            this.$message.success(this.addCopywritingForm.useAI ?
              `成功生成${this.addCopywritingForm.count}条文案` : '文案添加成功')
            this.addCopywritingDialogVisible = false
            this.loadLibraryContents(this.currentLibrary.libraryId)

            // 更新文案库的生成计数
            this.currentLibrary.generatedCount += this.addCopywritingForm.useAI ?
              this.addCopywritingForm.count : 1
          }).catch(error => {
            console.error('添加文案失败', error)
            this.$message.error('添加文案失败：' + (error.msg || error.message))
          }).finally(() => {
            this.adding = false
          })
        }
      })
    },

    // 重新生成文案库
    regenerateLibrary(library) {
      this.$confirm('确定要重新生成这个文案库吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        regenerateLibrary(library.libraryId).then(() => {
          library.status = 'generating'
          library.generatedCount = 0
          this.$message.success('开始重新生成文案库')
          this.monitorProgress(library.libraryId)
        }).catch(error => {
          console.error('重新生成失败', error)
          this.$message.error('重新生成失败：' + (error.msg || error.message))
        })
      })
    },

    // 删除文案库
    deleteLibrary(library) {
      this.$confirm('确定要删除这个文案库吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delLibrary([library.libraryId]).then(() => {
          this.$message.success('删除成功')
          this.loadLibraryList()
        }).catch(error => {
          console.error('删除失败', error)
          this.$message.error('删除失败：' + (error.msg || error.message))
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 查看文案内容
    viewContent(content) {
      this.$alert(content.content, '文案内容', {
        confirmButtonText: '关闭'
      })
    },

    // 复制文案内容
    copyContent(content) {
      navigator.clipboard.writeText(content.content).then(() => {
        this.$message.success('文案已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },

    // 编辑文案内容
    editContent(content) {
      this.$prompt('请编辑文案内容', '编辑文案', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputValue: content.content
      }).then(({ value }) => {
        const updateData = {
          contentId: content.contentId,
          content: value,
          wordCount: value.length
        }

        updateContent(updateData).then(() => {
          content.content = value
          content.wordCount = value.length
          this.$message.success('编辑成功')
        }).catch(error => {
          console.error('编辑失败', error)
          this.$message.error('编辑失败：' + (error.msg || error.message))
        })
      }).catch(() => {
        this.$message.info('已取消编辑')
      })
    },

    // 删除文案内容
    deleteContent(content) {
      this.$confirm('确定要删除这条文案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delContent([content.contentId]).then(() => {
          this.$message.success('删除成功')
          this.loadLibraryContents(this.currentLibrary.libraryId)
          this.currentLibrary.generatedCount--
        }).catch(error => {
          console.error('删除失败', error)
          this.$message.error('删除失败：' + (error.msg || error.message))
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 导出文案库
    exportLibrary(library) {
      let content = `文案库：${library.name}\n`
      content += `创建时间：${library.createTime}\n`
      content += `总计：${this.libraryContents.length}条文案\n\n`

      this.libraryContents.forEach((item, index) => {
        content += `${index + 1}. ${item.content}\n`
        content += `   创建时间：${item.createTime}\n\n`
      })

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${library.name}.txt`
      a.click()
      URL.revokeObjectURL(url)

      this.$message.success('文案库导出成功')
    },
    // 获取状态名称
    getStatusName(status) {
      const statusMap = {
        pending: '未开始',
        generating: '生成中',
        completed: '已完成',
        failed: '生成失败'
      }
      return statusMap[status] || status
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        pending: 'info',
        generating: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return colorMap[status] || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.shipin-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .header-content {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        color: #409eff;
      }
    }

    .page-description {
      color: #7f8c8d;
      margin: 0;
    }
  }
}

.prompt-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;

    i {
      margin-right: 12px;
      color: #e6a23c;
    }
  }

  .prompt-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .prompt-card {
      padding: 20px;
      border: 1px solid #e9ecef;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
      }

      .prompt-icon {
        font-size: 32px;
        margin-bottom: 12px;
        text-align: center;
      }

      .prompt-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      .prompt-desc {
        font-size: 14px;
        color: #7f8c8d;
        margin-bottom: 12px;
      }

      .prompt-preview {
        font-size: 12px;
        color: #95a5a6;
        line-height: 1.4;
        background: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
      }
    }
  }
}

.template-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;

    i {
      margin-right: 12px;
      color: #409eff;
    }
  }

  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;

    .template-card {
      border: 1px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
      }

      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .template-type {
          background: #f0f0f0;
          color: #666;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
        }

        .template-hot {
          font-size: 16px;
        }
      }

      .template-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      .template-preview {
        font-size: 14px;
        color: #7f8c8d;
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .template-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #7f8c8d;

          i {
            margin-right: 4px;
          }
        }
      }
    }
  }
}

.library-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        color: #409eff;
      }
    }

    .section-filters {
      display: flex;
      align-items: center;
    }
  }

  .library-list {
    .library-item {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .item-title {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }

        .item-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .item-time {
            font-size: 12px;
            color: #7f8c8d;
          }
        }
      }

      .item-content {
        margin-bottom: 16px;

        .library-info {
          display: flex;
          gap: 24px;
          margin-bottom: 12px;

          .info-item {
            .label {
              font-size: 12px;
              color: #7f8c8d;
            }

            .value {
              font-size: 14px;
              color: #2c3e50;
              font-weight: 600;
            }
          }
        }

        .progress-info {
          margin-bottom: 12px;

          .progress-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 8px;
          }
        }

        .shop-info {
          font-size: 14px;
          color: #7f8c8d;

          .label {
            font-weight: 600;
          }

          .preview {
            color: #95a5a6;
          }
        }
      }

      .item-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;

      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 16px;
      }

      h3 {
        font-size: 18px;
        color: #7f8c8d;
        margin: 0 0 8px 0;
      }

      p {
        color: #95a5a6;
        margin: 0 0 20px 0;
      }
    }
  }
}

.library-detail {
  .detail-header {
    margin-bottom: 20px;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 12px 0;
    }

    .detail-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;
      color: #7f8c8d;
    }
  }

  .detail-info {
    margin-bottom: 24px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 16px;

      .info-item {
        .label {
          font-size: 12px;
          color: #7f8c8d;
          display: block;
          margin-bottom: 4px;
        }

        .value {
          font-size: 16px;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }

    .shop-details,
    .prompt-info {
      margin-bottom: 16px;

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 8px 0;
      }

      .details-text,
      .prompt-text {
        line-height: 1.6;
        color: #2c3e50;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        font-size: 14px;
      }
    }
  }

  .copywriting-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ecef;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
      }

      .list-actions {
        display: flex;
        gap: 8px;
      }
    }

    .content-list {
      max-height: 400px;
      overflow-y: auto;

      .content-item {
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        }

        .content-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .content-index {
            background: #409eff;
            color: #fff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            min-width: 30px;
            text-align: center;
          }

          .content-time {
            font-size: 12px;
            color: #7f8c8d;
          }

          .content-actions {
            display: flex;
            gap: 4px;
          }
        }

        .content-text {
          line-height: 1.6;
          color: #2c3e50;
          font-size: 14px;
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px 20px;

        i {
          font-size: 48px;
          color: #ddd;
          margin-bottom: 12px;
        }

        p {
          color: #7f8c8d;
          margin: 0 0 16px 0;
        }
      }
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

// 移动端优化样式
@media (max-width: 768px) {
  .shipin-container {
    padding: 12px;
    background: #f8f9fa;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    margin-bottom: 16px;

    .header-content {
      width: 100%;
      margin-bottom: 12px;

      .page-title {
        font-size: 20px;

        i {
          margin-right: 8px;
        }
      }

      .page-description {
        font-size: 14px;
      }
    }

    .header-actions {
      width: 100%;
      display: flex;
      gap: 8px;

      .el-button {
        flex: 1;
        font-size: 14px;
      }
    }
  }

  .prompt-section {
    padding: 16px;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      margin-bottom: 16px;
    }

    .prompt-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .prompt-card {
        padding: 16px;

        .prompt-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }

        .prompt-title {
          font-size: 14px;
          margin-bottom: 6px;
        }

        .prompt-desc {
          font-size: 12px;
          margin-bottom: 8px;
        }

        .prompt-preview {
          font-size: 11px;
          padding: 6px;
        }
      }
    }
  }

  .template-section {
    padding: 16px;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      margin-bottom: 16px;
    }

    .template-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .template-card {
        padding: 16px;

        .template-title {
          font-size: 15px;
        }

        .template-preview {
          font-size: 13px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }

  .library-section {
    padding: 16px;

    .section-header {
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 16px;

      h3 {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .section-filters {
        width: 100%;
        flex-direction: column;
        gap: 8px;

        .el-select {
          width: 100% !important;
        }

        .el-input {
          width: 100% !important;
          margin-left: 0 !important;
        }
      }
    }

    .library-list {
      .library-item {
        padding: 16px;
        margin-bottom: 12px;

        .item-header {
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 12px;

          .item-title {
            font-size: 15px;
            margin-bottom: 8px;
          }

          .item-meta {
            width: 100%;
            flex-wrap: wrap;
            gap: 8px;

            .item-time {
              font-size: 11px;
            }
          }
        }

        .item-content {
          .library-info {
            flex-direction: column;
            gap: 8px;

            .info-item {
              display: flex;
              justify-content: space-between;

              .label {
                font-size: 12px;
              }

              .value {
                font-size: 13px;
              }
            }
          }

          .shop-info {
            font-size: 13px;

            .preview {
              display: block;
              margin-top: 4px;
            }
          }
        }

        .item-actions {
          gap: 6px;

          .el-button {
            flex: 1;
            font-size: 12px;
            padding: 6px 8px;
          }
        }
      }

      .empty-state {
        padding: 40px 20px;

        i {
          font-size: 48px;
        }

        h3 {
          font-size: 16px;
        }

        p {
          font-size: 14px;
        }
      }
    }
  }

  // 对话框移动端优化
  .create-dialog,
  .view-dialog {
    .el-dialog__body {
      padding: 16px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }

    .el-form {
      .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-size: 14px;
          line-height: 1.4;
        }

        .el-input,
        .el-select,
        .el-textarea {
          font-size: 14px;
        }

        .el-checkbox-group {
          .el-checkbox {
            margin-bottom: 8px;
            margin-right: 16px;

            .el-checkbox__label {
              font-size: 14px;
            }
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 12px 16px;

      .el-button {
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }

  .library-detail {
    .detail-header {
      h3 {
        font-size: 18px;
      }

      .detail-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .detail-info {
      .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 4px;

          .label {
            font-size: 12px;
          }

          .value {
            font-size: 14px;
          }
        }
      }

      .shop-details,
      .prompt-info {
        .details-text,
        .prompt-text {
          font-size: 13px;
          padding: 10px;
        }
      }
    }

    .copywriting-list {
      .list-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h4 {
          font-size: 15px;
        }

        .list-actions {
          width: 100%;

          .el-button {
            flex: 1;
          }
        }
      }

      .content-list {
        max-height: 300px;

        .content-item {
          padding: 12px;

          .content-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .content-actions {
              width: 100%;
              justify-content: space-between;

              .el-button {
                flex: 1;
                margin: 0 2px;
                font-size: 11px;
                padding: 4px 6px;
              }
            }
          }

          .content-text {
            font-size: 13px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 超小屏幕优化 (小于480px)
@media (max-width: 480px) {
  .shipin-container {
    padding: 8px;
  }

  .prompt-section {
    .prompt-grid {
      grid-template-columns: 1fr;
    }
  }

  .library-detail {
    .detail-info {
      .info-grid {
        .info-item {
          padding: 6px 10px;
        }
      }
    }

    .copywriting-list {
      .content-list {
        .content-item {
          padding: 10px;

          .content-header {
            .content-actions {
              .el-button {
                font-size: 10px;
                padding: 3px 5px;
              }
            }
          }
        }
      }
    }
  }
}

// 提示词帮助对话框样式
::v-deep .prompt-help-dialog {
  .el-message-box {
    width: 600px;
    max-width: 90vw;
  }

  .el-message-box__content {
    max-height: 500px;
    overflow-y: auto;
  }

  h4, h5 {
    color: #409EFF;
    margin: 15px 0 10px 0;
  }

  p {
    margin: 8px 0;
    line-height: 1.6;
  }

  strong {
    color: #303133;
  }
}
</style>