import request from '@/utils/request'

// 查询文案库列表
export function listLibrary(query) {
  return request({
    url: '/ai/copywriting/library/list',
    method: 'get',
    params: query
  })
}

// 查询文案库详细
export function getLibrary(libraryId) {
  return request({
    url: '/ai/copywriting/library/' + libraryId,
    method: 'get'
  })
}

// 新增文案库
export function addLibrary(data) {
  return request({
    url: '/ai/copywriting/library',
    method: 'post',
    data: data
  })
}

// 修改文案库
export function updateLibrary(data) {
  return request({
    url: '/ai/copywriting/library',
    method: 'put',
    data: data
  })
}

// 删除文案库
export function delLibrary(libraryIds) {
  return request({
    url: '/ai/copywriting/library/' + libraryIds,
    method: 'delete'
  })
}

// 生成AI文案
export function generateCopywriting(data) {
  return request({
    url: '/ai/copywriting/generate',
    method: 'post',
    data: data,
    timeout: 60000 // 60秒超时
  })
}

// 查询文案内容列表
export function listContent(libraryId) {
  return request({
    url: '/ai/copywriting/content/list/' + libraryId,
    method: 'get'
  })
}

// 新增文案内容
export function addContent(data) {
  return request({
    url: '/ai/copywriting/content',
    method: 'post',
    data: data,
    timeout: 30000 // 30秒超时
  })
}

// 修改文案内容
export function updateContent(data) {
  return request({
    url: '/ai/copywriting/content',
    method: 'put',
    data: data
  })
}

// 删除文案内容
export function delContent(contentIds) {
  return request({
    url: '/ai/copywriting/content/' + contentIds,
    method: 'delete'
  })
}

// 获取生成进度
export function getProgress(libraryId) {
  return request({
    url: '/ai/copywriting/progress/' + libraryId,
    method: 'get'
  })
}

// 重新生成文案库
export function regenerateLibrary(libraryId) {
  return request({
    url: '/ai/copywriting/regenerate/' + libraryId,
    method: 'post',
    timeout: 60000 // 60秒超时
  })
}

// 导出文案库
export function exportLibrary(query) {
  return request({
    url: '/ai/copywriting/export',
    method: 'post',
    data: query
  })
}

// 验证百度AI配置
export function validateBaiduConfig() {
  return request({
    url: '/ai/copywriting/validate-config',
    method: 'get'
  })
}

// 获取模型信息
export function getModelInfo() {
  return request({
    url: '/ai/copywriting/model-info',
    method: 'get'
  })
}
