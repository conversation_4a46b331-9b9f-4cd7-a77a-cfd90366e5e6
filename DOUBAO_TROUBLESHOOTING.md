# 火山引擎Doubao API故障排除指南

## 🔍 测试结果
- **状态**: ❌ API调用失败
- **错误码**: 401 未授权
- **错误信息**: 远程服务器返回错误: (401) 未经授权

## 🔧 问题诊断

### 1. API密钥验证
当前使用的API密钥: `5ad57720-913c-410e-b75f-debd2fe836a4z`

**请检查**:
- [ ] API密钥是否正确复制（注意末尾的'z'字符）
- [ ] API密钥是否已激活
- [ ] API密钥是否有访问权限

### 2. 火山引擎账户状态
**请登录火山引擎控制台检查**:
- [ ] 账户是否已实名认证
- [ ] 是否已开通豆包大模型服务
- [ ] 账户余额是否充足
- [ ] API调用配额是否充足

### 3. 模型访问权限
**当前使用模型**: `doubao-seed-1-6-flash-250715`

**请确认**:
- [ ] 该模型是否在您的可用模型列表中
- [ ] 是否有该模型的调用权限
- [ ] 模型名称是否正确

## 🛠️ 解决方案

### 方案1: 重新获取API密钥
1. 登录火山引擎控制台
2. 进入"豆包大模型"服务
3. 在"API管理"中重新生成API密钥
4. 更新项目配置文件中的密钥

### 方案2: 检查账户设置
1. 确认账户已完成实名认证
2. 确认已开通豆包大模型服务
3. 检查账户余额和配额
4. 如需要，进行充值

### 方案3: 验证模型可用性
1. 在控制台查看可用模型列表
2. 确认 `doubao-seed-1-6-flash-250715` 是否可用
3. 如不可用，选择其他可用模型

### 方案4: 使用官方示例测试
使用火山引擎官方文档中的示例代码进行测试，确保基础配置正确。

## 📞 获取帮助

如果以上方案都无法解决问题，建议：

1. **查看官方文档**: https://www.volcengine.com/docs/82379
2. **联系技术支持**: 通过火山引擎控制台提交工单
3. **检查服务状态**: 确认火山引擎服务是否正常运行

## 🔄 下一步行动

1. **立即行动**: 检查并更新API密钥
2. **验证账户**: 确认账户状态和权限
3. **重新测试**: 使用新的API密钥进行测试
4. **更新配置**: 如果密钥有变化，更新所有配置文件

## 📝 测试命令

更新API密钥后，可以使用以下命令重新测试：

```powershell
# 设置新的API密钥
$env:ARK_API_KEY = "YOUR_NEW_API_KEY"

# 运行测试
powershell -ExecutionPolicy Bypass -File "simple-test.ps1"
```

## ⚠️ 注意事项

- API密钥是敏感信息，请妥善保管
- 不要在公开代码库中提交真实的API密钥
- 建议使用环境变量或配置文件管理API密钥
- 定期检查API调用量和费用
