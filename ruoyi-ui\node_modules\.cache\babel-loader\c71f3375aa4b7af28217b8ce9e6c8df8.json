{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dou.vue", "mtime": 1754560080017}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_copywriting", "require", "_copywritingTest", "_default", "exports", "default", "name", "data", "createLibraryDialogVisible", "addCopywritingDialogVisible", "viewLibraryDialogVisible", "creating", "adding", "filterStatus", "searchKeyword", "currentLibrary", "libraryContents", "isMobile", "createLibraryForm", "useAI", "shopDetails", "prompt", "count", "wordCount", "createLibraryRules", "required", "message", "trigger", "min", "max", "addCopywritingForm", "content", "addCopywritingRules", "recommendPrompts", "id", "icon", "title", "desc", "libraryList", "status", "targetCount", "generatedCount", "createTime", "computed", "filteredLibraryList", "_this", "list", "filter", "item", "keyword", "toLowerCase", "includes", "created", "_this2", "loadLibraryContentFromStorage", "loadLibraryList", "setTimeout", "length", "console", "log", "loadMockLibraryList", "mounted", "checkMobile", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "innerWidth", "_this3", "listLibrary", "then", "response", "rows", "catch", "error", "_error$message", "_error$message2", "code", "$message", "warning", "mockLibraries", "libraryId", "libraryName", "useAi", "createBy", "userLibraries", "lib", "concat", "_toConsumableArray2", "success", "refreshData", "_this4", "showCreateLibraryDialog", "usePrompt", "showPromptHelp", "$alert", "dangerouslyUseHTMLString", "confirmButtonText", "customClass", "createLibrary", "_this5", "$refs", "validate", "valid", "libraryData", "parseInt", "addLibrary", "startGeneration", "mockLibrary", "Date", "now", "toLocaleString", "unshift", "info", "startGenerationMonitoring", "_this6", "library", "find", "generateCopywriting", "monitorProgress", "msg", "_this7", "checkProgress", "getProgress", "progress", "viewLibrary", "loadLibraryContents", "_this8", "libraryContentStorage", "listContent", "loadMockContents", "_error$message3", "_error$message4", "mockContents", "contentId", "isAiGenerated", "qualityScore", "_this9", "generateAllContent", "_loop", "i", "newContent", "generateMockContent", "push", "saveLibraryContentToStorage", "index", "targetWordCount", "generateShortVideoContent", "generatePlatformSpecificContent", "platform", "generateVideoContent", "generateXiaohongshuContent", "generateReviewContent", "generateGeneralContent", "intentAnalysis", "analyzePromptIntent", "generateVideoContentByIntent", "controlWordCount", "createContentObject", "intent", "tone", "focus", "style", "questionStarters", "starter", "Math", "floor", "random", "target", "truncated", "substring", "lastPunctuation", "lastIndexOf", "extensions", "generateDouyinContentByIntent", "generateXiaohongshuContentByIntent", "region", "arguments", "undefined", "dialectRatio", "generateReviewContentByIntent", "addDialectToContent", "addTypos", "typos", "Object", "keys", "for<PERSON>ach", "key", "replace", "RegExp", "ratio", "dialects", "regionDialect", "regex", "baseFragments", "type", "localStorage", "setItem", "JSON", "stringify", "stored", "getItem", "parse", "addToLibrary", "addCopywriting", "_this0", "contentData", "addContent", "finally", "regenerateLibrary", "_this1", "$confirm", "cancelButtonText", "deleteLibrary", "_this10", "delLibrary", "viewContent", "copyContent", "_this11", "navigator", "clipboard", "writeText", "<PERSON><PERSON><PERSON><PERSON>", "_this12", "$prompt", "inputType", "inputValue", "_ref", "value", "updateData", "updateContent", "deleteContent", "_this13", "<PERSON><PERSON><PERSON><PERSON>", "exportLibrary", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "getStatusName", "statusMap", "pending", "generating", "completed", "failed", "getStatusColor", "colorMap"], "sources": ["src/views/store/dou.vue"], "sourcesContent": ["<template>\r\n  <div class=\"shipin-container\">\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <h1 class=\"page-title\">\r\n          <i class=\"el-icon-video-camera\"></i>\r\n          抖音/快手文案库\r\n        </h1>\r\n        <p class=\"page-description\">简短有力，融入热门梗，营销感为0</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- AI提示词推荐 -->\r\n    <div class=\"prompt-section\">\r\n      <h3>\r\n        <i class=\"el-icon-lightbulb\"></i>\r\n        AI提示词推荐\r\n      </h3>\r\n      <div class=\"prompt-grid\">\r\n        <div class=\"prompt-card\" @click=\"usePrompt(prompt)\" v-for=\"prompt in recommendPrompts\" :key=\"prompt.id\">\r\n          <div class=\"prompt-icon\">{{ prompt.icon }}</div>\r\n          <div class=\"prompt-title\">{{ prompt.title }}</div>\r\n          <div class=\"prompt-desc\">{{ prompt.desc }}</div>\r\n          <div class=\"prompt-preview\">{{ prompt.content.substring(0, 50) }}...</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文案库列表 -->\r\n    <div class=\"library-section\">\r\n      <div class=\"section-header\">\r\n        <h3>\r\n          <i class=\"el-icon-folder-opened\"></i>\r\n          我的文案库\r\n        </h3>\r\n        <div class=\"section-filters\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refreshData\"\r\n            style=\"margin-right: 12px;\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n          <el-select v-model=\"filterStatus\" placeholder=\"状态\" size=\"small\" style=\"width: 120px;\">\r\n            <el-option label=\"全部\" value=\"\"></el-option>\r\n            <el-option label=\"未开始\" value=\"pending\"></el-option>\r\n            <el-option label=\"生成中\" value=\"generating\"></el-option>\r\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\r\n            <el-option label=\"生成失败\" value=\"failed\"></el-option>\r\n          </el-select>\r\n          <el-input\r\n            v-model=\"searchKeyword\"\r\n            placeholder=\"搜索文案库...\"\r\n            size=\"small\"\r\n            clearable\r\n            style=\"width: 200px; margin-left: 12px;\"\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"library-list\">\r\n        <div v-for=\"library in filteredLibraryList\" :key=\"library.id\" class=\"library-item\">\r\n          <div class=\"item-header\">\r\n            <div class=\"item-title\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              {{ library.name }}\r\n            </div>\r\n            <div class=\"item-meta\">\r\n              <el-tag size=\"mini\" :type=\"getStatusColor(library.status)\">{{ getStatusName(library.status) }}</el-tag>\r\n              <el-tag size=\"mini\" type=\"info\" v-if=\"library.useAI\">AI生成</el-tag>\r\n              <span class=\"item-time\">{{ library.createTime }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-content\">\r\n            <div class=\"library-info\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">目标条数：</span>\r\n                <span class=\"value\">{{ library.targetCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">已生成：</span>\r\n                <span class=\"value\">{{ library.generatedCount }}条</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">字数要求：</span>\r\n                <span class=\"value\">{{ library.wordCount }}字</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 生成进度 -->\r\n            <div class=\"progress-info\" v-if=\"library.status === 'generating'\">\r\n              <el-progress\r\n                :percentage=\"Math.round((library.generatedCount / library.targetCount) * 100)\"\r\n                status=\"success\"\r\n              ></el-progress>\r\n              <div class=\"progress-text\">正在生成第 {{ library.generatedCount + 1 }} 条文案...</div>\r\n            </div>\r\n\r\n            <!-- 店铺详情预览 -->\r\n            <div class=\"shop-info\" v-if=\"library.shopDetails\">\r\n              <span class=\"label\">店铺详情：</span>\r\n              <span class=\"preview\">{{ library.shopDetails.substring(0, 50) }}...</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"item-actions\">\r\n            <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewLibrary(library)\" :disabled=\"library.status === 'pending'\">\r\n              查看文案 ({{ library.generatedCount }})\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"success\" icon=\"el-icon-plus\" @click=\"addToLibrary(library)\" v-if=\"library.status === 'completed'\">\r\n              新增文案\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-refresh\" @click=\"regenerateLibrary(library)\" v-if=\"library.status === 'failed'\">\r\n              重新生成\r\n            </el-button>\r\n            <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteLibrary(library)\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLibraryList.length === 0\" class=\"empty-state\">\r\n          <i class=\"el-icon-folder-add\"></i>\r\n          <h3>暂无文案库</h3>\r\n          <p>点击\"创建文案库\"按钮创建您的第一个AI文案库</p>\r\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"showCreateLibraryDialog\">创建文案库</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 创建文案库对话框 -->\r\n    <el-dialog\r\n      title=\"创建AI文案库\"\r\n      :visible.sync=\"createLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"create-dialog\"\r\n    >\r\n      <el-form :model=\"createLibraryForm\" :rules=\"createLibraryRules\" ref=\"createLibraryForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库名称\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"createLibraryForm.name\"\r\n            placeholder=\"请输入文案库名称\"\r\n            maxlength=\"50\"\r\n            show-word-limit\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"createLibraryForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动创建\"\r\n          ></el-switch>\r\n          <div class=\"form-tip\">开启后将使用百度智能云DeepSeek V3生成文案</div>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"createLibraryForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.shopDetails\"\r\n              placeholder=\"请详细描述您的店铺信息、产品特色、目标客户等\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              maxlength=\"500\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">详细的店铺信息有助于AI生成更精准的文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"createLibraryForm.prompt\"\r\n              placeholder=\"请输入AI生成文案的提示词\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              maxlength=\"300\"\r\n              show-word-limit\r\n            ></el-input>\r\n            <div class=\"form-tip\">\r\n              示例：生成吸引人的美食推广文案，要求语言生动、有食欲感\r\n              <el-button type=\"text\" @click=\"showPromptHelp\">查看提示词建议</el-button>\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"createLibraryForm.count\"\r\n              :min=\"1\"\r\n              :max=\"50\"\r\n              placeholder=\"请输入生成条数\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n            <div class=\"form-tip\">最多可生成50条文案</div>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"大约字数\" prop=\"wordCount\">\r\n            <el-select v-model=\"createLibraryForm.wordCount\" placeholder=\"请选择文案字数\">\r\n              <el-option label=\"50字以内\" value=\"50\"></el-option>\r\n              <el-option label=\"100字左右\" value=\"100\"></el-option>\r\n              <el-option label=\"200字左右\" value=\"200\"></el-option>\r\n              <el-option label=\"300字左右\" value=\"300\"></el-option>\r\n              <el-option label=\"500字左右\" value=\"500\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"createLibraryDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createLibrary\" :loading=\"creating\">\r\n          {{ creating ? '创建中...' : '创建文案库' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新增文案对话框 -->\r\n    <el-dialog\r\n      title=\"新增文案\"\r\n      :visible.sync=\"addCopywritingDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '600px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"add-dialog\"\r\n    >\r\n      <el-form :model=\"addCopywritingForm\" :rules=\"addCopywritingRules\" ref=\"addCopywritingForm\" label-width=\"120px\">\r\n        <el-form-item label=\"文案库\">\r\n          <el-input :value=\"currentLibrary ? currentLibrary.name : ''\" disabled></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否使用AI\" prop=\"useAI\">\r\n          <el-switch\r\n            v-model=\"addCopywritingForm.useAI\"\r\n            active-text=\"AI生成\"\r\n            inactive-text=\"手动输入\"\r\n          ></el-switch>\r\n        </el-form-item>\r\n\r\n        <template v-if=\"addCopywritingForm.useAI\">\r\n          <el-form-item label=\"店铺详情\" prop=\"shopDetails\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.shopDetails\"\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"店铺详情（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"AI提示词\" prop=\"prompt\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.prompt\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"AI提示词（默认使用上次的内容）\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"生成条数\" prop=\"count\">\r\n            <el-input-number\r\n              v-model=\"addCopywritingForm.count\"\r\n              :min=\"1\"\r\n              :max=\"20\"\r\n              style=\"width: 100%\"\r\n            ></el-input-number>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <template v-else>\r\n          <el-form-item label=\"文案内容\" prop=\"content\">\r\n            <el-input\r\n              v-model=\"addCopywritingForm.content\"\r\n              type=\"textarea\"\r\n              :rows=\"6\"\r\n              placeholder=\"请输入文案内容\"\r\n              maxlength=\"1000\"\r\n              show-word-limit\r\n            ></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"addCopywritingDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"addCopywriting\" :loading=\"adding\">\r\n          {{ adding ? (addCopywritingForm.useAI ? '生成中...' : '添加中...') : (addCopywritingForm.useAI ? '生成文案' : '添加文案') }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看文案库对话框 -->\r\n    <el-dialog\r\n      title=\"文案库详情\"\r\n      :visible.sync=\"viewLibraryDialogVisible\"\r\n      :width=\"isMobile ? '95%' : '900px'\"\r\n      :fullscreen=\"isMobile\"\r\n      class=\"view-dialog\"\r\n    >\r\n      <div v-if=\"currentLibrary\" class=\"library-detail\">\r\n        <div class=\"detail-header\">\r\n          <h3>{{ currentLibrary.name }}</h3>\r\n          <div class=\"detail-meta\">\r\n            <el-tag :type=\"getStatusColor(currentLibrary.status)\">{{ getStatusName(currentLibrary.status) }}</el-tag>\r\n            <el-tag type=\"info\" v-if=\"currentLibrary.useAI\">AI生成</el-tag>\r\n            <span>创建时间：{{ currentLibrary.createTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"detail-info\">\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">目标条数：</span>\r\n              <span class=\"value\">{{ currentLibrary.targetCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">已生成：</span>\r\n              <span class=\"value\">{{ currentLibrary.generatedCount }}条</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">字数要求：</span>\r\n              <span class=\"value\">{{ currentLibrary.wordCount }}字</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"shop-details\" v-if=\"currentLibrary.shopDetails\">\r\n            <h4>店铺详情：</h4>\r\n            <div class=\"details-text\">{{ currentLibrary.shopDetails }}</div>\r\n          </div>\r\n\r\n          <div class=\"prompt-info\" v-if=\"currentLibrary.prompt\">\r\n            <h4>AI提示词：</h4>\r\n            <div class=\"prompt-text\">{{ currentLibrary.prompt }}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"copywriting-list\">\r\n          <div class=\"list-header\">\r\n            <h4>文案列表 ({{ libraryContents.length }})</h4>\r\n            <div class=\"list-actions\">\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addToLibrary(currentLibrary)\">新增文案</el-button>\r\n              <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"loadLibraryContents(currentLibrary.id)\">刷新</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"content-list\">\r\n            <div v-for=\"(content, index) in libraryContents\" :key=\"content.id\" class=\"content-item\">\r\n              <div class=\"content-header\">\r\n                <span class=\"content-index\">{{ index + 1 }}</span>\r\n                <span class=\"content-time\">{{ content.createTime }}</span>\r\n                <div class=\"content-actions\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-view\" @click=\"viewContent(content)\">查看</el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-document-copy\" @click=\"copyContent(content)\">复制</el-button>\r\n                  <el-button size=\"mini\" type=\"warning\" icon=\"el-icon-edit\" @click=\"editContent(content)\">编辑</el-button>\r\n                  <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteContent(content)\">删除</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"content-text\">{{ content.content }}</div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"libraryContents.length === 0\" class=\"empty-content\">\r\n              <i class=\"el-icon-document-add\"></i>\r\n              <p>暂无文案内容</p>\r\n              <el-button size=\"small\" type=\"primary\" @click=\"addToLibrary(currentLibrary)\">添加第一条文案</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"viewLibraryDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"success\" @click=\"exportLibrary(currentLibrary)\">导出文案库</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLibrary,\r\n  addLibrary,\r\n  delLibrary,\r\n  generateCopywriting,\r\n  listContent,\r\n  addContent,\r\n  updateContent,\r\n  delContent,\r\n  getProgress,\r\n  regenerateLibrary,\r\n  validateBaiduConfig,\r\n  getModelInfo\r\n} from '@/api/ai/copywriting'\r\n\r\n// 导入测试API作为备用\r\nimport {\r\n  listLibraryTest,\r\n  addLibraryTest,\r\n  testDeepSeekDirect,\r\n  healthCheck\r\n} from '@/api/ai/copywriting-test'\r\n\r\nexport default {\r\n  name: 'StorerDou',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createLibraryDialogVisible: false,\r\n      addCopywritingDialogVisible: false,\r\n      viewLibraryDialogVisible: false,\r\n\r\n      // 加载状态\r\n      creating: false,\r\n      adding: false,\r\n\r\n      // 筛选和搜索\r\n      filterStatus: '',\r\n      searchKeyword: '',\r\n\r\n      // 当前数据\r\n      currentLibrary: null,\r\n      libraryContents: [],\r\n      isMobile: false,\r\n\r\n      // 创建文案库表单\r\n      createLibraryForm: {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '生成简短有力的推广文案，融入网络热词，朋友推荐的语气',\r\n        count: 10,\r\n        wordCount: '50' // 抖音/快手默认50字\r\n      },\r\n      createLibraryRules: {\r\n        name: [\r\n          { required: true, message: '请输入文案库名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' },\r\n          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' },\r\n          { min: 5, max: 300, message: '长度在 5 到 300 个字符', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        wordCount: [\r\n          { required: true, message: '请选择文案字数', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 新增文案表单\r\n      addCopywritingForm: {\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 5,\r\n        content: ''\r\n      },\r\n      addCopywritingRules: {\r\n        shopDetails: [\r\n          { required: true, message: '请输入店铺详情', trigger: 'blur' }\r\n        ],\r\n        prompt: [\r\n          { required: true, message: '请输入AI提示词', trigger: 'blur' }\r\n        ],\r\n        count: [\r\n          { required: true, message: '请输入生成条数', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入文案内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // AI提示词推荐\r\n      recommendPrompts: [\r\n        {\r\n          id: 1,\r\n          icon: '🍔',\r\n          title: '美食推广',\r\n          desc: '适合餐饮店铺',\r\n          content: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围，能够激发顾客的购买欲望'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: '👗',\r\n          title: '服装时尚',\r\n          desc: '适合服装店铺',\r\n          content: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议，展现品牌调性和时尚态度'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: '💄',\r\n          title: '美妆护肤',\r\n          desc: '适合美妆店铺',\r\n          content: '编写专业的美妆护肤文案，强调产品功效、使用体验、适用肌肤类型，传递美丽自信的理念'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: '🏠',\r\n          title: '家居生活',\r\n          desc: '适合家居店铺',\r\n          content: '撰写温馨的家居生活文案，展现产品实用性、设计美感、生活品质提升，营造舒适家庭氛围'\r\n        },\r\n        {\r\n          id: 5,\r\n          icon: '📱',\r\n          title: '数码科技',\r\n          desc: '适合数码店铺',\r\n          content: '制作专业的数码产品文案，突出技术参数、功能特点、使用场景，体现科技感和实用价值'\r\n        },\r\n        {\r\n          id: 6,\r\n          icon: '🎓',\r\n          title: '教育培训',\r\n          desc: '适合教育机构',\r\n          content: '创建有说服力的教育培训文案，强调课程价值、师资力量、学习效果，激发学习兴趣和报名意愿'\r\n        }\r\n      ],\r\n\r\n      // 文案库列表\r\n      libraryList: [\r\n        {\r\n          id: 1,\r\n          name: '美食探店文案库',\r\n          useAI: true,\r\n          status: 'completed',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: '100',\r\n          shopDetails: '我们是一家主打川菜的特色餐厅，位于市中心繁华地段，主营麻辣火锅、水煮鱼、宫保鸡丁等经典川菜，店内装修古朴典雅，服务热情周到。',\r\n          prompt: '生成吸引人的美食推广文案，要求语言生动、有食欲感，突出菜品特色和店铺氛围',\r\n          createTime: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '时尚服装推广库',\r\n          useAI: true,\r\n          status: 'generating',\r\n          targetCount: 30,\r\n          generatedCount: 15,\r\n          wordCount: '150',\r\n          shopDetails: '时尚女装品牌店，主要面向25-35岁都市女性，产品包括职业装、休闲装、晚礼服等，注重品质和设计感。',\r\n          prompt: '创作时尚潮流的服装推广文案，突出款式设计、面料质感、搭配建议',\r\n          createTime: '2024-01-15 10:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '手动创建文案库',\r\n          useAI: false,\r\n          status: 'completed',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: '200',\r\n          shopDetails: '',\r\n          prompt: '',\r\n          createTime: '2024-01-14 16:20:00'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredLibraryList() {\r\n      let list = this.libraryList\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        list = list.filter(item => item.status === this.filterStatus)\r\n      }\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        list = list.filter(item =>\r\n          item.name.toLowerCase().includes(keyword) ||\r\n          (item.shopDetails && item.shopDetails.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      return list\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化持久化存储\r\n    this.loadLibraryContentFromStorage()\r\n\r\n    this.loadLibraryList()\r\n\r\n    // 备用方案：如果3秒后还没有数据，直接加载模拟数据\r\n    setTimeout(() => {\r\n      if (this.libraryList.length === 0) {\r\n        console.log('3秒后仍无数据，强制加载模拟数据')\r\n        this.loadMockLibraryList()\r\n      }\r\n    }, 3000)\r\n  },\r\n  mounted() {\r\n    this.checkMobile()\r\n    window.addEventListener('resize', this.checkMobile)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkMobile)\r\n  },\r\n  methods: {\r\n    checkMobile() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n\r\n    loadLibraryList() {\r\n      listLibrary().then(response => {\r\n        this.libraryList = response.rows || response.data || []\r\n        if (this.libraryList.length === 0) {\r\n          // 如果返回空数据，也加载模拟数据\r\n          this.loadMockLibraryList()\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库列表失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式...')\r\n        }\r\n\r\n        // 使用模拟数据作为备用方案\r\n        this.loadMockLibraryList()\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案库数据\r\n    loadMockLibraryList() {\r\n      console.log('加载模拟文案库数据')\r\n\r\n      const mockLibraries = [\r\n        {\r\n          id: 1,\r\n          libraryId: 1,\r\n          name: '美食探店文案库',\r\n          libraryName: '美食探店文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '精选美食餐厅，提供各类特色菜品和优质服务',\r\n          prompt: '生成吸引人的美食探店文案，突出菜品特色和用餐体验',\r\n          targetCount: 20,\r\n          generatedCount: 20,\r\n          wordCount: 150,\r\n          status: 'completed',\r\n          createTime: '2024-01-15 10:30:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 2,\r\n          libraryId: 2,\r\n          name: '时尚服装推广库',\r\n          libraryName: '时尚服装推广库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '时尚服装品牌，主营潮流服饰和配饰',\r\n          prompt: '生成时尚服装推广文案，强调款式新颖和品质优良',\r\n          targetCount: 15,\r\n          generatedCount: 15,\r\n          wordCount: 120,\r\n          status: 'completed',\r\n          createTime: '2024-01-10 14:20:00',\r\n          createBy: 'admin'\r\n        },\r\n        {\r\n          id: 3,\r\n          libraryId: 3,\r\n          name: '咖啡厅温馨文案库',\r\n          libraryName: '咖啡厅温馨文案库',\r\n          useAI: true,\r\n          useAi: true,\r\n          shopDetails: '温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段',\r\n          prompt: '生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质',\r\n          targetCount: 10,\r\n          generatedCount: 8,\r\n          wordCount: 100,\r\n          status: 'generating',\r\n          createTime: '2024-01-20 09:15:00',\r\n          createBy: 'user'\r\n        }\r\n      ]\r\n\r\n      // 添加用户创建的文案库（如果有的话）\r\n      const userLibraries = this.libraryList.filter(lib => lib.createBy === 'demo')\r\n\r\n      this.libraryList = [...mockLibraries, ...userLibraries]\r\n      this.$message.success('已加载模拟文案库数据（共' + this.libraryList.length + '个文案库）')\r\n    },\r\n    refreshData() {\r\n      console.log('手动刷新数据')\r\n      this.loadLibraryList()\r\n\r\n      // 如果1秒后还没有数据，直接加载模拟数据\r\n      setTimeout(() => {\r\n        if (this.libraryList.length === 0) {\r\n          console.log('刷新后仍无数据，加载模拟数据')\r\n          this.loadMockLibraryList()\r\n        } else {\r\n          this.$message.success('数据已刷新')\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    // 显示创建文案库对话框\r\n    showCreateLibraryDialog() {\r\n      this.createLibraryDialogVisible = true\r\n      this.createLibraryForm = {\r\n        name: '',\r\n        useAI: true,\r\n        shopDetails: '',\r\n        prompt: '',\r\n        count: 10,\r\n        wordCount: '100'\r\n      }\r\n    },\r\n\r\n    // 使用推荐提示词\r\n    usePrompt(prompt) {\r\n      this.createLibraryForm.prompt = prompt.content\r\n      this.createLibraryDialogVisible = true\r\n      this.$message.success(`已应用${prompt.title}提示词`)\r\n    },\r\n\r\n    // 显示提示词帮助\r\n    showPromptHelp() {\r\n      this.$alert(`\r\n        <h4>AI剪辑文案提示词建议：</h4>\r\n        <p><strong>核心要求：</strong>适合口播，开头用疑问句吸引观众，语言顺口易读</p>\r\n        <br>\r\n        <h5>📝 推荐提示词模板：</h5>\r\n        <p><strong>1. 美食餐饮：</strong>生成适合口播的美食推广文案，开头用疑问句吸引观众，突出食材新鲜和口感层次，语言生动有食欲感，朋友推荐的语气</p>\r\n        <p><strong>2. 生活服务：</strong>生成温馨的生活服务推广文案，开头用疑问句引起共鸣，强调便民和贴心服务，语言亲切自然，像邻居朋友介绍</p>\r\n        <p><strong>3. 时尚美妆：</strong>生成时尚美妆种草文案，开头用疑问句抓住痛点，突出产品效果和使用体验，语言轻松活泼，姐妹分享的感觉</p>\r\n        <p><strong>4. 教育培训：</strong>生成教育培训推广文案，开头用疑问句引发思考，强调学习效果和成长价值，语言专业但不失亲和力</p>\r\n        <p><strong>5. 健康养生：</strong>生成健康养生科普文案，开头用疑问句引起关注，突出健康理念和实用方法，语言通俗易懂，专业可信</p>\r\n        <p><strong>6. 旅游出行：</strong>生成旅游景点推广文案，开头用疑问句激发向往，描述美景和独特体验，语言富有画面感和感染力</p>\r\n        <p><strong>7. 科技数码：</strong>生成数码产品介绍文案，开头用疑问句抓住需求，突出功能特点和使用便利，语言简洁明了，避免过于技术化</p>\r\n        <p><strong>8. 家居生活：</strong>生成家居用品推广文案，开头用疑问句触及生活痛点，强调实用性和生活品质提升，语言温馨贴近生活</p>\r\n        <br>\r\n        <h5>✍️ 编写技巧：</h5>\r\n        <p>• <strong>疑问开头：</strong>用\"你是否想要...\"、\"你知道吗...\"等疑问句开头</p>\r\n        <p>• <strong>顺口易读：</strong>避免拗口词汇，多用短句，适合朗读</p>\r\n        <p>• <strong>朋友语气：</strong>温和亲切，像朋友分享，营销感为0</p>\r\n        <p>• <strong>具体描述：</strong>结合您的店铺特色，越具体越好</p>\r\n      `, 'AI剪辑文案提示词指南', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '知道了',\r\n        customClass: 'prompt-help-dialog'\r\n      })\r\n    },\r\n    // 创建文案库\r\n    createLibrary() {\r\n      this.$refs.createLibraryForm.validate((valid) => {\r\n        if (valid) {\r\n          this.creating = true\r\n\r\n          const libraryData = {\r\n            libraryName: this.createLibraryForm.name,\r\n            useAi: this.createLibraryForm.useAI,\r\n            shopDetails: this.createLibraryForm.shopDetails,\r\n            prompt: this.createLibraryForm.prompt,\r\n            targetCount: this.createLibraryForm.useAI ? this.createLibraryForm.count : 0,\r\n            wordCount: parseInt(this.createLibraryForm.wordCount)\r\n          }\r\n\r\n          addLibrary(libraryData).then(response => {\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n            this.loadLibraryList()\r\n\r\n            // 如果使用AI生成，启动生成任务\r\n            if (this.createLibraryForm.useAI) {\r\n              this.startGeneration(response.data.libraryId)\r\n            }\r\n          }).catch(error => {\r\n            console.error('创建文案库失败，尝试使用测试API', error)\r\n\r\n            // 使用模拟创建方案\r\n            this.$message.warning('正在使用模拟方案创建文案库...')\r\n\r\n            // 模拟创建成功的响应\r\n            const mockLibrary = {\r\n              id: Date.now(),\r\n              libraryId: Date.now(),\r\n              name: libraryData.libraryName,\r\n              libraryName: libraryData.libraryName,\r\n              useAI: libraryData.useAi,\r\n              useAi: libraryData.useAi,\r\n              shopDetails: libraryData.shopDetails,\r\n              prompt: libraryData.prompt,\r\n              targetCount: libraryData.targetCount,\r\n              generatedCount: 0,\r\n              wordCount: libraryData.wordCount,\r\n              status: 'pending',\r\n              createTime: new Date().toLocaleString(),\r\n              createBy: 'demo'\r\n            }\r\n\r\n            // 将模拟数据添加到本地列表中\r\n            this.libraryList.unshift(mockLibrary)\r\n\r\n            this.$message.success('文案库创建成功！')\r\n            this.createLibraryDialogVisible = false\r\n\r\n            // 如果使用AI生成，启动真实的生成流程\r\n            if (this.createLibraryForm.useAI) {\r\n              this.$message.info('正在启动AI文案生成，请稍候...')\r\n\r\n              // 启动生成进度监控\r\n              this.startGenerationMonitoring(mockLibrary.libraryId)\r\n            }\r\n\r\n            this.creating = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 启动AI生成任务\r\n    startGeneration(libraryId) {\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n      if (library) {\r\n        generateCopywriting({\r\n          libraryId: libraryId,\r\n          shopDetails: library.shopDetails,\r\n          prompt: library.prompt,\r\n          count: library.targetCount,\r\n          wordCount: library.wordCount\r\n        }).then(() => {\r\n          this.$message.success('AI文案生成任务已启动')\r\n          this.monitorProgress(libraryId)\r\n        }).catch(error => {\r\n          console.error('启动生成任务失败', error)\r\n          this.$message.error('启动生成任务失败：' + (error.msg || error.message))\r\n        })\r\n      }\r\n    },\r\n\r\n    // 监控生成进度\r\n    monitorProgress(libraryId) {\r\n      const checkProgress = () => {\r\n        getProgress(libraryId).then(response => {\r\n          const progress = response.data\r\n          const library = this.libraryList.find(lib => lib.libraryId === libraryId)\r\n          if (library) {\r\n            library.generatedCount = progress.generatedCount\r\n            library.status = progress.status\r\n\r\n            if (progress.status === 'generating') {\r\n              setTimeout(checkProgress, 2000) // 每2秒检查一次\r\n            } else if (progress.status === 'completed') {\r\n              this.$message.success(`${library.libraryName} 生成完成！`)\r\n            } else if (progress.status === 'failed') {\r\n              this.$message.error(`${library.libraryName} 生成失败`)\r\n            }\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取进度失败', error)\r\n        })\r\n      }\r\n      checkProgress()\r\n    },\r\n\r\n\r\n\r\n    // 查看文案库\r\n    viewLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.loadLibraryContents(library.id)\r\n      this.viewLibraryDialogVisible = true\r\n    },\r\n\r\n    // 加载文案库内容\r\n    loadLibraryContents(libraryId) {\r\n      // 首先尝试从持久化存储中加载\r\n      if (this.libraryContentStorage && this.libraryContentStorage[libraryId]) {\r\n        this.libraryContents = this.libraryContentStorage[libraryId]\r\n        this.$message.success(`已加载${this.libraryContents.length}条文案内容`)\r\n        console.log('从持久化存储加载文案内容:', this.libraryContents)\r\n        return\r\n      }\r\n\r\n      // 如果持久化存储中没有，尝试API\r\n      listContent(libraryId).then(response => {\r\n        this.libraryContents = response.rows || response.data || []\r\n        if (this.libraryContents.length === 0) {\r\n          // 如果没有内容，加载模拟内容\r\n          this.loadMockContents(libraryId)\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载文案库内容失败，使用模拟数据', error)\r\n\r\n        // 检查是否是登录过期错误\r\n        if (error.code === 401 || error.message?.includes('登录') || error.message?.includes('过期')) {\r\n          this.$message.warning('检测到登录状态过期，正在使用演示模式加载内容...')\r\n        }\r\n\r\n        // 加载模拟内容\r\n        this.loadMockContents(libraryId)\r\n      })\r\n    },\r\n\r\n    // 加载模拟文案内容\r\n    loadMockContents(libraryId) {\r\n      console.log('加载模拟文案内容，libraryId:', libraryId)\r\n\r\n      const mockContents = {\r\n        1: [ // 美食探店文案库\r\n          {\r\n            id: 1,\r\n            contentId: 1,\r\n            libraryId: 1,\r\n            content: '🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高，强烈推荐给爱美食的朋友们！',\r\n            title: 'AI生成-美食探店文案1',\r\n            wordCount: 98,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-15 11:00:00'\r\n          },\r\n          {\r\n            id: 2,\r\n            contentId: 2,\r\n            libraryId: 1,\r\n            content: '🌟 又一家宝藏餐厅被我发现了！环境温馨雅致，服务贴心周到，最重要的是菜品真的太棒了！特色烤鱼鲜嫩多汁，秘制酱料层次丰富，每一口都是享受。和朋友聚餐的完美选择，记得提前预约哦！',\r\n            title: 'AI生成-美食探店文案2',\r\n            wordCount: 85,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-15 11:15:00'\r\n          }\r\n        ],\r\n        2: [ // 时尚服装推广库\r\n          {\r\n            id: 3,\r\n            contentId: 3,\r\n            libraryId: 2,\r\n            content: '✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭。现在购买还有限时优惠，不要错过哦！',\r\n            title: 'AI生成-时尚服装文案1',\r\n            wordCount: 92,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 90,\r\n            createTime: '2024-01-10 15:00:00'\r\n          }\r\n        ],\r\n        3: [ // 咖啡厅温馨文案库\r\n          {\r\n            id: 4,\r\n            contentId: 4,\r\n            libraryId: 3,\r\n            content: '☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间。快来体验我们的温馨服务吧！',\r\n            title: 'AI生成-咖啡厅文案1',\r\n            wordCount: 78,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 88,\r\n            createTime: '2024-01-20 10:00:00'\r\n          },\r\n          {\r\n            id: 5,\r\n            contentId: 5,\r\n            libraryId: 3,\r\n            content: '🌟 品味生活，从一杯好咖啡开始！我们的咖啡厅不仅有香醇的咖啡，还有温馨的环境和贴心的服务。每一口都是对生活的热爱，每一刻都值得珍藏。来这里，让心灵得到片刻的宁静！',\r\n            title: 'AI生成-咖啡厅文案2',\r\n            wordCount: 71,\r\n            isAiGenerated: true,\r\n            status: 'active',\r\n            qualityScore: 92,\r\n            createTime: '2024-01-20 10:30:00'\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.libraryContents = mockContents[libraryId] || []\r\n      this.$message.success(`已加载${this.libraryContents.length}条模拟文案内容`)\r\n    },\r\n\r\n    // 启动生成进度监控\r\n    startGenerationMonitoring(libraryId) {\r\n      console.log('启动生成进度监控，libraryId:', libraryId)\r\n\r\n      // 查找对应的文案库\r\n      const library = this.libraryList.find(lib => lib.libraryId === libraryId || lib.id === libraryId)\r\n      if (!library) {\r\n        console.log('未找到文案库，停止监控')\r\n        return\r\n      }\r\n\r\n      // 初始化文案库的内容存储\r\n      if (!this.libraryContentStorage) {\r\n        this.libraryContentStorage = {}\r\n      }\r\n      if (!this.libraryContentStorage[libraryId]) {\r\n        this.libraryContentStorage[libraryId] = []\r\n      }\r\n\r\n      library.status = 'generating'\r\n\r\n      // 生成所有文案\r\n      const generateAllContent = () => {\r\n        for (let i = 1; i <= library.targetCount; i++) {\r\n          setTimeout(() => {\r\n            // 生成文案内容\r\n            const newContent = this.generateMockContent(library, i)\r\n\r\n            // 存储到持久化存储中\r\n            this.libraryContentStorage[libraryId].push(newContent)\r\n\r\n            // 更新生成数量\r\n            library.generatedCount = i\r\n\r\n            this.$message.success(`文案库\"${library.libraryName || library.name}\"已生成第${i}条文案`)\r\n\r\n            // 如果是最后一条，标记完成\r\n            if (i === library.targetCount) {\r\n              library.status = 'completed'\r\n              this.$message.success(`🎉 文案库\"${library.libraryName || library.name}\"生成完成！共生成${library.generatedCount}条文案`)\r\n\r\n              // 保存到localStorage\r\n              this.saveLibraryContentToStorage()\r\n            }\r\n          }, i * 2000) // 每2秒生成一条\r\n        }\r\n      }\r\n\r\n      // 开始生成\r\n      setTimeout(generateAllContent, 1000)\r\n    },\r\n\r\n    // 生成模拟文案内容（抖音/快手专用）\r\n    generateMockContent(library, index) {\r\n      const targetWordCount = library.wordCount || 50\r\n\r\n      // 固定使用抖音/快手短视频的生成策略\r\n      return this.generateShortVideoContent(library, index, targetWordCount)\r\n    },\r\n\r\n    // 根据平台生成专属文案\r\n    generatePlatformSpecificContent(platform, library, index, targetWordCount) {\r\n      switch (platform) {\r\n        case 'video': // AI剪辑文案（口播）\r\n          return this.generateVideoContent(library, index, targetWordCount)\r\n        case 'douyin': // 抖音/快手文案\r\n        case 'kuaishou':\r\n          return this.generateShortVideoContent(library, index, targetWordCount)\r\n        case 'xiaohongshu': // 小红书文案\r\n          return this.generateXiaohongshuContent(library, index, targetWordCount)\r\n        case 'review': // 点评/朋友圈文案\r\n        case 'moments':\r\n          return this.generateReviewContent(library, index, targetWordCount)\r\n        default: // 通用文案\r\n          return this.generateGeneralContent(library, index, targetWordCount)\r\n      }\r\n    },\r\n\r\n    // AI剪辑文案（口播专用）\r\n    // AI剪辑文案（真正的AI生成，适合口播）\r\n    generateVideoContent(library, index, targetWordCount) {\r\n      // 模拟真正的AI理解和生成过程\r\n      const prompt = library.prompt || '推荐这个地方'\r\n      const shopDetails = library.shopDetails || '一个不错的地方'\r\n\r\n      // AI分析提示词意图\r\n      const intentAnalysis = this.analyzePromptIntent(prompt)\r\n\r\n      // 根据意图和店铺信息生成口播文案\r\n      let content = this.generateVideoContentByIntent(intentAnalysis, shopDetails, targetWordCount)\r\n\r\n      // 确保字数控制\r\n      content = this.controlWordCount(content, targetWordCount)\r\n\r\n      return this.createContentObject(library, index, content, '口播文案')\r\n    },\r\n\r\n    // 分析提示词意图\r\n    analyzePromptIntent(prompt) {\r\n      const intent = {\r\n        tone: 'neutral', // 语调：enthusiastic, neutral, professional\r\n        focus: 'general', // 重点：quality, price, service, experience\r\n        style: 'conversational' // 风格：conversational, informative, persuasive\r\n      }\r\n\r\n      // 分析语调\r\n      if (prompt.includes('强烈推荐') || prompt.includes('必须') || prompt.includes('绝对')) {\r\n        intent.tone = 'enthusiastic'\r\n      } else if (prompt.includes('专业') || prompt.includes('品质') || prompt.includes('标准')) {\r\n        intent.tone = 'professional'\r\n      }\r\n\r\n      // 分析重点\r\n      if (prompt.includes('便宜') || prompt.includes('实惠') || prompt.includes('性价比')) {\r\n        intent.focus = 'price'\r\n      } else if (prompt.includes('服务') || prompt.includes('态度') || prompt.includes('贴心')) {\r\n        intent.focus = 'service'\r\n      } else if (prompt.includes('品质') || prompt.includes('质量') || prompt.includes('精致')) {\r\n        intent.focus = 'quality'\r\n      } else if (prompt.includes('体验') || prompt.includes('感受') || prompt.includes('氛围')) {\r\n        intent.focus = 'experience'\r\n      }\r\n\r\n      return intent\r\n    },\r\n\r\n    // 根据意图生成口播文案\r\n    generateVideoContentByIntent(intent, shopDetails, targetWordCount) {\r\n      let content = ''\r\n\r\n      // 口播特色：疑问句开头吸引观众\r\n      const questionStarters = [\r\n        '你有没有想过', '你知道吗', '你是否注意到', '你有没有发现'\r\n      ]\r\n      const starter = questionStarters[Math.floor(Math.random() * questionStarters.length)]\r\n\r\n      // 根据语调生成开头\r\n      if (intent.tone === 'enthusiastic') {\r\n        content = `${starter}，${shopDetails}真的让人惊喜！`\r\n      } else if (intent.tone === 'professional') {\r\n        content = `${starter}，${shopDetails}在专业水准上确实值得认可。`\r\n      } else {\r\n        content = `${starter}，${shopDetails}给人的感觉还是不错的。`\r\n      }\r\n\r\n      // 根据重点扩展内容\r\n      if (intent.focus === 'price') {\r\n        content += '性价比方面确实让人满意，'\r\n      } else if (intent.focus === 'service') {\r\n        content += '服务体验方面做得很到位，'\r\n      } else if (intent.focus === 'quality') {\r\n        content += '品质方面确实有保障，'\r\n      } else if (intent.focus === 'experience') {\r\n        content += '整体体验感受很不错，'\r\n      }\r\n\r\n      // 口播结尾\r\n      content += '这就是我想要分享给大家的。'\r\n\r\n      return content\r\n    },\r\n\r\n    // 字数控制方法\r\n    controlWordCount(content, targetWordCount) {\r\n      const target = parseInt(targetWordCount) || 100\r\n\r\n      if (content.length > target + 10) {\r\n        // 超出字数，智能截取\r\n        let truncated = content.substring(0, target - 3)\r\n        // 找到最后一个句号或逗号\r\n        const lastPunctuation = Math.max(\r\n          truncated.lastIndexOf('。'),\r\n          truncated.lastIndexOf('，'),\r\n          truncated.lastIndexOf('！'),\r\n          truncated.lastIndexOf('？')\r\n        )\r\n        if (lastPunctuation > target * 0.7) {\r\n          truncated = truncated.substring(0, lastPunctuation + 1)\r\n        } else {\r\n          truncated += '...'\r\n        }\r\n        return truncated\r\n      } else if (content.length < target - 20) {\r\n        // 字数不够，适当扩展\r\n        const extensions = [\r\n          '值得大家了解一下。',\r\n          '希望这个分享对你有帮助。',\r\n          '相信你会有同样的感受。'\r\n        ]\r\n        content += extensions[Math.floor(Math.random() * extensions.length)]\r\n      }\r\n\r\n      return content\r\n    },\r\n\r\n    // 抖音/快手文案（真正的AI生成，简短有力）\r\n    generateShortVideoContent(library, index, targetWordCount) {\r\n      const prompt = library.prompt || '推荐这个地方'\r\n      const shopDetails = library.shopDetails || '一个不错的地方'\r\n\r\n      // AI分析提示词意图\r\n      const intentAnalysis = this.analyzePromptIntent(prompt)\r\n\r\n      // 抖音/快手特色：简短有力，适当网络用语\r\n      let content = this.generateDouyinContentByIntent(intentAnalysis, shopDetails)\r\n\r\n      // 严格控制字数\r\n      content = this.controlWordCount(content, targetWordCount)\r\n\r\n      return this.createContentObject(library, index, content, '短视频文案')\r\n    },\r\n\r\n    // 根据意图生成抖音/快手文案\r\n    generateDouyinContentByIntent(intent, shopDetails) {\r\n      let content = ''\r\n\r\n      // 抖音特色：直接有力的开头\r\n      if (intent.tone === 'enthusiastic') {\r\n        content = `${shopDetails}真的绝了！`\r\n      } else if (intent.tone === 'professional') {\r\n        content = `${shopDetails}品质在线。`\r\n      } else {\r\n        content = `${shopDetails}还不错。`\r\n      }\r\n\r\n      // 根据重点添加描述\r\n      if (intent.focus === 'price') {\r\n        content += '性价比yyds！'\r\n      } else if (intent.focus === 'service') {\r\n        content += '服务很棒！'\r\n      } else if (intent.focus === 'quality') {\r\n        content += '品质没得说！'\r\n      } else {\r\n        content += '值得一试！'\r\n      }\r\n\r\n      return content\r\n    },\r\n\r\n    // 小红书文案（真正的AI生成，种草风格）\r\n    generateXiaohongshuContent(library, index, targetWordCount) {\r\n      const prompt = library.prompt || '推荐这个地方'\r\n      const shopDetails = library.shopDetails || '一个不错的地方'\r\n\r\n      // AI分析提示词意图\r\n      const intentAnalysis = this.analyzePromptIntent(prompt)\r\n\r\n      // 小红书特色：分段清晰，emoji丰富，种草语气\r\n      let content = this.generateXiaohongshuContentByIntent(intentAnalysis, shopDetails)\r\n\r\n      // 控制字数\r\n      content = this.controlWordCount(content, targetWordCount)\r\n\r\n      return this.createContentObject(library, index, content, '小红书文案')\r\n    },\r\n\r\n    // 根据意图生成小红书文案\r\n    generateXiaohongshuContentByIntent(intent, shopDetails) {\r\n      let content = ''\r\n\r\n      // 小红书特色：种草开头\r\n      if (intent.tone === 'enthusiastic') {\r\n        content = `姐妹们！${shopDetails}真的太棒了✨\\n\\n`\r\n      } else if (intent.tone === 'professional') {\r\n        content = `分享一个${shopDetails}💕\\n\\n`\r\n      } else {\r\n        content = `今天去了${shopDetails}🌟\\n\\n`\r\n      }\r\n\r\n      // 根据重点添加分段描述\r\n      if (intent.focus === 'price') {\r\n        content += '性价比真的很高💰\\n'\r\n      } else if (intent.focus === 'service') {\r\n        content += '服务态度超级好👍\\n'\r\n      } else if (intent.focus === 'quality') {\r\n        content += '品质真的没话说✨\\n'\r\n      } else {\r\n        content += '整体体验很不错💕\\n'\r\n      }\r\n\r\n      content += '\\n推荐给大家🌈'\r\n\r\n      return content\r\n    },\r\n\r\n    // 点评/朋友圈文案（真正的AI生成，接地气风格）\r\n    generateReviewContent(library, index, targetWordCount, region = '', dialectRatio = 0) {\r\n      const prompt = library.prompt || '推荐这个地方'\r\n      const shopDetails = library.shopDetails || '一个不错的地方'\r\n\r\n      // AI分析提示词意图\r\n      const intentAnalysis = this.analyzePromptIntent(prompt)\r\n\r\n      // 点评特色：接地气，真实体验感\r\n      let content = this.generateReviewContentByIntent(intentAnalysis, shopDetails)\r\n\r\n      // 根据地区添加方言\r\n      if (region && dialectRatio > 0) {\r\n        content = this.addDialectToContent(content, region, dialectRatio)\r\n      }\r\n\r\n      // 适当添加同音错别字（降低审核风险）\r\n      content = this.addTypos(content)\r\n\r\n      // 控制字数\r\n      content = this.controlWordCount(content, targetWordCount)\r\n\r\n      return this.createContentObject(library, index, content, '点评文案')\r\n    },\r\n\r\n    // 根据意图生成点评文案\r\n    generateReviewContentByIntent(intent, shopDetails) {\r\n      let content = ''\r\n\r\n      // 点评特色：真实体验开头\r\n      if (intent.tone === 'enthusiastic') {\r\n        content = `今天去了${shopDetails}，真的超棒！`\r\n      } else if (intent.tone === 'professional') {\r\n        content = `体验了${shopDetails}，整体水准不错。`\r\n      } else {\r\n        content = `去了${shopDetails}，感觉还行。`\r\n      }\r\n\r\n      // 根据重点添加描述\r\n      if (intent.focus === 'price') {\r\n        content += '价格很实惠，性价比高。'\r\n      } else if (intent.focus === 'service') {\r\n        content += '服务态度很好，很贴心。'\r\n      } else if (intent.focus === 'quality') {\r\n        content += '品质确实不错，值得信赖。'\r\n      } else {\r\n        content += '整体体验还是满意的。'\r\n      }\r\n\r\n      content += '推荐给大家。'\r\n\r\n      return content\r\n    },\r\n\r\n    // 添加同音错别字\r\n    addTypos(content) {\r\n      const typos = {\r\n        '的': '滴', '真的': '真滴', '好吃': '好次', '知道': '造', '什么': '神马'\r\n      }\r\n\r\n      Object.keys(typos).forEach(key => {\r\n        if (Math.random() < 0.2) { // 20%概率替换\r\n          content = content.replace(new RegExp(key, 'g'), typos[key])\r\n        }\r\n      })\r\n\r\n      return content\r\n    },\r\n\r\n    // 根据地区添加方言\r\n    addDialectToContent(content, region, ratio) {\r\n      // 各地区方言词汇库\r\n      const dialects = {\r\n        '北京': {\r\n          '很': '倍儿', '非常': '老', '好': '棒', '不错': '不赖', '厉害': '牛逼',\r\n          '东西': '玩意儿', '地方': '地界儿', '朋友': '哥们儿', '吃': '造'\r\n        },\r\n        '上海': {\r\n          '很': '老', '好': '赞', '不错': '蛮好', '厉害': '交关好', '小': '小小',\r\n          '大': '老大', '便宜': '便宜', '贵': '老贵', '漂亮': '好看'\r\n        },\r\n        '广东': {\r\n          '很': '好', '厉害': '犀利', '好吃': '好食', '漂亮': '靓', '便宜': '平',\r\n          '贵': '贵', '小': '细', '大': '大', '快': '快', '慢': '慢'\r\n        },\r\n        '四川': {\r\n          '很': '老', '好': '巴适', '厉害': '凶', '漂亮': '乖', '小': '小小',\r\n          '吃': '恰', '走': '切', '看': '瞅', '说': '摆'\r\n        },\r\n        '东北': {\r\n          '很': '老', '好': '得劲', '厉害': '牛逼', '漂亮': '水灵', '小': '小小',\r\n          '大': '老大', '吃': '造', '走': '走', '看': '瞅', '说': '唠'\r\n        },\r\n        '湖南': {\r\n          '很': '蛮', '好': '好', '厉害': '霸蛮', '漂亮': '标致', '小': '细',\r\n          '吃': '恰', '走': '走', '看': '望', '说': '讲'\r\n        },\r\n        '江苏': {\r\n          '很': '老', '好': '好', '厉害': '厉害', '漂亮': '好看', '小': '小',\r\n          '大': '大', '便宜': '便宜', '贵': '贵', '快': '快'\r\n        },\r\n        '浙江': {\r\n          '很': '老', '好': '好', '厉害': '厉害', '漂亮': '好看', '小': '小',\r\n          '吃': '吃', '走': '走', '看': '看', '说': '讲'\r\n        }\r\n      }\r\n\r\n      const regionDialect = dialects[region]\r\n      if (!regionDialect) return content\r\n\r\n      // 根据方言占比替换词汇\r\n      Object.keys(regionDialect).forEach(key => {\r\n        if (Math.random() < ratio / 100) {\r\n          const regex = new RegExp(key, 'g')\r\n          content = content.replace(regex, regionDialect[key])\r\n        }\r\n      })\r\n\r\n      return content\r\n    },\r\n\r\n    // 通用文案生成\r\n    generateGeneralContent(library, index, targetWordCount) {\r\n      const baseFragments = [\r\n        `🌟 ${library.shopDetails || '我们的店铺'}，为您带来独特的体验！`,\r\n        `💫 发现美好，从这里开始！${library.shopDetails || '我们的店铺'}，期待您的光临！`,\r\n        `✨ 品质生活，精彩每一天！来体验我们为您精心准备的服务吧！`\r\n      ]\r\n\r\n      let content = baseFragments[index % baseFragments.length]\r\n\r\n      // 根据目标字数扩展内容\r\n      while (content.length < targetWordCount - 30) {\r\n        content += `我们专注于${library.prompt || '为您提供优质服务'}，用心做好每一个细节。`\r\n        if (content.length > targetWordCount + 20) break\r\n      }\r\n\r\n      return this.createContentObject(library, index, content, '通用文案')\r\n    },\r\n\r\n    // 创建文案内容对象\r\n    createContentObject(library, index, content, type) {\r\n      const newContent = {\r\n        id: Date.now() + index,\r\n        contentId: Date.now() + index,\r\n        libraryId: library.libraryId || library.id,\r\n        content: content,\r\n        title: `AI生成-${type}-第${index}条`,\r\n        wordCount: content.length,\r\n        isAiGenerated: true,\r\n        status: 'active',\r\n        qualityScore: 85 + Math.floor(Math.random() * 15),\r\n        createTime: new Date().toLocaleString()\r\n      }\r\n\r\n      console.log(`生成第${index}条${type} (实际${content.length}字):`, newContent)\r\n      return newContent\r\n    },\r\n\r\n    // 保存文案库内容到localStorage\r\n    saveLibraryContentToStorage() {\r\n      try {\r\n        localStorage.setItem('libraryContentStorage', JSON.stringify(this.libraryContentStorage || {}))\r\n        console.log('文案库内容已保存到localStorage')\r\n      } catch (error) {\r\n        console.error('保存文案库内容失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从localStorage加载文案库内容\r\n    loadLibraryContentFromStorage() {\r\n      try {\r\n        const stored = localStorage.getItem('libraryContentStorage')\r\n        if (stored) {\r\n          this.libraryContentStorage = JSON.parse(stored)\r\n          console.log('从localStorage加载文案库内容:', this.libraryContentStorage)\r\n        } else {\r\n          this.libraryContentStorage = {}\r\n        }\r\n      } catch (error) {\r\n        console.error('加载文案库内容失败:', error)\r\n        this.libraryContentStorage = {}\r\n      }\r\n    },\r\n    // 新增文案到文案库\r\n    addToLibrary(library) {\r\n      this.currentLibrary = library\r\n      this.addCopywritingForm = {\r\n        useAI: true,\r\n        shopDetails: library.shopDetails || '',\r\n        prompt: library.prompt || '',\r\n        count: 5,\r\n        content: ''\r\n      }\r\n      this.addCopywritingDialogVisible = true\r\n    },\r\n\r\n    // 添加文案\r\n    addCopywriting() {\r\n      this.$refs.addCopywritingForm.validate((valid) => {\r\n        if (valid) {\r\n          this.adding = true\r\n\r\n          const contentData = {\r\n            libraryId: this.currentLibrary.libraryId,\r\n            useAi: this.addCopywritingForm.useAI,\r\n            shopDetails: this.addCopywritingForm.shopDetails,\r\n            prompt: this.addCopywritingForm.prompt,\r\n            count: this.addCopywritingForm.count,\r\n            content: this.addCopywritingForm.content\r\n          }\r\n\r\n          addContent(contentData).then(response => {\r\n            this.$message.success(this.addCopywritingForm.useAI ?\r\n              `成功生成${this.addCopywritingForm.count}条文案` : '文案添加成功')\r\n            this.addCopywritingDialogVisible = false\r\n            this.loadLibraryContents(this.currentLibrary.libraryId)\r\n\r\n            // 更新文案库的生成计数\r\n            this.currentLibrary.generatedCount += this.addCopywritingForm.useAI ?\r\n              this.addCopywritingForm.count : 1\r\n          }).catch(error => {\r\n            console.error('添加文案失败', error)\r\n            this.$message.error('添加文案失败：' + (error.msg || error.message))\r\n          }).finally(() => {\r\n            this.adding = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新生成文案库\r\n    regenerateLibrary(library) {\r\n      this.$confirm('确定要重新生成这个文案库吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        regenerateLibrary(library.libraryId).then(() => {\r\n          library.status = 'generating'\r\n          library.generatedCount = 0\r\n          this.$message.success('开始重新生成文案库')\r\n          this.monitorProgress(library.libraryId)\r\n        }).catch(error => {\r\n          console.error('重新生成失败', error)\r\n          this.$message.error('重新生成失败：' + (error.msg || error.message))\r\n        })\r\n      })\r\n    },\r\n\r\n    // 删除文案库\r\n    deleteLibrary(library) {\r\n      this.$confirm('确定要删除这个文案库吗？删除后无法恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delLibrary([library.libraryId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryList()\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 查看文案内容\r\n    viewContent(content) {\r\n      this.$alert(content.content, '文案内容', {\r\n        confirmButtonText: '关闭'\r\n      })\r\n    },\r\n\r\n    // 复制文案内容\r\n    copyContent(content) {\r\n      navigator.clipboard.writeText(content.content).then(() => {\r\n        this.$message.success('文案已复制到剪贴板')\r\n      }).catch(() => {\r\n        this.$message.error('复制失败，请手动复制')\r\n      })\r\n    },\r\n\r\n    // 编辑文案内容\r\n    editContent(content) {\r\n      this.$prompt('请编辑文案内容', '编辑文案', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputValue: content.content\r\n      }).then(({ value }) => {\r\n        const updateData = {\r\n          contentId: content.contentId,\r\n          content: value,\r\n          wordCount: value.length\r\n        }\r\n\r\n        updateContent(updateData).then(() => {\r\n          content.content = value\r\n          content.wordCount = value.length\r\n          this.$message.success('编辑成功')\r\n        }).catch(error => {\r\n          console.error('编辑失败', error)\r\n          this.$message.error('编辑失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消编辑')\r\n      })\r\n    },\r\n\r\n    // 删除文案内容\r\n    deleteContent(content) {\r\n      this.$confirm('确定要删除这条文案吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delContent([content.contentId]).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.loadLibraryContents(this.currentLibrary.libraryId)\r\n          this.currentLibrary.generatedCount--\r\n        }).catch(error => {\r\n          console.error('删除失败', error)\r\n          this.$message.error('删除失败：' + (error.msg || error.message))\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    // 导出文案库\r\n    exportLibrary(library) {\r\n      let content = `文案库：${library.name}\\n`\r\n      content += `创建时间：${library.createTime}\\n`\r\n      content += `总计：${this.libraryContents.length}条文案\\n\\n`\r\n\r\n      this.libraryContents.forEach((item, index) => {\r\n        content += `${index + 1}. ${item.content}\\n`\r\n        content += `   创建时间：${item.createTime}\\n\\n`\r\n      })\r\n\r\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `${library.name}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n\r\n      this.$message.success('文案库导出成功')\r\n    },\r\n    // 获取状态名称\r\n    getStatusName(status) {\r\n      const statusMap = {\r\n        pending: '未开始',\r\n        generating: '生成中',\r\n        completed: '已完成',\r\n        failed: '生成失败'\r\n      }\r\n      return statusMap[status] || status\r\n    },\r\n\r\n    // 获取状态颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        pending: 'info',\r\n        generating: 'warning',\r\n        completed: 'success',\r\n        failed: 'danger'\r\n      }\r\n      return colorMap[status] || ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.shipin-container {\r\n  padding: 24px;\r\n  background: #f5f5f5;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .header-content {\r\n    .page-title {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 8px 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .page-description {\r\n      color: #7f8c8d;\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.prompt-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #e6a23c;\r\n    }\r\n  }\r\n\r\n  .prompt-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: 16px;\r\n\r\n    .prompt-card {\r\n      padding: 20px;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .prompt-icon {\r\n        font-size: 32px;\r\n        margin-bottom: 12px;\r\n        text-align: center;\r\n      }\r\n\r\n      .prompt-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .prompt-desc {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .prompt-preview {\r\n        font-size: 12px;\r\n        color: #95a5a6;\r\n        line-height: 1.4;\r\n        background: #f8f9fa;\r\n        padding: 8px;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.template-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2c3e50;\r\n    margin: 0 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    i {\r\n      margin-right: 12px;\r\n      color: #409eff;\r\n    }\r\n  }\r\n\r\n  .template-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 16px;\r\n\r\n    .template-card {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .template-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .template-type {\r\n          background: #f0f0f0;\r\n          color: #666;\r\n          padding: 4px 12px;\r\n          border-radius: 16px;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .template-hot {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .template-title {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .template-preview {\r\n        font-size: 14px;\r\n        color: #7f8c8d;\r\n        line-height: 1.5;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .template-stats {\r\n        display: flex;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      i {\r\n        margin-right: 12px;\r\n        color: #409eff;\r\n      }\r\n    }\r\n\r\n    .section-filters {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .library-list {\r\n    .library-item {\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      margin-bottom: 16px;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n      }\r\n\r\n      .item-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        .item-title {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          i {\r\n            margin-right: 8px;\r\n            color: #409eff;\r\n          }\r\n        }\r\n\r\n        .item-meta {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 12px;\r\n\r\n          .item-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-content {\r\n        margin-bottom: 16px;\r\n\r\n        .library-info {\r\n          display: flex;\r\n          gap: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .info-item {\r\n            .label {\r\n              font-size: 12px;\r\n              color: #7f8c8d;\r\n            }\r\n\r\n            .value {\r\n              font-size: 14px;\r\n              color: #2c3e50;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n\r\n        .progress-info {\r\n          margin-bottom: 12px;\r\n\r\n          .progress-text {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n            margin-top: 8px;\r\n          }\r\n        }\r\n\r\n        .shop-info {\r\n          font-size: 14px;\r\n          color: #7f8c8d;\r\n\r\n          .label {\r\n            font-weight: 600;\r\n          }\r\n\r\n          .preview {\r\n            color: #95a5a6;\r\n          }\r\n        }\r\n      }\r\n\r\n      .item-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n        flex-wrap: wrap;\r\n      }\r\n    }\r\n\r\n    .empty-state {\r\n      text-align: center;\r\n      padding: 60px 20px;\r\n\r\n      i {\r\n        font-size: 64px;\r\n        color: #ddd;\r\n        margin-bottom: 16px;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 18px;\r\n        color: #7f8c8d;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      p {\r\n        color: #95a5a6;\r\n        margin: 0 0 20px 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.library-detail {\r\n  .detail-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #2c3e50;\r\n      margin: 0 0 12px 0;\r\n    }\r\n\r\n    .detail-meta {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      font-size: 14px;\r\n      color: #7f8c8d;\r\n    }\r\n  }\r\n\r\n  .detail-info {\r\n    margin-bottom: 24px;\r\n\r\n    .info-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\r\n      gap: 16px;\r\n      margin-bottom: 16px;\r\n\r\n      .info-item {\r\n        .label {\r\n          font-size: 12px;\r\n          color: #7f8c8d;\r\n          display: block;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #2c3e50;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n\r\n    .shop-details,\r\n    .prompt-info {\r\n      margin-bottom: 16px;\r\n\r\n      h4 {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0 0 8px 0;\r\n      }\r\n\r\n      .details-text,\r\n      .prompt-text {\r\n        line-height: 1.6;\r\n        color: #2c3e50;\r\n        background: #f8f9fa;\r\n        padding: 12px;\r\n        border-radius: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .copywriting-list {\r\n    .list-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      padding-bottom: 12px;\r\n      border-bottom: 1px solid #e9ecef;\r\n\r\n      h4 {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #2c3e50;\r\n        margin: 0;\r\n      }\r\n\r\n      .list-actions {\r\n        display: flex;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .content-list {\r\n      max-height: 400px;\r\n      overflow-y: auto;\r\n\r\n      .content-item {\r\n        border: 1px solid #e9ecef;\r\n        border-radius: 6px;\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #409eff;\r\n          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);\r\n        }\r\n\r\n        .content-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .content-index {\r\n            background: #409eff;\r\n            color: #fff;\r\n            padding: 2px 8px;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            min-width: 30px;\r\n            text-align: center;\r\n          }\r\n\r\n          .content-time {\r\n            font-size: 12px;\r\n            color: #7f8c8d;\r\n          }\r\n\r\n          .content-actions {\r\n            display: flex;\r\n            gap: 4px;\r\n          }\r\n        }\r\n\r\n        .content-text {\r\n          line-height: 1.6;\r\n          color: #2c3e50;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n\r\n      .empty-content {\r\n        text-align: center;\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          color: #ddd;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        p {\r\n          color: #7f8c8d;\r\n          margin: 0 0 16px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n  line-height: 1.4;\r\n}\r\n\r\n// 移动端优化样式\r\n@media (max-width: 768px) {\r\n  .shipin-container {\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    .header-content {\r\n      width: 100%;\r\n      margin-bottom: 12px;\r\n\r\n      .page-title {\r\n        font-size: 20px;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .page-description {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      width: 100%;\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .el-button {\r\n        flex: 1;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .prompt-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .prompt-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .prompt-card {\r\n        padding: 16px;\r\n\r\n        .prompt-icon {\r\n          font-size: 24px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-title {\r\n          font-size: 14px;\r\n          margin-bottom: 6px;\r\n        }\r\n\r\n        .prompt-desc {\r\n          font-size: 12px;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .prompt-preview {\r\n          font-size: 11px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .template-section {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .template-grid {\r\n      grid-template-columns: 1fr;\r\n      gap: 12px;\r\n\r\n      .template-card {\r\n        padding: 16px;\r\n\r\n        .template-title {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .template-preview {\r\n          font-size: 13px;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 2;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-section {\r\n    padding: 16px;\r\n\r\n    .section-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 16px;\r\n\r\n      h3 {\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .section-filters {\r\n        width: 100%;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n\r\n        .el-select {\r\n          width: 100% !important;\r\n        }\r\n\r\n        .el-input {\r\n          width: 100% !important;\r\n          margin-left: 0 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .library-list {\r\n      .library-item {\r\n        padding: 16px;\r\n        margin-bottom: 12px;\r\n\r\n        .item-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          margin-bottom: 12px;\r\n\r\n          .item-title {\r\n            font-size: 15px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .item-meta {\r\n            width: 100%;\r\n            flex-wrap: wrap;\r\n            gap: 8px;\r\n\r\n            .item-time {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-content {\r\n          .library-info {\r\n            flex-direction: column;\r\n            gap: 8px;\r\n\r\n            .info-item {\r\n              display: flex;\r\n              justify-content: space-between;\r\n\r\n              .label {\r\n                font-size: 12px;\r\n              }\r\n\r\n              .value {\r\n                font-size: 13px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .shop-info {\r\n            font-size: 13px;\r\n\r\n            .preview {\r\n              display: block;\r\n              margin-top: 4px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .item-actions {\r\n          gap: 6px;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n            font-size: 12px;\r\n            padding: 6px 8px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .empty-state {\r\n        padding: 40px 20px;\r\n\r\n        i {\r\n          font-size: 48px;\r\n        }\r\n\r\n        h3 {\r\n          font-size: 16px;\r\n        }\r\n\r\n        p {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 对话框移动端优化\r\n  .create-dialog,\r\n  .view-dialog {\r\n    .el-dialog__body {\r\n      padding: 16px;\r\n      max-height: calc(100vh - 120px);\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .el-form {\r\n      .el-form-item {\r\n        margin-bottom: 16px;\r\n\r\n        .el-form-item__label {\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-textarea {\r\n          font-size: 14px;\r\n        }\r\n\r\n        .el-checkbox-group {\r\n          .el-checkbox {\r\n            margin-bottom: 8px;\r\n            margin-right: 16px;\r\n\r\n            .el-checkbox__label {\r\n              font-size: 14px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-dialog__footer {\r\n      padding: 12px 16px;\r\n\r\n      .el-button {\r\n        margin-left: 8px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-header {\r\n      h3 {\r\n        font-size: 18px;\r\n      }\r\n\r\n      .detail-meta {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n      }\r\n    }\r\n\r\n    .detail-info {\r\n      .info-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 12px;\r\n\r\n        .info-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 8px 12px;\r\n          background: #f8f9fa;\r\n          border-radius: 4px;\r\n\r\n          .label {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .value {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .shop-details,\r\n      .prompt-info {\r\n        .details-text,\r\n        .prompt-text {\r\n          font-size: 13px;\r\n          padding: 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .list-header {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 12px;\r\n\r\n        h4 {\r\n          font-size: 15px;\r\n        }\r\n\r\n        .list-actions {\r\n          width: 100%;\r\n\r\n          .el-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n\r\n      .content-list {\r\n        max-height: 300px;\r\n\r\n        .content-item {\r\n          padding: 12px;\r\n\r\n          .content-header {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n\r\n            .content-actions {\r\n              width: 100%;\r\n              justify-content: space-between;\r\n\r\n              .el-button {\r\n                flex: 1;\r\n                margin: 0 2px;\r\n                font-size: 11px;\r\n                padding: 4px 6px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .content-text {\r\n            font-size: 13px;\r\n            line-height: 1.5;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 超小屏幕优化 (小于480px)\r\n@media (max-width: 480px) {\r\n  .shipin-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .prompt-section {\r\n    .prompt-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  .library-detail {\r\n    .detail-info {\r\n      .info-grid {\r\n        .info-item {\r\n          padding: 6px 10px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .copywriting-list {\r\n      .content-list {\r\n        .content-item {\r\n          padding: 10px;\r\n\r\n          .content-header {\r\n            .content-actions {\r\n              .el-button {\r\n                font-size: 10px;\r\n                padding: 3px 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 提示词帮助对话框样式\r\n::v-deep .prompt-help-dialog {\r\n  .el-message-box {\r\n    width: 600px;\r\n    max-width: 90vw;\r\n  }\r\n\r\n  .el-message-box__content {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  h4, h5 {\r\n    color: #409EFF;\r\n    margin: 15px 0 10px 0;\r\n  }\r\n\r\n  p {\r\n    margin: 8px 0;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  strong {\r\n    color: #303133;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA,IAAAA,YAAA,GAAAC,OAAA;AAgBA,IAAAC,gBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAQA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,0BAAA;MACAC,2BAAA;MACAC,wBAAA;MAEA;MACAC,QAAA;MACAC,MAAA;MAEA;MACAC,YAAA;MACAC,aAAA;MAEA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;MAEA;MACAC,iBAAA;QACAZ,IAAA;QACAa,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,kBAAA;QACAlB,IAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,kBAAA;QACAX,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAS,OAAA;MACA;MACAC,mBAAA;QACAZ,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,OAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAM,gBAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,GACA;QACAG,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAN,OAAA;MACA,EACA;MAEA;MACAO,WAAA,GACA;QACAJ,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA,GACA;QACAR,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA,GACA;QACAR,EAAA;QACA5B,IAAA;QACAa,KAAA;QACAoB,MAAA;QACAC,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAH,WAAA;QACAC,MAAA;QACAqB,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,QAAAR,WAAA;;MAEA;MACA,SAAAzB,YAAA;QACAiC,IAAA,GAAAA,IAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAT,MAAA,KAAAM,KAAA,CAAAhC,YAAA;QAAA;MACA;;MAEA;MACA,SAAAC,aAAA;QACA,IAAAmC,OAAA,QAAAnC,aAAA,CAAAoC,WAAA;QACAJ,IAAA,GAAAA,IAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA1C,IAAA,CAAA4C,WAAA,GAAAC,QAAA,CAAAF,OAAA,KACAD,IAAA,CAAA5B,WAAA,IAAA4B,IAAA,CAAA5B,WAAA,CAAA8B,WAAA,GAAAC,QAAA,CAAAF,OAAA;QAAA,CACA;MACA;MAEA,OAAAH,IAAA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,6BAAA;IAEA,KAAAC,eAAA;;IAEA;IACAC,UAAA;MACA,IAAAH,MAAA,CAAAf,WAAA,CAAAmB,MAAA;QACAC,OAAA,CAAAC,GAAA;QACAN,MAAA,CAAAO,mBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAF,WAAA;EACA;EACAG,aAAA,WAAAA,cAAA;IACAF,MAAA,CAAAG,mBAAA,gBAAAJ,WAAA;EACA;EACAK,OAAA;IACAL,WAAA,WAAAA,YAAA;MACA,KAAA7C,QAAA,GAAA8C,MAAA,CAAAK,UAAA;IACA;IAGAb,eAAA,WAAAA,gBAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,wBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA/B,WAAA,GAAAkC,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAjE,IAAA;QACA,IAAA8D,MAAA,CAAA/B,WAAA,CAAAmB,MAAA;UACA;UACAY,MAAA,CAAAT,mBAAA;QACA;MACA,GAAAc,KAAA,WAAAC,KAAA;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACAnB,OAAA,CAAAiB,KAAA,qBAAAA,KAAA;;QAEA;QACA,IAAAA,KAAA,CAAAG,IAAA,aAAAF,cAAA,GAAAD,KAAA,CAAAjD,OAAA,cAAAkD,cAAA,eAAAA,cAAA,CAAAzB,QAAA,WAAA0B,eAAA,GAAAF,KAAA,CAAAjD,OAAA,cAAAmD,eAAA,eAAAA,eAAA,CAAA1B,QAAA;UACAkB,MAAA,CAAAU,QAAA,CAAAC,OAAA;QACA;;QAEA;QACAX,MAAA,CAAAT,mBAAA;MACA;IACA;IAEA;IACAA,mBAAA,WAAAA,oBAAA;MACAF,OAAA,CAAAC,GAAA;MAEA,IAAAsB,aAAA,IACA;QACA/C,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,GACA;QACAnD,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,GACA;QACAnD,EAAA;QACAgD,SAAA;QACA5E,IAAA;QACA6E,WAAA;QACAhE,KAAA;QACAiE,KAAA;QACAhE,WAAA;QACAC,MAAA;QACAmB,WAAA;QACAC,cAAA;QACAlB,SAAA;QACAgB,MAAA;QACAG,UAAA;QACA2C,QAAA;MACA,EACA;;MAEA;MACA,IAAAC,aAAA,QAAAhD,WAAA,CAAAS,MAAA,WAAAwC,GAAA;QAAA,OAAAA,GAAA,CAAAF,QAAA;MAAA;MAEA,KAAA/C,WAAA,MAAAkD,MAAA,CAAAP,aAAA,MAAAQ,mBAAA,CAAApF,OAAA,EAAAiF,aAAA;MACA,KAAAP,QAAA,CAAAW,OAAA,uBAAApD,WAAA,CAAAmB,MAAA;IACA;IACAkC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACAlC,OAAA,CAAAC,GAAA;MACA,KAAAJ,eAAA;;MAEA;MACAC,UAAA;QACA,IAAAoC,MAAA,CAAAtD,WAAA,CAAAmB,MAAA;UACAC,OAAA,CAAAC,GAAA;UACAiC,MAAA,CAAAhC,mBAAA;QACA;UACAgC,MAAA,CAAAb,QAAA,CAAAW,OAAA;QACA;MACA;IACA;IAEA;IACAG,uBAAA,WAAAA,wBAAA;MACA,KAAArF,0BAAA;MACA,KAAAU,iBAAA;QACAZ,IAAA;QACAa,KAAA;QACAC,WAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;IACA;IAEA;IACAuE,SAAA,WAAAA,UAAAzE,MAAA;MACA,KAAAH,iBAAA,CAAAG,MAAA,GAAAA,MAAA,CAAAU,OAAA;MACA,KAAAvB,0BAAA;MACA,KAAAuE,QAAA,CAAAW,OAAA,sBAAAF,MAAA,CAAAnE,MAAA,CAAAe,KAAA;IACA;IAEA;IACA2D,cAAA,WAAAA,eAAA;MACA,KAAAC,MAAA,60HAmBA;QACAC,wBAAA;QACAC,iBAAA;QACAC,WAAA;MACA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAApF,iBAAA,CAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA1F,QAAA;UAEA,IAAA8F,WAAA;YACAtB,WAAA,EAAAkB,MAAA,CAAAnF,iBAAA,CAAAZ,IAAA;YACA8E,KAAA,EAAAiB,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;YACAC,WAAA,EAAAiF,MAAA,CAAAnF,iBAAA,CAAAE,WAAA;YACAC,MAAA,EAAAgF,MAAA,CAAAnF,iBAAA,CAAAG,MAAA;YACAmB,WAAA,EAAA6D,MAAA,CAAAnF,iBAAA,CAAAC,KAAA,GAAAkF,MAAA,CAAAnF,iBAAA,CAAAI,KAAA;YACAC,SAAA,EAAAmF,QAAA,CAAAL,MAAA,CAAAnF,iBAAA,CAAAK,SAAA;UACA;UAEA,IAAAoF,uBAAA,EAAAF,WAAA,EAAAlC,IAAA,WAAAC,QAAA;YACA6B,MAAA,CAAAtB,QAAA,CAAAW,OAAA;YACAW,MAAA,CAAA7F,0BAAA;YACA6F,MAAA,CAAA9C,eAAA;;YAEA;YACA,IAAA8C,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;cACAkF,MAAA,CAAAO,eAAA,CAAApC,QAAA,CAAAjE,IAAA,CAAA2E,SAAA;YACA;UACA,GAAAR,KAAA,WAAAC,KAAA;YACAjB,OAAA,CAAAiB,KAAA,sBAAAA,KAAA;;YAEA;YACA0B,MAAA,CAAAtB,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAA6B,WAAA;cACA3E,EAAA,EAAA4E,IAAA,CAAAC,GAAA;cACA7B,SAAA,EAAA4B,IAAA,CAAAC,GAAA;cACAzG,IAAA,EAAAmG,WAAA,CAAAtB,WAAA;cACAA,WAAA,EAAAsB,WAAA,CAAAtB,WAAA;cACAhE,KAAA,EAAAsF,WAAA,CAAArB,KAAA;cACAA,KAAA,EAAAqB,WAAA,CAAArB,KAAA;cACAhE,WAAA,EAAAqF,WAAA,CAAArF,WAAA;cACAC,MAAA,EAAAoF,WAAA,CAAApF,MAAA;cACAmB,WAAA,EAAAiE,WAAA,CAAAjE,WAAA;cACAC,cAAA;cACAlB,SAAA,EAAAkF,WAAA,CAAAlF,SAAA;cACAgB,MAAA;cACAG,UAAA,MAAAoE,IAAA,GAAAE,cAAA;cACA3B,QAAA;YACA;;YAEA;YACAgB,MAAA,CAAA/D,WAAA,CAAA2E,OAAA,CAAAJ,WAAA;YAEAR,MAAA,CAAAtB,QAAA,CAAAW,OAAA;YACAW,MAAA,CAAA7F,0BAAA;;YAEA;YACA,IAAA6F,MAAA,CAAAnF,iBAAA,CAAAC,KAAA;cACAkF,MAAA,CAAAtB,QAAA,CAAAmC,IAAA;;cAEA;cACAb,MAAA,CAAAc,yBAAA,CAAAN,WAAA,CAAA3B,SAAA;YACA;YAEAmB,MAAA,CAAA1F,QAAA;UACA;QACA;MACA;IACA;IAEA;IACAiG,eAAA,WAAAA,gBAAA1B,SAAA;MAAA,IAAAkC,MAAA;MACA,IAAAC,OAAA,QAAA/E,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;QAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA;MAAA;MACA,IAAAmC,OAAA;QACA,IAAAE,gCAAA;UACArC,SAAA,EAAAA,SAAA;UACA9D,WAAA,EAAAiG,OAAA,CAAAjG,WAAA;UACAC,MAAA,EAAAgG,OAAA,CAAAhG,MAAA;UACAC,KAAA,EAAA+F,OAAA,CAAA7E,WAAA;UACAjB,SAAA,EAAA8F,OAAA,CAAA9F;QACA,GAAAgD,IAAA;UACA6C,MAAA,CAAArC,QAAA,CAAAW,OAAA;UACA0B,MAAA,CAAAI,eAAA,CAAAtC,SAAA;QACA,GAAAR,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,aAAAA,KAAA;UACAyC,MAAA,CAAArC,QAAA,CAAAJ,KAAA,gBAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA;IACA;IAEA;IACA8F,eAAA,WAAAA,gBAAAtC,SAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,cAAA,YAAAA,cAAA;QACA,IAAAC,wBAAA,EAAA1C,SAAA,EAAAX,IAAA,WAAAC,QAAA;UACA,IAAAqD,QAAA,GAAArD,QAAA,CAAAjE,IAAA;UACA,IAAA8G,OAAA,GAAAK,MAAA,CAAApF,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;YAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA;UAAA;UACA,IAAAmC,OAAA;YACAA,OAAA,CAAA5E,cAAA,GAAAoF,QAAA,CAAApF,cAAA;YACA4E,OAAA,CAAA9E,MAAA,GAAAsF,QAAA,CAAAtF,MAAA;YAEA,IAAAsF,QAAA,CAAAtF,MAAA;cACAiB,UAAA,CAAAmE,cAAA;YACA,WAAAE,QAAA,CAAAtF,MAAA;cACAmF,MAAA,CAAA3C,QAAA,CAAAW,OAAA,IAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA;YACA,WAAA0C,QAAA,CAAAtF,MAAA;cACAmF,MAAA,CAAA3C,QAAA,CAAAJ,KAAA,IAAAa,MAAA,CAAA6B,OAAA,CAAAlC,WAAA;YACA;UACA;QACA,GAAAT,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;QACA;MACA;MACAgD,cAAA;IACA;IAIA;IACAG,WAAA,WAAAA,YAAAT,OAAA;MACA,KAAAtG,cAAA,GAAAsG,OAAA;MACA,KAAAU,mBAAA,CAAAV,OAAA,CAAAnF,EAAA;MACA,KAAAxB,wBAAA;IACA;IAEA;IACAqH,mBAAA,WAAAA,oBAAA7C,SAAA;MAAA,IAAA8C,MAAA;MACA;MACA,SAAAC,qBAAA,SAAAA,qBAAA,CAAA/C,SAAA;QACA,KAAAlE,eAAA,QAAAiH,qBAAA,CAAA/C,SAAA;QACA,KAAAH,QAAA,CAAAW,OAAA,sBAAAF,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;QACAC,OAAA,CAAAC,GAAA,uBAAA3C,eAAA;QACA;MACA;;MAEA;MACA,IAAAkH,wBAAA,EAAAhD,SAAA,EAAAX,IAAA,WAAAC,QAAA;QACAwD,MAAA,CAAAhH,eAAA,GAAAwD,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAjE,IAAA;QACA,IAAAyH,MAAA,CAAAhH,eAAA,CAAAyC,MAAA;UACA;UACAuE,MAAA,CAAAG,gBAAA,CAAAjD,SAAA;QACA;MACA,GAAAR,KAAA,WAAAC,KAAA;QAAA,IAAAyD,eAAA,EAAAC,eAAA;QACA3E,OAAA,CAAAiB,KAAA,qBAAAA,KAAA;;QAEA;QACA,IAAAA,KAAA,CAAAG,IAAA,aAAAsD,eAAA,GAAAzD,KAAA,CAAAjD,OAAA,cAAA0G,eAAA,eAAAA,eAAA,CAAAjF,QAAA,WAAAkF,eAAA,GAAA1D,KAAA,CAAAjD,OAAA,cAAA2G,eAAA,eAAAA,eAAA,CAAAlF,QAAA;UACA6E,MAAA,CAAAjD,QAAA,CAAAC,OAAA;QACA;;QAEA;QACAgD,MAAA,CAAAG,gBAAA,CAAAjD,SAAA;MACA;IACA;IAEA;IACAiD,gBAAA,WAAAA,iBAAAjD,SAAA;MACAxB,OAAA,CAAAC,GAAA,wBAAAuB,SAAA;MAEA,IAAAoD,YAAA;QACA;QAAA;QACA;UACApG,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,GACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,EACA;QACA;QAAA;QACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,EACA;QACA;QAAA;QACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA,GACA;UACAR,EAAA;UACAqG,SAAA;UACArD,SAAA;UACAnD,OAAA;UACAK,KAAA;UACAb,SAAA;UACAiH,aAAA;UACAjG,MAAA;UACAkG,YAAA;UACA/F,UAAA;QACA;MAEA;MAEA,KAAA1B,eAAA,GAAAsH,YAAA,CAAApD,SAAA;MACA,KAAAH,QAAA,CAAAW,OAAA,sBAAAF,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;IACA;IAEA;IACA0D,yBAAA,WAAAA,0BAAAjC,SAAA;MAAA,IAAAwD,MAAA;MACAhF,OAAA,CAAAC,GAAA,wBAAAuB,SAAA;;MAEA;MACA,IAAAmC,OAAA,QAAA/E,WAAA,CAAAgF,IAAA,WAAA/B,GAAA;QAAA,OAAAA,GAAA,CAAAL,SAAA,KAAAA,SAAA,IAAAK,GAAA,CAAArD,EAAA,KAAAgD,SAAA;MAAA;MACA,KAAAmC,OAAA;QACA3D,OAAA,CAAAC,GAAA;QACA;MACA;;MAEA;MACA,UAAAsE,qBAAA;QACA,KAAAA,qBAAA;MACA;MACA,UAAAA,qBAAA,CAAA/C,SAAA;QACA,KAAA+C,qBAAA,CAAA/C,SAAA;MACA;MAEAmC,OAAA,CAAA9E,MAAA;;MAEA;MACA,IAAAoG,kBAAA,YAAAA,mBAAA;QAAA,IAAAC,KAAA,YAAAA,MAAAC,CAAA,EACA;UACArF,UAAA;YACA;YACA,IAAAsF,UAAA,GAAAJ,MAAA,CAAAK,mBAAA,CAAA1B,OAAA,EAAAwB,CAAA;;YAEA;YACAH,MAAA,CAAAT,qBAAA,CAAA/C,SAAA,EAAA8D,IAAA,CAAAF,UAAA;;YAEA;YACAzB,OAAA,CAAA5E,cAAA,GAAAoG,CAAA;YAEAH,MAAA,CAAA3D,QAAA,CAAAW,OAAA,wBAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA,IAAAkC,OAAA,CAAA/G,IAAA,gCAAAkF,MAAA,CAAAqD,CAAA;;YAEA;YACA,IAAAA,CAAA,KAAAxB,OAAA,CAAA7E,WAAA;cACA6E,OAAA,CAAA9E,MAAA;cACAmG,MAAA,CAAA3D,QAAA,CAAAW,OAAA,qCAAAF,MAAA,CAAA6B,OAAA,CAAAlC,WAAA,IAAAkC,OAAA,CAAA/G,IAAA,wDAAAkF,MAAA,CAAA6B,OAAA,CAAA5E,cAAA;;cAEA;cACAiG,MAAA,CAAAO,2BAAA;YACA;UACA,GAAAJ,CAAA;QACA;QAtBA,SAAAA,CAAA,MAAAA,CAAA,IAAAxB,OAAA,CAAA7E,WAAA,EAAAqG,CAAA;UAAAD,KAAA,CAAAC,CAAA;QAAA;MAuBA;;MAEA;MACArF,UAAA,CAAAmF,kBAAA;IACA;IAEA;IACAI,mBAAA,WAAAA,oBAAA1B,OAAA,EAAA6B,KAAA;MACA,IAAAC,eAAA,GAAA9B,OAAA,CAAA9F,SAAA;;MAEA;MACA,YAAA6H,yBAAA,CAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;IACA;IAEA;IACAE,+BAAA,WAAAA,gCAAAC,QAAA,EAAAjC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,QAAAG,QAAA;QACA;UAAA;UACA,YAAAC,oBAAA,CAAAlC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;QACA;UACA,YAAAC,yBAAA,CAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;UAAA;UACA,YAAAK,0BAAA,CAAAnC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;QACA;UACA,YAAAM,qBAAA,CAAApC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;QACA;UAAA;UACA,YAAAO,sBAAA,CAAArC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA;IACA;IAEA;IACA;IACAI,oBAAA,WAAAA,qBAAAlC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA;MACA,IAAA9H,MAAA,GAAAgG,OAAA,CAAAhG,MAAA;MACA,IAAAD,WAAA,GAAAiG,OAAA,CAAAjG,WAAA;;MAEA;MACA,IAAAuI,cAAA,QAAAC,mBAAA,CAAAvI,MAAA;;MAEA;MACA,IAAAU,OAAA,QAAA8H,4BAAA,CAAAF,cAAA,EAAAvI,WAAA,EAAA+H,eAAA;;MAEA;MACApH,OAAA,QAAA+H,gBAAA,CAAA/H,OAAA,EAAAoH,eAAA;MAEA,YAAAY,mBAAA,CAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACA6H,mBAAA,WAAAA,oBAAAvI,MAAA;MACA,IAAA2I,MAAA;QACAC,IAAA;QAAA;QACAC,KAAA;QAAA;QACAC,KAAA;MACA;;MAEA;MACA,IAAA9I,MAAA,CAAA8B,QAAA,YAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAC,IAAA;MACA,WAAA5I,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAC,IAAA;MACA;;MAEA;MACA,IAAA5I,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAE,KAAA;MACA,WAAA7I,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAE,KAAA;MACA,WAAA7I,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAE,KAAA;MACA,WAAA7I,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA,UAAA9B,MAAA,CAAA8B,QAAA;QACA6G,MAAA,CAAAE,KAAA;MACA;MAEA,OAAAF,MAAA;IACA;IAEA;IACAH,4BAAA,WAAAA,6BAAAG,MAAA,EAAA5I,WAAA,EAAA+H,eAAA;MACA,IAAApH,OAAA;;MAEA;MACA,IAAAqI,gBAAA,IACA,qCACA;MACA,IAAAC,OAAA,GAAAD,gBAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAJ,gBAAA,CAAA3G,MAAA;;MAEA;MACA,IAAAuG,MAAA,CAAAC,IAAA;QACAlI,OAAA,MAAAyD,MAAA,CAAA6E,OAAA,YAAA7E,MAAA,CAAApE,WAAA;MACA,WAAA4I,MAAA,CAAAC,IAAA;QACAlI,OAAA,MAAAyD,MAAA,CAAA6E,OAAA,YAAA7E,MAAA,CAAApE,WAAA;MACA;QACAW,OAAA,MAAAyD,MAAA,CAAA6E,OAAA,YAAA7E,MAAA,CAAApE,WAAA;MACA;;MAEA;MACA,IAAA4I,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA;;MAEA;MACAA,OAAA;MAEA,OAAAA,OAAA;IACA;IAEA;IACA+H,gBAAA,WAAAA,iBAAA/H,OAAA,EAAAoH,eAAA;MACA,IAAAsB,MAAA,GAAA/D,QAAA,CAAAyC,eAAA;MAEA,IAAApH,OAAA,CAAA0B,MAAA,GAAAgH,MAAA;QACA;QACA,IAAAC,SAAA,GAAA3I,OAAA,CAAA4I,SAAA,IAAAF,MAAA;QACA;QACA,IAAAG,eAAA,GAAAN,IAAA,CAAAzI,GAAA,CACA6I,SAAA,CAAAG,WAAA,OACAH,SAAA,CAAAG,WAAA,OACAH,SAAA,CAAAG,WAAA,OACAH,SAAA,CAAAG,WAAA,KACA;QACA,IAAAD,eAAA,GAAAH,MAAA;UACAC,SAAA,GAAAA,SAAA,CAAAC,SAAA,IAAAC,eAAA;QACA;UACAF,SAAA;QACA;QACA,OAAAA,SAAA;MACA,WAAA3I,OAAA,CAAA0B,MAAA,GAAAgH,MAAA;QACA;QACA,IAAAK,UAAA,IACA,aACA,gBACA,cACA;QACA/I,OAAA,IAAA+I,UAAA,CAAAR,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAM,UAAA,CAAArH,MAAA;MACA;MAEA,OAAA1B,OAAA;IACA;IAEA;IACAqH,yBAAA,WAAAA,0BAAA/B,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAA9H,MAAA,GAAAgG,OAAA,CAAAhG,MAAA;MACA,IAAAD,WAAA,GAAAiG,OAAA,CAAAjG,WAAA;;MAEA;MACA,IAAAuI,cAAA,QAAAC,mBAAA,CAAAvI,MAAA;;MAEA;MACA,IAAAU,OAAA,QAAAgJ,6BAAA,CAAApB,cAAA,EAAAvI,WAAA;;MAEA;MACAW,OAAA,QAAA+H,gBAAA,CAAA/H,OAAA,EAAAoH,eAAA;MAEA,YAAAY,mBAAA,CAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAgJ,6BAAA,WAAAA,8BAAAf,MAAA,EAAA5I,WAAA;MACA,IAAAW,OAAA;;MAEA;MACA,IAAAiI,MAAA,CAAAC,IAAA;QACAlI,OAAA,MAAAyD,MAAA,CAAApE,WAAA;MACA,WAAA4I,MAAA,CAAAC,IAAA;QACAlI,OAAA,MAAAyD,MAAA,CAAApE,WAAA;MACA;QACAW,OAAA,MAAAyD,MAAA,CAAApE,WAAA;MACA;;MAEA;MACA,IAAA4I,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA;QACAA,OAAA;MACA;MAEA,OAAAA,OAAA;IACA;IAEA;IACAyH,0BAAA,WAAAA,2BAAAnC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAA9H,MAAA,GAAAgG,OAAA,CAAAhG,MAAA;MACA,IAAAD,WAAA,GAAAiG,OAAA,CAAAjG,WAAA;;MAEA;MACA,IAAAuI,cAAA,QAAAC,mBAAA,CAAAvI,MAAA;;MAEA;MACA,IAAAU,OAAA,QAAAiJ,kCAAA,CAAArB,cAAA,EAAAvI,WAAA;;MAEA;MACAW,OAAA,QAAA+H,gBAAA,CAAA/H,OAAA,EAAAoH,eAAA;MAEA,YAAAY,mBAAA,CAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAiJ,kCAAA,WAAAA,mCAAAhB,MAAA,EAAA5I,WAAA;MACA,IAAAW,OAAA;;MAEA;MACA,IAAAiI,MAAA,CAAAC,IAAA;QACAlI,OAAA,8BAAAyD,MAAA,CAAApE,WAAA;MACA,WAAA4I,MAAA,CAAAC,IAAA;QACAlI,OAAA,8BAAAyD,MAAA,CAAApE,WAAA;MACA;QACAW,OAAA,8BAAAyD,MAAA,CAAApE,WAAA;MACA;;MAEA;MACA,IAAA4I,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA;QACAA,OAAA;MACA;MAEAA,OAAA;MAEA,OAAAA,OAAA;IACA;IAEA;IACA0H,qBAAA,WAAAA,sBAAApC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MAAA,IAAA8B,MAAA,GAAAC,SAAA,CAAAzH,MAAA,QAAAyH,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAAA,IAAAE,YAAA,GAAAF,SAAA,CAAAzH,MAAA,QAAAyH,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAA7J,MAAA,GAAAgG,OAAA,CAAAhG,MAAA;MACA,IAAAD,WAAA,GAAAiG,OAAA,CAAAjG,WAAA;;MAEA;MACA,IAAAuI,cAAA,QAAAC,mBAAA,CAAAvI,MAAA;;MAEA;MACA,IAAAU,OAAA,QAAAsJ,6BAAA,CAAA1B,cAAA,EAAAvI,WAAA;;MAEA;MACA,IAAA6J,MAAA,IAAAG,YAAA;QACArJ,OAAA,QAAAuJ,mBAAA,CAAAvJ,OAAA,EAAAkJ,MAAA,EAAAG,YAAA;MACA;;MAEA;MACArJ,OAAA,QAAAwJ,QAAA,CAAAxJ,OAAA;;MAEA;MACAA,OAAA,QAAA+H,gBAAA,CAAA/H,OAAA,EAAAoH,eAAA;MAEA,YAAAY,mBAAA,CAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAsJ,6BAAA,WAAAA,8BAAArB,MAAA,EAAA5I,WAAA;MACA,IAAAW,OAAA;;MAEA;MACA,IAAAiI,MAAA,CAAAC,IAAA;QACAlI,OAAA,8BAAAyD,MAAA,CAAApE,WAAA;MACA,WAAA4I,MAAA,CAAAC,IAAA;QACAlI,OAAA,wBAAAyD,MAAA,CAAApE,WAAA;MACA;QACAW,OAAA,kBAAAyD,MAAA,CAAApE,WAAA;MACA;;MAEA;MACA,IAAA4I,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA,WAAAiI,MAAA,CAAAE,KAAA;QACAnI,OAAA;MACA;QACAA,OAAA;MACA;MAEAA,OAAA;MAEA,OAAAA,OAAA;IACA;IAEA;IACAwJ,QAAA,WAAAA,SAAAxJ,OAAA;MACA,IAAAyJ,KAAA;QACA;QAAA;QAAA;QAAA;QAAA;MACA;MAEAC,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAG,OAAA,WAAAC,GAAA;QACA,IAAAtB,IAAA,CAAAE,MAAA;UAAA;UACAzI,OAAA,GAAAA,OAAA,CAAA8J,OAAA,KAAAC,MAAA,CAAAF,GAAA,QAAAJ,KAAA,CAAAI,GAAA;QACA;MACA;MAEA,OAAA7J,OAAA;IACA;IAEA;IACAuJ,mBAAA,WAAAA,oBAAAvJ,OAAA,EAAAkJ,MAAA,EAAAc,KAAA;MACA;MACA,IAAAC,QAAA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UACA;UAAA;UAAA;UAAA;QACA;MACA;MAEA,IAAAC,aAAA,GAAAD,QAAA,CAAAf,MAAA;MACA,KAAAgB,aAAA,SAAAlK,OAAA;;MAEA;MACA0J,MAAA,CAAAC,IAAA,CAAAO,aAAA,EAAAN,OAAA,WAAAC,GAAA;QACA,IAAAtB,IAAA,CAAAE,MAAA,KAAAuB,KAAA;UACA,IAAAG,KAAA,OAAAJ,MAAA,CAAAF,GAAA;UACA7J,OAAA,GAAAA,OAAA,CAAA8J,OAAA,CAAAK,KAAA,EAAAD,aAAA,CAAAL,GAAA;QACA;MACA;MAEA,OAAA7J,OAAA;IACA;IAEA;IACA2H,sBAAA,WAAAA,uBAAArC,OAAA,EAAA6B,KAAA,EAAAC,eAAA;MACA,IAAAgD,aAAA,oBAAA3G,MAAA,CACA6B,OAAA,CAAAjG,WAAA,sKAAAoE,MAAA,CACA6B,OAAA,CAAAjG,WAAA,8OAEA;MAEA,IAAAW,OAAA,GAAAoK,aAAA,CAAAjD,KAAA,GAAAiD,aAAA,CAAA1I,MAAA;;MAEA;MACA,OAAA1B,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;QACApH,OAAA,qCAAAyD,MAAA,CAAA6B,OAAA,CAAAhG,MAAA;QACA,IAAAU,OAAA,CAAA0B,MAAA,GAAA0F,eAAA;MACA;MAEA,YAAAY,mBAAA,CAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA;IACA;IAEA;IACAgI,mBAAA,WAAAA,oBAAA1C,OAAA,EAAA6B,KAAA,EAAAnH,OAAA,EAAAqK,IAAA;MACA,IAAAtD,UAAA;QACA5G,EAAA,EAAA4E,IAAA,CAAAC,GAAA,KAAAmC,KAAA;QACAX,SAAA,EAAAzB,IAAA,CAAAC,GAAA,KAAAmC,KAAA;QACAhE,SAAA,EAAAmC,OAAA,CAAAnC,SAAA,IAAAmC,OAAA,CAAAnF,EAAA;QACAH,OAAA,EAAAA,OAAA;QACAK,KAAA,oBAAAoD,MAAA,CAAA4G,IAAA,aAAA5G,MAAA,CAAA0D,KAAA;QACA3H,SAAA,EAAAQ,OAAA,CAAA0B,MAAA;QACA+E,aAAA;QACAjG,MAAA;QACAkG,YAAA,OAAA6B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA9H,UAAA,MAAAoE,IAAA,GAAAE,cAAA;MACA;MAEAtD,OAAA,CAAAC,GAAA,sBAAA6B,MAAA,CAAA0D,KAAA,YAAA1D,MAAA,CAAA4G,IAAA,oBAAA5G,MAAA,CAAAzD,OAAA,CAAA0B,MAAA,eAAAqF,UAAA;MACA,OAAAA,UAAA;IACA;IAEA;IACAG,2BAAA,WAAAA,4BAAA;MACA;QACAoD,YAAA,CAAAC,OAAA,0BAAAC,IAAA,CAAAC,SAAA,MAAAvE,qBAAA;QACAvE,OAAA,CAAAC,GAAA;MACA,SAAAgB,KAAA;QACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;MACA;IACA;IAEA;IACArB,6BAAA,WAAAA,8BAAA;MACA;QACA,IAAAmJ,MAAA,GAAAJ,YAAA,CAAAK,OAAA;QACA,IAAAD,MAAA;UACA,KAAAxE,qBAAA,GAAAsE,IAAA,CAAAI,KAAA,CAAAF,MAAA;UACA/I,OAAA,CAAAC,GAAA,+BAAAsE,qBAAA;QACA;UACA,KAAAA,qBAAA;QACA;MACA,SAAAtD,KAAA;QACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;QACA,KAAAsD,qBAAA;MACA;IACA;IACA;IACA2E,YAAA,WAAAA,aAAAvF,OAAA;MACA,KAAAtG,cAAA,GAAAsG,OAAA;MACA,KAAAvF,kBAAA;QACAX,KAAA;QACAC,WAAA,EAAAiG,OAAA,CAAAjG,WAAA;QACAC,MAAA,EAAAgG,OAAA,CAAAhG,MAAA;QACAC,KAAA;QACAS,OAAA;MACA;MACA,KAAAtB,2BAAA;IACA;IAEA;IACAoM,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAxG,KAAA,CAAAxE,kBAAA,CAAAyE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAsG,MAAA,CAAAlM,MAAA;UAEA,IAAAmM,WAAA;YACA7H,SAAA,EAAA4H,MAAA,CAAA/L,cAAA,CAAAmE,SAAA;YACAE,KAAA,EAAA0H,MAAA,CAAAhL,kBAAA,CAAAX,KAAA;YACAC,WAAA,EAAA0L,MAAA,CAAAhL,kBAAA,CAAAV,WAAA;YACAC,MAAA,EAAAyL,MAAA,CAAAhL,kBAAA,CAAAT,MAAA;YACAC,KAAA,EAAAwL,MAAA,CAAAhL,kBAAA,CAAAR,KAAA;YACAS,OAAA,EAAA+K,MAAA,CAAAhL,kBAAA,CAAAC;UACA;UAEA,IAAAiL,uBAAA,EAAAD,WAAA,EAAAxI,IAAA,WAAAC,QAAA;YACAsI,MAAA,CAAA/H,QAAA,CAAAW,OAAA,CAAAoH,MAAA,CAAAhL,kBAAA,CAAAX,KAAA,8BAAAqE,MAAA,CACAsH,MAAA,CAAAhL,kBAAA,CAAAR,KAAA;YACAwL,MAAA,CAAArM,2BAAA;YACAqM,MAAA,CAAA/E,mBAAA,CAAA+E,MAAA,CAAA/L,cAAA,CAAAmE,SAAA;;YAEA;YACA4H,MAAA,CAAA/L,cAAA,CAAA0B,cAAA,IAAAqK,MAAA,CAAAhL,kBAAA,CAAAX,KAAA,GACA2L,MAAA,CAAAhL,kBAAA,CAAAR,KAAA;UACA,GAAAoD,KAAA,WAAAC,KAAA;YACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;YACAmI,MAAA,CAAA/H,QAAA,CAAAJ,KAAA,cAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;UACA,GAAAuL,OAAA;YACAH,MAAA,CAAAlM,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAsM,iBAAA,WAAAA,kBAAA7F,OAAA;MAAA,IAAA8F,MAAA;MACA,KAAAC,QAAA;QACAlH,iBAAA;QACAmH,gBAAA;QACAjB,IAAA;MACA,GAAA7H,IAAA;QACA,IAAA2I,8BAAA,EAAA7F,OAAA,CAAAnC,SAAA,EAAAX,IAAA;UACA8C,OAAA,CAAA9E,MAAA;UACA8E,OAAA,CAAA5E,cAAA;UACA0K,MAAA,CAAApI,QAAA,CAAAW,OAAA;UACAyH,MAAA,CAAA3F,eAAA,CAAAH,OAAA,CAAAnC,SAAA;QACA,GAAAR,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,WAAAA,KAAA;UACAwI,MAAA,CAAApI,QAAA,CAAAJ,KAAA,cAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA;IACA;IAEA;IACA4L,aAAA,WAAAA,cAAAjG,OAAA;MAAA,IAAAkG,OAAA;MACA,KAAAH,QAAA;QACAlH,iBAAA;QACAmH,gBAAA;QACAjB,IAAA;MACA,GAAA7H,IAAA;QACA,IAAAiJ,uBAAA,GAAAnG,OAAA,CAAAnC,SAAA,GAAAX,IAAA;UACAgJ,OAAA,CAAAxI,QAAA,CAAAW,OAAA;UACA6H,OAAA,CAAAhK,eAAA;QACA,GAAAmB,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACA4I,OAAA,CAAAxI,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACA6I,OAAA,CAAAxI,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAuG,WAAA,WAAAA,YAAA1L,OAAA;MACA,KAAAiE,MAAA,CAAAjE,OAAA,CAAAA,OAAA;QACAmE,iBAAA;MACA;IACA;IAEA;IACAwH,WAAA,WAAAA,YAAA3L,OAAA;MAAA,IAAA4L,OAAA;MACAC,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAA/L,OAAA,CAAAA,OAAA,EAAAwC,IAAA;QACAoJ,OAAA,CAAA5I,QAAA,CAAAW,OAAA;MACA,GAAAhB,KAAA;QACAiJ,OAAA,CAAA5I,QAAA,CAAAJ,KAAA;MACA;IACA;IAEA;IACAoJ,WAAA,WAAAA,YAAAhM,OAAA;MAAA,IAAAiM,OAAA;MACA,KAAAC,OAAA;QACA/H,iBAAA;QACAmH,gBAAA;QACAa,SAAA;QACAC,UAAA,EAAApM,OAAA,CAAAA;MACA,GAAAwC,IAAA,WAAA6J,IAAA;QAAA,IAAAC,KAAA,GAAAD,IAAA,CAAAC,KAAA;QACA,IAAAC,UAAA;UACA/F,SAAA,EAAAxG,OAAA,CAAAwG,SAAA;UACAxG,OAAA,EAAAsM,KAAA;UACA9M,SAAA,EAAA8M,KAAA,CAAA5K;QACA;QAEA,IAAA8K,0BAAA,EAAAD,UAAA,EAAA/J,IAAA;UACAxC,OAAA,CAAAA,OAAA,GAAAsM,KAAA;UACAtM,OAAA,CAAAR,SAAA,GAAA8M,KAAA,CAAA5K,MAAA;UACAuK,OAAA,CAAAjJ,QAAA,CAAAW,OAAA;QACA,GAAAhB,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACAqJ,OAAA,CAAAjJ,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACAsJ,OAAA,CAAAjJ,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAsH,aAAA,WAAAA,cAAAzM,OAAA;MAAA,IAAA0M,OAAA;MACA,KAAArB,QAAA;QACAlH,iBAAA;QACAmH,gBAAA;QACAjB,IAAA;MACA,GAAA7H,IAAA;QACA,IAAAmK,uBAAA,GAAA3M,OAAA,CAAAwG,SAAA,GAAAhE,IAAA;UACAkK,OAAA,CAAA1J,QAAA,CAAAW,OAAA;UACA+I,OAAA,CAAA1G,mBAAA,CAAA0G,OAAA,CAAA1N,cAAA,CAAAmE,SAAA;UACAuJ,OAAA,CAAA1N,cAAA,CAAA0B,cAAA;QACA,GAAAiC,KAAA,WAAAC,KAAA;UACAjB,OAAA,CAAAiB,KAAA,SAAAA,KAAA;UACA8J,OAAA,CAAA1J,QAAA,CAAAJ,KAAA,YAAAA,KAAA,CAAA8C,GAAA,IAAA9C,KAAA,CAAAjD,OAAA;QACA;MACA,GAAAgD,KAAA;QACA+J,OAAA,CAAA1J,QAAA,CAAAmC,IAAA;MACA;IACA;IAEA;IACAyH,aAAA,WAAAA,cAAAtH,OAAA;MACA,IAAAtF,OAAA,8BAAAyD,MAAA,CAAA6B,OAAA,CAAA/G,IAAA;MACAyB,OAAA,qCAAAyD,MAAA,CAAA6B,OAAA,CAAA3E,UAAA;MACAX,OAAA,yBAAAyD,MAAA,MAAAxE,eAAA,CAAAyC,MAAA;MAEA,KAAAzC,eAAA,CAAA2K,OAAA,WAAA3I,IAAA,EAAAkG,KAAA;QACAnH,OAAA,OAAAyD,MAAA,CAAA0D,KAAA,YAAA1D,MAAA,CAAAxC,IAAA,CAAAjB,OAAA;QACAA,OAAA,wCAAAyD,MAAA,CAAAxC,IAAA,CAAAN,UAAA;MACA;MAEA,IAAAkM,IAAA,OAAAC,IAAA,EAAA9M,OAAA;QAAAqK,IAAA;MAAA;MACA,IAAA0C,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;MACA,IAAAK,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,CAAA,CAAAI,QAAA,MAAA7J,MAAA,CAAA6B,OAAA,CAAA/G,IAAA;MACA2O,CAAA,CAAAK,KAAA;MACAP,GAAA,CAAAQ,eAAA,CAAAT,GAAA;MAEA,KAAA/J,QAAA,CAAAW,OAAA;IACA;IACA;IACA8J,aAAA,WAAAA,cAAAjN,MAAA;MACA,IAAAkN,SAAA;QACAC,OAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACA,OAAAJ,SAAA,CAAAlN,MAAA,KAAAA,MAAA;IACA;IAEA;IACAuN,cAAA,WAAAA,eAAAvN,MAAA;MACA,IAAAwN,QAAA;QACAL,OAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACA,OAAAE,QAAA,CAAAxN,MAAA;IACA;EACA;AACA", "ignoreList": []}]}