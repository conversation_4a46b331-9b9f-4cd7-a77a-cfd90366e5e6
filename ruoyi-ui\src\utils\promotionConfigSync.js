/**
 * 推广页面配置同步管理器
 * 负责在DIY页面、Store页面、预览页面之间同步配置信息
 */

class PromotionConfigSync {
  constructor() {
    this.listeners = new Map()
    this.setupStorageListener()
  }

  /**
   * 获取配置存储键
   */
  getConfigKey(storeId) {
    return `promotion_diy_fixed_${storeId}`
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    // 平台图标映射
    const platformIcons = {
      douyin: require('@/assets/images/platforms/douyin.png'),
      kuaishou: require('@/assets/images/platforms/kuaishou.png'),
      xiaohongshu: require('@/assets/images/platforms/xiaohongshu.png'),
      pengyouquan: require('@/assets/images/platforms/pengyouquan.png'),
      shipinhao: require('@/assets/images/platforms/shipinhao.png'),
      meituandian: require('@/assets/images/platforms/meituandian.png'),
      dazhongdian: require('@/assets/images/platforms/dazhongdian.png'),
      gaodedian: require('@/assets/images/platforms/gaodedian.png'),
      baidudian: require('@/assets/images/platforms/baidudian.png')
    }
    return {
      // 页面背景
      background: {
        type: 'gradient',
        color: '#ffffff',
        gradientDirection: '135deg',
        gradientStart: '#667eea',
        gradientEnd: '#764ba2',
        image: ''
      },
      
      // Banner设置
      banner: {
        enabled: true,
        backgroundImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=375&h=210&fit=crop',
        title: '碰一碰领福利',
        subtitle: '请点击进行操作吧',
        titleColor: '#ffffff',
        subtitleColor: '#f0f0f0',
        titleFontSize: 24,
        subtitleFontSize: 16,
        titleFontWeight: '700',
        subtitleFontWeight: '400',
        textAlign: 'center',
        maskOpacity: 30,
        priority: 1
      },

      // 产品设置
      products: {
        enabled: true,
        priority: 2,
        items: [
          {
            image: '',
            name: '精选商品',
            description: '优质商品，值得信赖',
            specifications: '规格：标准版',
            originalPrice: '99.00',
            currentPrice: '79.00',
            discount: '限时8折',
            buyUrl: '',
            isHot: true,
            isBestSeller: false,
            isNew: false,
            isLimited: false,
            customTag: '',
            buttonText: '立即购买'
          }
        ]
      },

      // 平台设置
      platforms: {
        enabled: true,
        priority: 3,
        itemsPerRow: 2,
        videoPlatforms: {
          enabled: true,
          items: [
            {
              name: 'douyin',
              displayName: '抖音',
              icon: platformIcons.douyin,
              color: '#FE2974',
              url: 'https://www.douyin.com',
              description: '关注我们获取更多优惠',
              enabled: true,
              editing: false
            },
            {
              name: 'kuaishou',
              displayName: '快手',
              icon: platformIcons.kuaishou,
              color: '#FF4100',
              url: 'https://www.kuaishou.com',
              description: '精彩短视频等你来看',
              enabled: true,
              editing: false
            },
            {
              name: 'xiaohongshu',
              displayName: '小红书',
              icon: platformIcons.xiaohongshu,
              color: '#FF2D92',
              url: 'https://www.xiaohongshu.com',
              description: '分享生活的美好',
              enabled: true,
              editing: false
            },
            {
              name: 'pengyouquan_video',
              displayName: '朋友圈视频',
              icon: platformIcons.pengyouquan,
              color: '#07C160',
              url: 'https://weixin.qq.com',
              description: '分享到朋友圈',
              enabled: true,
              editing: false
            },
            {
              name: 'shipinhao',
              displayName: '视频号',
              icon: platformIcons.shipinhao,
              color: '#07C160',
              url: 'https://channels.weixin.qq.com',
              description: '微信视频号',
              enabled: true,
              editing: false
            }
          ]
        },
        reviewPlatforms: {
          enabled: true,
          items: [
            {
              name: 'douyin_review',
              displayName: '抖音点评',
              icon: platformIcons.douyin,
              color: '#FE2974',
              url: 'https://www.douyin.com',
              description: '抖音店铺点评',
              enabled: true,
              editing: false
            },
            {
              name: 'meituan',
              displayName: '美团',
              icon: platformIcons.meituandian,
              color: '#FFBE00',
              url: 'https://www.meituan.com',
              description: '给我们好评吧',
              enabled: true,
              editing: false
            },
            {
              name: 'dianping',
              displayName: '大众点评',
              icon: platformIcons.dazhongdian,
              color: '#FF6900',
              url: 'https://www.dianping.com',
              description: '查看更多评价',
              enabled: true,
              editing: false
            },
            {
              name: 'xiaohongshu_text',
              displayName: '小红书图文',
              icon: platformIcons.xiaohongshu,
              color: '#FF2D92',
              url: 'https://www.xiaohongshu.com',
              description: '小红书图文分享',
              enabled: true,
              editing: false
            },
            {
              name: 'pengyouquan_text',
              displayName: '朋友圈图文',
              icon: platformIcons.pengyouquan,
              color: '#07C160',
              url: 'https://weixin.qq.com',
              description: '朋友圈图文分享',
              enabled: true,
              editing: false
            },
            {
              name: 'gaode',
              displayName: '高德地图',
              icon: platformIcons.gaodedian,
              color: '#00A6FB',
              url: 'https://www.amap.com',
              description: '查看位置信息',
              enabled: true,
              editing: false
            },
            {
              name: 'baidu',
              displayName: '百度地图',
              icon: platformIcons.baidudian,
              color: '#3385FF',
              url: 'https://map.baidu.com',
              description: '导航到店',
              enabled: true,
              editing: false
            }
          ]
        }
      },

      // WiFi设置
      wifi: {
        enabled: true,
        priority: 4,
        ssid: 'Starbucks_Free_WiFi',
        password: 'coffee2024',
        buttonText: '一键连接WiFi',
        description: '免费WiFi，欢迎使用',
        backgroundColor: 'rgba(240,248,255,0.95)',
        textColor: '#333333',
        buttonColor: '#409EFF',
        borderRadius: 12,
        fontSize: 14
      }
    }
  }

  /**
   * 深度合并对象
   */
  deepMerge(target, source) {
    const result = { ...target }
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    return result
  }

  /**
   * 保存配置
   */
  saveConfig(storeId, config) {
    const configKey = this.getConfigKey(storeId)
    const configData = {
      ...config,
      lastUpdated: Date.now(),
      version: '2.0'
    }
    
    localStorage.setItem(configKey, JSON.stringify(configData))
    
    // 触发配置更新事件
    this.notifyConfigChange(storeId, configData)
    
    console.log(`配置已保存 - 门店ID: ${storeId}`)
    return true
  }

  /**
   * 加载配置
   */
  loadConfig(storeId) {
    const configKey = this.getConfigKey(storeId)
    const savedConfig = localStorage.getItem(configKey)
    
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig)
        const defaultConfig = this.getDefaultConfig()
        
        // 检查版本兼容性
        if (this.isConfigCompatible(parsedConfig, defaultConfig)) {
          return this.deepMerge(defaultConfig, parsedConfig)
        } else {
          console.log('配置版本不兼容，使用默认配置')
          return defaultConfig
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        return this.getDefaultConfig()
      }
    }
    
    return this.getDefaultConfig()
  }

  /**
   * 检查配置兼容性
   */
  isConfigCompatible(savedConfig, defaultConfig) {
    // 检查平台数量是否匹配
    const savedVideoCount = savedConfig.platforms?.videoPlatforms?.items?.length || 0
    const savedReviewCount = savedConfig.platforms?.reviewPlatforms?.items?.length || 0
    const defaultVideoCount = defaultConfig.platforms.videoPlatforms.items.length
    const defaultReviewCount = defaultConfig.platforms.reviewPlatforms.items.length
    
    return savedVideoCount === defaultVideoCount && savedReviewCount === defaultReviewCount
  }

  /**
   * 设置localStorage监听
   */
  setupStorageListener() {
    window.addEventListener('storage', (e) => {
      if (e.key && e.key.startsWith('promotion_diy_fixed_')) {
        const storeId = e.key.replace('promotion_diy_fixed_', '')
        if (e.newValue) {
          try {
            const newConfig = JSON.parse(e.newValue)
            this.notifyConfigChange(storeId, newConfig)
          } catch (error) {
            console.error('解析配置变化失败:', error)
          }
        }
      }
    })
  }

  /**
   * 注册配置变化监听器
   */
  onConfigChange(storeId, callback) {
    if (!this.listeners.has(storeId)) {
      this.listeners.set(storeId, [])
    }
    this.listeners.get(storeId).push(callback)
  }

  /**
   * 移除配置变化监听器
   */
  offConfigChange(storeId, callback) {
    if (this.listeners.has(storeId)) {
      const callbacks = this.listeners.get(storeId)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 通知配置变化
   */
  notifyConfigChange(storeId, config) {
    if (this.listeners.has(storeId)) {
      this.listeners.get(storeId).forEach(callback => {
        try {
          callback(config)
        } catch (error) {
          console.error('配置变化回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 清除配置
   */
  clearConfig(storeId) {
    const configKey = this.getConfigKey(storeId)
    localStorage.removeItem(configKey)
    console.log(`配置已清除 - 门店ID: ${storeId}`)
  }

  /**
   * 获取NFC地址
   */
  getNFCUrl(storeId) {
    return `${window.location.origin}/promotion/${storeId}`
  }

  /**
   * 获取DIY编辑地址
   */
  getDIYUrl(storeId) {
    return `${window.location.origin}/promotion/${storeId}/diy`
  }
}

// 创建全局实例
const promotionConfigSync = new PromotionConfigSync()

export default promotionConfigSync
