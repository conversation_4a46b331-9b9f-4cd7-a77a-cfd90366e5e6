# AI文案库API测试脚本
$baseUrl = "http://localhost:8078"

Write-Host "=== AI文案库API测试 ===" -ForegroundColor Green

# 1. 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResult = Invoke-RestMethod -Uri "$baseUrl/ai/test/health" -Method GET
    Write-Host "健康检查成功:" -ForegroundColor Green
    $healthResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试查询文案库列表
Write-Host "`n2. 测试查询文案库列表..." -ForegroundColor Yellow
try {
    $listResult = Invoke-RestMethod -Uri "$baseUrl/ai/copywriting/library/list/test" -Method GET
    Write-Host "查询文案库列表成功:" -ForegroundColor Green
    $listResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "查询文案库列表失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# 3. 测试创建文案库
Write-Host "`n3. 测试创建文案库..." -ForegroundColor Yellow
$createData = @{
    libraryName = "TestLibrary_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    useAi = $true
    shopDetails = "Test shop: A cozy coffee shop"
    prompt = "Generate attractive food promotion copy"
    targetCount = 5
    wordCount = 100
} | ConvertTo-Json

try {
    $createResult = Invoke-RestMethod -Uri "$baseUrl/ai/copywriting/library/test" -Method POST -ContentType "application/json" -Body $createData
    Write-Host "创建文案库成功:" -ForegroundColor Green
    $createResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "创建文案库失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "响应内容: $responseBody" -ForegroundColor Red
    }
}

# 4. 测试火山引擎Doubao
Write-Host "`n4. 测试火山引擎Doubao..." -ForegroundColor Yellow
try {
    $doubaoResult = Invoke-RestMethod -Uri "$baseUrl/ai/test/test-doubao-direct" -Method GET
    Write-Host "火山引擎Doubao测试成功:" -ForegroundColor Green
    $doubaoResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "火山引擎Doubao测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
