import request from '@/utils/request'

// 查询文案库列表（测试版本，无需权限）
export function listLibraryTest(query) {
  return request({
    url: '/ai/copywriting/library/list/test',
    method: 'get',
    params: query
  })
}

// 新增文案库（测试版本，无需权限）
export function addLibraryTest(data) {
  return request({
    url: '/ai/copywriting/library/test',
    method: 'post',
    data: data
  })
}

// 查询文案库详细（测试版本，无需权限）
export function getLibraryTest(libraryId) {
  return request({
    url: '/ai/copywriting/library/test/' + libraryId,
    method: 'get'
  })
}

// 生成AI文案（测试版本，无需权限）
export function generateCopywritingTest(data) {
  return request({
    url: '/ai/copywriting/generate/test',
    method: 'post',
    data: data,
    timeout: 60000 // 60秒超时
  })
}

// 查询文案内容列表（测试版本，无需权限）
export function listContentTest(libraryId) {
  return request({
    url: '/ai/copywriting/content/list/test/' + libraryId,
    method: 'get'
  })
}

// 新增文案内容（测试版本，无需权限）
export function addContentTest(data) {
  return request({
    url: '/ai/copywriting/content/test',
    method: 'post',
    data: data,
    timeout: 30000 // 30秒超时
  })
}

// 删除文案库（测试版本，无需权限）
export function delLibraryTest(libraryIds) {
  return request({
    url: '/ai/copywriting/library/test/' + libraryIds,
    method: 'delete'
  })
}

// 删除文案内容（测试版本，无需权限）
export function delContentTest(contentIds) {
  return request({
    url: '/ai/copywriting/content/test/' + contentIds,
    method: 'delete'
  })
}

// 获取生成进度（测试版本，无需权限）
export function getProgressTest(libraryId) {
  return request({
    url: '/ai/copywriting/progress/test/' + libraryId,
    method: 'get'
  })
}

// 重新生成文案库（测试版本，无需权限）
export function regenerateLibraryTest(libraryId) {
  return request({
    url: '/ai/copywriting/regenerate/test/' + libraryId,
    method: 'post',
    timeout: 60000 // 60秒超时
  })
}

// 验证百度AI配置（测试版本，无需权限）
export function validateBaiduConfigTest() {
  return request({
    url: '/ai/copywriting/validate-config/test',
    method: 'get'
  })
}

// 获取模型信息（测试版本，无需权限）
export function getModelInfoTest() {
  return request({
    url: '/ai/copywriting/model-info/test',
    method: 'get'
  })
}

// 直接测试火山引擎Doubao API
export function testDoubaoDirectService() {
  return request({
    url: '/ai/test/test-doubao-direct',
    method: 'get',
    timeout: 30000
  })
}

// 测试文案生成
export function testGenerate(data) {
  return request({
    url: '/ai/test/generate',
    method: 'post',
    data: data,
    timeout: 30000
  })
}

// 健康检查
export function healthCheck() {
  return request({
    url: '/ai/test/health',
    method: 'get'
  })
}
