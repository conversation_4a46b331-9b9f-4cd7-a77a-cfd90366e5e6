<template>
  <div class="promotion-page-simple">
    <div class="header">
      <h1>推广页面测试</h1>
      <p>Store ID: {{ storeId }}</p>
    </div>
    
    <div class="content">
      <div class="store-info">
        <h2>{{ currentConfig.store.name }}</h2>
        <p>{{ currentConfig.store.promotionText }}</p>
      </div>
      
      <div class="product-info">
        <h3>商品: {{ currentConfig.product.name }}</h3>
        <p>状态: {{ currentConfig.product.status }}</p>
      </div>
      
      <div class="platforms">
        <div 
          v-for="platform in currentConfig.platforms" 
          :key="platform.id"
          class="platform-item"
        >
          <span class="platform-icon">{{ platform.icon }}</span>
          <span class="platform-label">{{ platform.label }}</span>
        </div>
      </div>
      
      <div class="action-buttons">
        <button @click="goToConfig" class="config-btn">配置页面</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionPageSimple',
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },
    currentConfig() {
      return {
        store: {
          name: '天府火锅',
          promotionText: '爆款团购'
        },
        product: {
          name: '天府火锅二人餐',
          status: '热销'
        },
        platforms: [
          { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '发抖音' },
          { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '发快手' },
          { id: 3, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书图文' },
          { id: 4, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书视频' }
        ]
      }
    }
  },
  methods: {
    goToConfig() {
      this.$router.push({
        path: '/promotion/config/index',
        query: {
          storeId: this.storeId,
          storeName: this.currentConfig.store.name
        }
      })
    }
  }
}
</script>

<style scoped>
.promotion-page-simple {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.store-info, .product-info {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.platforms {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.platform-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  text-align: center;
  flex-direction: column;
}

.platform-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.platform-label {
  font-size: 12px;
}

.action-buttons {
  text-align: center;
}

.config-btn {
  background: #fff;
  color: #667eea;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.config-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}
</style>
