<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文案库演示页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .demo-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .library-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .library-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .library-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .library-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .library-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .tag.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .tag.generating {
            background: #fff3cd;
            color: #856404;
        }
        
        .tag.ai {
            background: #cce5ff;
            color: #004085;
        }
        
        .library-info {
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .library-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6c757d;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .content-preview {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-size: 0.9em;
            line-height: 1.6;
            color: #495057;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .feature h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .library-grid {
                grid-template-columns: 1fr;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI文案库演示系统</h1>
            <p>智能文案生成 · 无需登录 · 立即体验</p>
        </div>
        
        <div class="content">
            <!-- 功能特色 -->
            <div class="demo-section">
                <h3>🌟 系统特色</h3>
                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">🚀</div>
                        <h4>AI智能生成</h4>
                        <p>基于DeepSeek-V3模型，生成高质量营销文案</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">📚</div>
                        <h4>文案库管理</h4>
                        <p>分类管理文案，支持批量生成和编辑</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">🎯</div>
                        <h4>精准定制</h4>
                        <p>根据店铺特色和需求，定制专属文案风格</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">⚡</div>
                        <h4>即时可用</h4>
                        <p>无需复杂配置，开箱即用的文案生成体验</p>
                    </div>
                </div>
            </div>
            
            <!-- 演示文案库 -->
            <div class="demo-section">
                <h3>📖 演示文案库</h3>
                <div class="library-grid">
                    <div class="library-card">
                        <div class="library-title">
                            🍽️ 美食探店文案库
                        </div>
                        <div class="library-meta">
                            <span class="tag completed">已完成</span>
                            <span class="tag ai">AI生成</span>
                        </div>
                        <div class="library-info">
                            精选美食餐厅，提供各类特色菜品和优质服务。专注于生成吸引人的美食探店文案，突出菜品特色和用餐体验。
                        </div>
                        <div class="library-stats">
                            <div class="stat">
                                <div class="stat-value">20</div>
                                <div class="stat-label">目标条数</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">20</div>
                                <div class="stat-label">已生成</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">150</div>
                                <div class="stat-label">平均字数</div>
                            </div>
                        </div>
                        <div class="content-preview">
                            🍽️ 探店新发现！这家隐藏在巷子里的小餐厅，用最朴实的食材做出了最惊艳的味道。招牌红烧肉入口即化，配菜清爽解腻，老板娘的手艺真是没话说！人均消费不到50元，性价比超高...
                        </div>
                    </div>
                    
                    <div class="library-card">
                        <div class="library-title">
                            👗 时尚服装推广库
                        </div>
                        <div class="library-meta">
                            <span class="tag completed">已完成</span>
                            <span class="tag ai">AI生成</span>
                        </div>
                        <div class="library-info">
                            时尚服装品牌，主营潮流服饰和配饰。生成时尚服装推广文案，强调款式新颖和品质优良。
                        </div>
                        <div class="library-stats">
                            <div class="stat">
                                <div class="stat-value">15</div>
                                <div class="stat-label">目标条数</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">15</div>
                                <div class="stat-label">已生成</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">120</div>
                                <div class="stat-label">平均字数</div>
                            </div>
                        </div>
                        <div class="content-preview">
                            ✨ 春季新品上市！这件连衣裙的设计简直太美了，优雅的A字版型修饰身形，精致的蕾丝细节增添女性魅力。面料柔软舒适，颜色清新淡雅，无论是约会还是上班都能轻松驾驭...
                        </div>
                    </div>
                    
                    <div class="library-card">
                        <div class="library-title">
                            ☕ 咖啡厅温馨文案库
                        </div>
                        <div class="library-meta">
                            <span class="tag generating">生成中</span>
                            <span class="tag ai">AI生成</span>
                        </div>
                        <div class="library-info">
                            温馨咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段。生成温馨咖啡厅推广文案，突出环境舒适和咖啡品质。
                        </div>
                        <div class="library-stats">
                            <div class="stat">
                                <div class="stat-value">10</div>
                                <div class="stat-label">目标条数</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">8</div>
                                <div class="stat-label">已生成</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">100</div>
                                <div class="stat-label">平均字数</div>
                            </div>
                        </div>
                        <div class="content-preview">
                            ☕ 温馨咖啡时光，等你来享受！精选优质咖啡豆，手工调制每一杯，搭配精致甜点，让你的午后时光更加美好。在这里，你可以放慢脚步，享受生活的美好瞬间...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="demo-section">
                <h3>📋 使用说明</h3>
                <div style="background: white; padding: 20px; border-radius: 6px; line-height: 1.8;">
                    <p><strong>🎯 当前状态：</strong>演示模式 - 所有功能均可正常体验</p>
                    <p><strong>🔧 完整功能：</strong>重启后端服务后，可启用真实的AI文案生成功能</p>
                    <p><strong>💡 体验方式：</strong></p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>访问 <code>http://localhost:8080/storer/shipin</code> 查看文案库管理界面</li>
                        <li>访问 <code>http://localhost:8080/storer/ai-test</code> 测试AI文案生成功能</li>
                        <li>点击"创建文案库"体验完整的创建流程</li>
                        <li>查看已有文案库的内容和管理功能</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 AI文案库演示系统 - 让文案创作更简单高效</p>
        </div>
    </div>
</body>
</html>
