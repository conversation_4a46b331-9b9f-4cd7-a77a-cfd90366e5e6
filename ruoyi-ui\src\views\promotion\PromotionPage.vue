<template>
  <div class="promotion-page" :style="pageStyles">
    <!-- 头部区域 -->
    <div class="header-section">
      <h1 class="main-title">{{ currentPageConfig.header.title }}</h1>
      <p class="sub-title">{{ currentPageConfig.header.subtitle }}</p>
    </div>

    <!-- 店铺信息区域 -->
    <div class="store-info-section">
      <div class="store-card">
        <img :src="currentPageConfig.store.logo" class="store-logo" alt="店铺logo">
        <span class="store-name">{{ currentPageConfig.store.name }}</span>
      </div>
      <div class="promotion-badge">{{ currentPageConfig.store.promotionText }}</div>
    </div>

    <!-- 商品展示区域 -->
    <div class="product-section">
      <div class="product-card">
        <img :src="currentPageConfig.product.image" class="product-image" alt="商品图片">
        <div class="product-info">
          <div class="product-title">商品：{{ currentPageConfig.product.name }}</div>
          <div class="product-status">{{ currentPageConfig.product.status }}</div>
        </div>
      </div>
    </div>

    <!-- 视频发布按钮 -->
    <div class="video-section">
      <button class="video-btn">{{ currentPageConfig.video.buttonText }}</button>
    </div>

    <!-- 平台图标区域 -->
    <div class="platforms-section">
      <div 
        v-for="platform in currentPageConfig.platforms"
        :key="platform.id"
        class="platform-item"
        :class="{ 'disabled': !platform.enabled }"
      >
        <div class="platform-icon" :style="{ backgroundColor: platform.color }">
          <IconDisplay :icon="platform.icon" size="24px" :color="platform.color" />
        </div>
        <div class="platform-label">{{ platform.label }}</div>
      </div>
    </div>

    <!-- 朋友圈图文和视频区域 -->
    <div class="friend-circle-section">
      <div 
        v-for="item in currentPageConfig.friendCircle"
        :key="item.id"
        class="friend-circle-item"
        :class="{ 'disabled': !item.enabled }"
      >
        <div class="friend-circle-icon" :style="{ backgroundColor: item.color }">
          <IconDisplay :icon="item.icon" size="24px" :color="item.color" />
        </div>
        <div class="friend-circle-label">{{ item.label }}</div>
      </div>
    </div>

    <!-- 自定义链接小程序 -->
    <div class="mini-program-section">
      <div class="mini-program-title">{{ currentPageConfig.miniProgram.title }}</div>
      <div class="mini-program-items">
        <div 
          v-for="item in currentPageConfig.miniProgram.items"
          :key="item.id"
          class="mini-program-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <img :src="item.icon" class="mini-program-icon" alt="小程序图标">
          <div class="mini-program-label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- 营销私域 -->
    <div class="marketing-section">
      <div class="marketing-title">{{ currentPageConfig.marketing.title }}</div>
      <div class="marketing-items">
        <div 
          v-for="item in currentPageConfig.marketing.items"
          :key="item.id"
          class="marketing-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <div class="marketing-icon" :style="{ backgroundColor: item.color }">
            <IconDisplay :icon="item.icon" size="24px" :color="item.color" />
          </div>
          <div class="marketing-label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- WiFi信息区域 -->
    <div class="wifi-section" v-if="currentPageConfig.wifi.enabled">
      <div class="wifi-info">
        <div class="wifi-icon">📶</div>
        <div class="wifi-details">
          <div class="wifi-name">Wi-Fi: {{ currentPageConfig.wifi.name }}</div>
          <div class="wifi-password">密码: {{ currentPageConfig.wifi.password }}</div>
        </div>
        <button class="wifi-connect-btn">{{ currentPageConfig.wifi.buttonText }}</button>
      </div>
    </div>

    <!-- 打卡点评团购入口 -->
    <div class="review-section">
      <div class="review-title">{{ currentPageConfig.review.title }}</div>
      <div class="review-items">
        <div 
          v-for="item in currentPageConfig.review.items"
          :key="item.id"
          class="review-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <div class="review-icon" :style="{ backgroundColor: item.color }">
            <IconDisplay :icon="item.icon" size="24px" :color="item.color" />
          </div>
          <div class="review-label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav-section" v-if="currentPageConfig.quickNav.enabled">
      <div class="quick-nav-title">{{ currentPageConfig.quickNav.title }}</div>
      <div class="product-showcase">
        <img :src="currentPageConfig.quickNav.product.image" class="showcase-image" alt="产品展示">
        <div class="product-details">
          <div class="product-name">{{ currentPageConfig.quickNav.product.name }}</div>
          <div class="product-code">{{ currentPageConfig.quickNav.product.code }}</div>
          <div class="product-price">
            <span class="current-price">{{ currentPageConfig.quickNav.product.currentPrice }}</span>
            <span class="original-price">{{ currentPageConfig.quickNav.product.originalPrice }}</span>
          </div>
          <div class="product-status-text">{{ currentPageConfig.quickNav.product.statusText }}</div>
        </div>
        <button class="order-btn">{{ currentPageConfig.quickNav.product.buttonText }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import IconDisplay from '@/components/IconDisplay.vue'

export default {
  name: 'PromotionPage',
  components: {
    IconDisplay
  },
  props: {
    pageConfig: {
      type: Object,
      default: null
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },
    currentPageConfig() {
      return this.pageConfig || this.defaultConfig
    },
    pageStyles() {
      return {
        background: this.currentPageConfig.background
      }
    }
  },
  data() {
    return {
      defaultConfig: {
        // 页面样式配置
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        
        // 头部配置
        header: {
          title: '碰一碰，领福利',
          subtitle: '请点击进行操作吧'
        },
        
        // 店铺信息
        store: {
          name: '天府火锅',
          logo: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          promotionText: '爆款团购'
        },
        
        // 商品信息
        product: {
          name: '天府火锅二人餐',
          image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
          status: '热销'
        },
        
        // 视频发布
        video: {
          buttonText: '视频发布'
        },
        
        // 平台配置
        platforms: [
          { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '发抖音', color: '#000000', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '发快手', color: '#ff6600', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书图文', color: '#ff2442', enabled: true },
          { id: 4, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书视频', color: '#ff2442', enabled: true }
        ],
        
        // 朋友圈配置
        friendCircle: [
          { id: 1, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈图文', color: '#33cc33', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈视频', color: '#33cc33', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/shipinhao.png'), label: '发视频号', color: '#07c160', enabled: true }
        ],
        
        // 小程序配置
        miniProgram: {
          title: '自定义链接小程序',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '点餐', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '导航名称', enabled: true }
          ]
        },
        
        // 营销私域
        marketing: {
          title: '营销私域',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/weixin.png'), label: '加微信', color: '#07c160', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/qq.png'), label: '加QQ', color: '#1296db', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/qiye.png'), label: '加企微', color: '#1296db', enabled: true }
          ]
        },
        
        // WiFi配置
        wifi: {
          enabled: true,
          name: 'dajiangjia',
          password: '18300250542',
          buttonText: '一键链接'
        },
        
        // 点评配置
        review: {
          title: '打卡点评团购入口',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyindian.png'), label: '抖音点评', color: '#000000', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/gaodedian.png'), label: '高德点评', color: '#00a6f7', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/baidudian.png'), label: '百度点评', color: '#2196f3', enabled: true },
            { id: 4, icon: require('@/assets/images/platforms/meituandian.png'), label: '美团点评', color: '#ffc107', enabled: true },
            { id: 5, icon: require('@/assets/images/platforms/dazhongdian.png'), label: '大众点评', color: '#ff9800', enabled: true }
          ]
        },
        
        // 快速导航
        quickNav: {
          enabled: true,
          title: '快速导航',
          product: {
            name: '天府',
            code: '已售 11111',
            image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
            currentPrice: '￥11111.00',
            originalPrice: '￥11.00',
            statusText: '立即中',
            buttonText: '立即抢购'
          }
        }
      }
    }
  },
  mounted() {
    this.loadPageConfig()
  },
  methods: {
    // 加载页面配置
    loadPageConfig() {
      // 如果没有传入pageConfig prop，则根据storeId加载对应的页面配置
      if (!this.pageConfig) {
        console.log('加载店铺ID:', this.storeId, '的推广页面配置')
        // 这里可以调用API获取配置数据
        // const config = await this.$api.promotion.getConfig(this.storeId)
        // this.defaultConfig = config
      }
    },
    
    // 保存页面配置
    savePageConfig() {
      // 保存配置到后端
      console.log('保存页面配置:', this.currentPageConfig)
    }
  }
}
</script>

<style lang="scss" scoped>
.promotion-page {
  max-width: 375px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header-section {
  text-align: center;
  margin-bottom: 20px;
  color: white;
  
  .main-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }
  
  .sub-title {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
  }
}

.store-info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  
  .store-card {
    display: flex;
    align-items: center;
    
    .store-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      margin-right: 12px;
      object-fit: cover;
    }
    
    .store-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .promotion-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
      border: 8px solid transparent;
      border-left-color: #ff6b6b;
    }
  }
}

.product-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  
  .product-card {
    display: flex;
    align-items: center;
    
    .product-image {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      margin-right: 15px;
      object-fit: cover;
    }
    
    .product-info {
      .product-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .product-status {
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        color: #333;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
      }
    }
  }
}

.video-section {
  text-align: center;
  margin-bottom: 20px;
  
  .video-btn {
    background: linear-gradient(45deg, #ff9500, #ffb84d);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

.platforms-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  
  .platform-item {
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .platform-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      
      .platform-symbol {
        color: white;
        font-size: 20px;
        font-weight: 600;
      }
    }
    
    .platform-label {
      color: white;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.friend-circle-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  
  .friend-circle-item {
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .friend-circle-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      
      .friend-circle-symbol {
        color: white;
        font-size: 20px;
      }
    }
    
    .friend-circle-label {
      color: white;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.mini-program-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  
  .mini-program-title {
    text-align: center;
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
  }
  
  .mini-program-items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    
    .mini-program-item {
      text-align: center;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .mini-program-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        margin: 0 auto 8px;
        object-fit: cover;
      }
      
      .mini-program-label {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.marketing-section {
  margin-bottom: 20px;
  
  .marketing-title {
    text-align: center;
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
  }
  
  .marketing-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    
    .marketing-item {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      padding: 15px;
      text-align: center;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .marketing-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        
        .marketing-symbol {
          color: white;
          font-size: 16px;
        }
      }
      
      .marketing-label {
        color: #333;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

.wifi-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  
  .wifi-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .wifi-icon {
      font-size: 24px;
      margin-right: 12px;
    }
    
    .wifi-details {
      flex: 1;
      
      .wifi-name, .wifi-password {
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
      }
    }
    
    .wifi-connect-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      cursor: pointer;
    }
  }
}

.review-section {
  margin-bottom: 20px;
  
  .review-title {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .review-items {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    
    .review-item {
      text-align: center;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .review-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        .review-symbol {
          color: white;
          font-size: 16px;
        }
      }
      
      .review-label {
        color: white;
        font-size: 10px;
        font-weight: 500;
      }
    }
  }
}

.quick-nav-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 15px;
  
  .quick-nav-title {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .product-showcase {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .showcase-image {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .product-details {
      flex: 1;
      margin: 0 15px;
      
      .product-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .product-code {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .product-price {
        margin-bottom: 4px;
        
        .current-price {
          font-size: 16px;
          font-weight: 600;
          color: #ff4757;
        }
        
        .original-price {
          font-size: 12px;
          color: #999;
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
      
      .product-status-text {
        font-size: 12px;
        color: #007bff;
      }
    }
    
    .order-btn {
      background: linear-gradient(45deg, #ff4757, #ff6b7d);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      min-width: 70px;
    }
  }
}

@media (max-width: 375px) {
  .promotion-page {
    padding: 15px;
  }
  
  .platforms-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .review-items {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
