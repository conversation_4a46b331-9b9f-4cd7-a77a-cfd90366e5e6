{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\api\\ai\\copywriting-test.js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\api\\ai\\copywriting-test.js", "mtime": 1754628589977}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1753759488589}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listLibraryTest", "query", "request", "url", "method", "params", "addLibraryTest", "data", "getLibraryTest", "libraryId", "generateCopywritingTest", "timeout", "listContentTest", "addContentTest", "delLibraryTest", "libraryIds", "delContentTest", "contentIds", "getProgressTest", "regenerateLibraryTest", "validateBaiduConfigTest", "getModelInfoTest", "testDoubaoDirectService", "testGenerate", "healthCheck"], "sources": ["E:/ry-vue-flowable-xg-main/ruoyi-ui/src/api/ai/copywriting-test.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询文案库列表（测试版本，无需权限）\nexport function listLibraryTest(query) {\n  return request({\n    url: '/ai/copywriting/library/list/test',\n    method: 'get',\n    params: query\n  })\n}\n\n// 新增文案库（测试版本，无需权限）\nexport function addLibraryTest(data) {\n  return request({\n    url: '/ai/copywriting/library/test',\n    method: 'post',\n    data: data\n  })\n}\n\n// 查询文案库详细（测试版本，无需权限）\nexport function getLibraryTest(libraryId) {\n  return request({\n    url: '/ai/copywriting/library/test/' + libraryId,\n    method: 'get'\n  })\n}\n\n// 生成AI文案（测试版本，无需权限）\nexport function generateCopywritingTest(data) {\n  return request({\n    url: '/ai/copywriting/generate/test',\n    method: 'post',\n    data: data,\n    timeout: 60000 // 60秒超时\n  })\n}\n\n// 查询文案内容列表（测试版本，无需权限）\nexport function listContentTest(libraryId) {\n  return request({\n    url: '/ai/copywriting/content/list/test/' + libraryId,\n    method: 'get'\n  })\n}\n\n// 新增文案内容（测试版本，无需权限）\nexport function addContentTest(data) {\n  return request({\n    url: '/ai/copywriting/content/test',\n    method: 'post',\n    data: data,\n    timeout: 30000 // 30秒超时\n  })\n}\n\n// 删除文案库（测试版本，无需权限）\nexport function delLibraryTest(libraryIds) {\n  return request({\n    url: '/ai/copywriting/library/test/' + libraryIds,\n    method: 'delete'\n  })\n}\n\n// 删除文案内容（测试版本，无需权限）\nexport function delContentTest(contentIds) {\n  return request({\n    url: '/ai/copywriting/content/test/' + contentIds,\n    method: 'delete'\n  })\n}\n\n// 获取生成进度（测试版本，无需权限）\nexport function getProgressTest(libraryId) {\n  return request({\n    url: '/ai/copywriting/progress/test/' + libraryId,\n    method: 'get'\n  })\n}\n\n// 重新生成文案库（测试版本，无需权限）\nexport function regenerateLibraryTest(libraryId) {\n  return request({\n    url: '/ai/copywriting/regenerate/test/' + libraryId,\n    method: 'post',\n    timeout: 60000 // 60秒超时\n  })\n}\n\n// 验证百度AI配置（测试版本，无需权限）\nexport function validateBaiduConfigTest() {\n  return request({\n    url: '/ai/copywriting/validate-config/test',\n    method: 'get'\n  })\n}\n\n// 获取模型信息（测试版本，无需权限）\nexport function getModelInfoTest() {\n  return request({\n    url: '/ai/copywriting/model-info/test',\n    method: 'get'\n  })\n}\n\n// 直接测试火山引擎Doubao API\nexport function testDoubaoDirectService() {\n  return request({\n    url: '/ai/test/test-doubao-direct',\n    method: 'get',\n    timeout: 30000\n  })\n}\n\n// 测试文案生成\nexport function testGenerate(data) {\n  return request({\n    url: '/ai/test/generate',\n    method: 'post',\n    data: data,\n    timeout: 30000\n  })\n}\n\n// 健康检查\nexport function healthCheck() {\n  return request({\n    url: '/ai/test/health',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGM,SAAS;IAChDL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,uBAAuBA,CAACH,IAAI,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAE,KAAK,CAAC;EACjB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACH,SAAS,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC,GAAGM,SAAS;IACrDL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,cAAcA,CAACN,IAAI,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAE,KAAK,CAAC;EACjB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,cAAcA,CAACC,UAAU,EAAE;EACzC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGY,UAAU;IACjDX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,cAAcA,CAACC,UAAU,EAAE;EACzC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGc,UAAU;IACjDb,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,eAAeA,CAACT,SAAS,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGM,SAAS;IACjDL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,qBAAqBA,CAACV,SAAS,EAAE;EAC/C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGM,SAAS;IACnDL,MAAM,EAAE,MAAM;IACdO,OAAO,EAAE,KAAK,CAAC;EACjB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASkB,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAApB,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbO,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAAChB,IAAI,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,WAAWA,CAAA,EAAG;EAC5B,OAAO,IAAAtB,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}