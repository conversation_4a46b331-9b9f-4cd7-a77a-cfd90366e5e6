<template>
  <div class="promotion-page" :style="pageStyles">
    <!-- 返回按钮和DIY按钮 -->
    <div class="action-buttons-container">
      <button class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        返回门店管理
      </button>
      <button class="diy-button" @click="goDIY">
        <i class="el-icon-brush"></i>
        DIY编辑
      </button>
    </div>

    <!-- 头部区域 -->
    <div class="header-section">
      <h1 class="main-title">{{ currentPageConfig.header.title }}</h1>
      <p class="sub-title">{{ currentPageConfig.header.subtitle }}</p>
    </div>

    <!-- 店铺信息区域 -->
    <div class="store-info-section">
      <div class="store-card">
        <img :src="currentPageConfig.store.logo" class="store-logo" alt="店铺logo">
        <span class="store-name">{{ currentPageConfig.store.name }}</span>
      </div>
      <div class="promotion-badge">{{ currentPageConfig.store.promotionText }}</div>
    </div>

    <!-- 商品展示区域 -->
    <div class="product-section">
      <div class="product-card">
        <img :src="currentPageConfig.product.image" class="product-image" alt="商品图片">
        <div class="product-info">
          <div class="product-title">商品：{{ currentPageConfig.product.name }}</div>
          <div class="product-status">{{ currentPageConfig.product.status }}</div>
        </div>
      </div>
    </div>

    <!-- 视频发布按钮 -->
    <div class="video-section">
      <button class="video-btn">{{ currentPageConfig.video.buttonText }}</button>
    </div>

    <!-- 平台图标区域 -->
    <div class="platforms-section">
      <div 
        v-for="platform in currentPageConfig.platforms"
        :key="platform.id"
        class="platform-item"
        :class="{ 'disabled': !platform.enabled }"
      >
        <div class="platform-icon" :style="{ backgroundColor: platform.color }">
          <IconDisplay :icon="platform.icon" size="24px" :color="platform.color" />
        </div>
        <div class="platform-label">{{ platform.label }}</div>
      </div>
    </div>

    <!-- 朋友圈图文和视频区域 -->
    <div class="friend-circle-section">
      <div 
        v-for="item in currentPageConfig.friendCircle"
        :key="item.id"
        class="friend-circle-item"
        :class="{ 'disabled': !item.enabled }"
      >
        <div class="friend-circle-icon" :style="{ backgroundColor: item.color }">
          <IconDisplay :icon="item.icon" size="20px" :color="item.color" />
        </div>
        <div class="friend-circle-label">{{ item.label }}</div>
      </div>
    </div>

    <!-- 小程序链接区域 -->
    <div class="mini-program-section">
      <h3 class="section-title">{{ currentPageConfig.miniProgram.title }}</h3>
      <div class="mini-program-items">
        <div 
          v-for="item in currentPageConfig.miniProgram.items"
          :key="item.id"
          class="mini-program-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <IconDisplay :icon="item.icon" size="40px" class="mini-program-icon" />
          <span class="mini-program-label">{{ item.label }}</span>
        </div>
      </div>
    </div>

    <!-- 营销私域区域 -->
    <div class="marketing-section">
      <h3 class="section-title">{{ currentPageConfig.marketing.title }}</h3>
      <div class="marketing-items">
        <div 
          v-for="item in currentPageConfig.marketing.items"
          :key="item.id"
          class="marketing-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <div class="marketing-icon" :style="{ backgroundColor: item.color }">
            <IconDisplay :icon="item.icon" size="20px" :color="item.color" />
          </div>
          <div class="marketing-label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- WiFi连接区域 -->
    <div v-if="currentPageConfig.wifi.enabled" class="wifi-section">
      <div class="wifi-card">
        <div class="wifi-info">
          <p class="wifi-name">WiFi: {{ currentPageConfig.wifi.name }}</p>
          <p class="wifi-password">密码: {{ currentPageConfig.wifi.password }}</p>
        </div>
        <button class="wifi-btn">{{ currentPageConfig.wifi.buttonText }}</button>
      </div>
    </div>

    <!-- 点评入口区域 -->
    <div class="review-section">
      <h3 class="section-title">{{ currentPageConfig.review.title }}</h3>
      <div class="review-items">
        <div 
          v-for="item in currentPageConfig.review.items"
          :key="item.id"
          class="review-item"
          :class="{ 'disabled': !item.enabled }"
        >
          <div class="review-icon" :style="{ backgroundColor: item.color }">
            <IconDisplay :icon="item.icon" size="20px" :color="item.color" />
          </div>
          <div class="review-label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- 快速导航区域 -->
    <div v-if="currentPageConfig.quickNav.enabled" class="quick-nav-section">
      <h3 class="section-title">{{ currentPageConfig.quickNav.title }}</h3>
      <div class="quick-nav-product">
        <img :src="currentPageConfig.quickNav.product.image" class="quick-nav-image" alt="">
        <div class="quick-nav-info">
          <h4>{{ currentPageConfig.quickNav.product.name }}</h4>
          <p class="product-code">{{ currentPageConfig.quickNav.product.code }}</p>
          <div class="price-info">
            <span class="current-price">{{ currentPageConfig.quickNav.product.currentPrice }}</span>
            <span class="original-price">{{ currentPageConfig.quickNav.product.originalPrice }}</span>
          </div>
          <div class="status-text">{{ currentPageConfig.quickNav.product.statusText }}</div>
        </div>
        <button class="purchase-btn">{{ currentPageConfig.quickNav.product.buttonText }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import IconDisplay from '@/components/IconDisplay.vue'

export default {
  name: 'PromotionPageFixed',
  components: {
    IconDisplay
  },
  props: {
    pageConfig: {
      type: Object,
      default: null
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },
    currentPageConfig() {
      return this.pageConfig || this.defaultConfig
    },
    pageStyles() {
      return {
        background: this.currentPageConfig.background,
        minHeight: '100vh'
      }
    }
  },
  data() {
    return {
      defaultConfig: {
        // 页面样式配置
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        
        // 头部配置
        header: {
          title: '碰一碰，领福利',
          subtitle: '请点击进行操作吧'
        },
        
        // 店铺信息
        store: {
          name: '天府火锅',
          logo: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          promotionText: '爆款团购'
        },
        
        // 商品信息
        product: {
          name: '天府火锅二人餐',
          image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
          status: '热销'
        },
        
        // 视频发布
        video: {
          buttonText: '视频发布'
        },
        
        // 平台配置
        platforms: [
          { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '发抖音', color: '#000000', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '发快手', color: '#ff6600', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书图文', color: '#ff2442', enabled: true },
          { id: 4, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书视频', color: '#ff2442', enabled: true }
        ],
        
        // 朋友圈配置
        friendCircle: [
          { id: 1, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈图文', color: '#33cc33', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈视频', color: '#33cc33', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/shipinhao.png'), label: '发视频号', color: '#07c160', enabled: true }
        ],
        
        // 小程序配置
        miniProgram: {
          title: '自定义链接小程序',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '点餐', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '导航名称', enabled: true }
          ]
        },
        
        // 营销私域
        marketing: {
          title: '营销私域',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/weixin.png'), label: '加微信', color: '#07c160', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/qq.png'), label: '加QQ', color: '#1296db', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/qiye.png'), label: '加企微', color: '#1296db', enabled: true }
          ]
        },
        
        // WiFi配置
        wifi: {
          enabled: true,
          name: 'dajiangjia',
          password: '18300250542',
          buttonText: '一键链接'
        },
        
        // 点评配置
        review: {
          title: '打卡点评团购入口',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyindian.png'), label: '抖音点评', color: '#000000', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/gaodedian.png'), label: '高德点评', color: '#00a6f7', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/baidudian.png'), label: '百度点评', color: '#2196f3', enabled: true },
            { id: 4, icon: require('@/assets/images/platforms/meituandian.png'), label: '美团点评', color: '#ffc107', enabled: true },
            { id: 5, icon: require('@/assets/images/platforms/dazhongdian.png'), label: '大众点评', color: '#ff9800', enabled: true }
          ]
        },
        
        // 快速导航
        quickNav: {
          enabled: true,
          title: '快速导航',
          product: {
            name: '天府',
            code: '已售 11111',
            image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
            currentPrice: '￥11111.00',
            originalPrice: '￥11.00',
            statusText: '立即中',
            buttonText: '立即抢购'
          }
        }
      }
    }
  },
  mounted() {
    this.loadPageConfig()
    
    // 监听localStorage变化，实现配置的实时更新
    this.addStorageListener()
  },
  beforeDestroy() {
    // 清理事件监听器
    this.removeStorageListener()
  },
  methods: {
    // 加载页面配置
    loadPageConfig() {
      // 如果没有传入pageConfig prop，则根据storeId加载对应的页面配置
      if (!this.pageConfig) {
        console.log('加载店铺ID:', this.storeId, '的推广页面配置')

        // 根据storeId定制门店信息
        this.customizeStoreConfig()

        // 尝试从localStorage加载配置
        const configKey = `promotion_config_${this.storeId}`
        const savedConfig = localStorage.getItem(configKey)

        if (savedConfig) {
          try {
            const parsedConfig = JSON.parse(savedConfig)
            // 合并保存的配置到默认配置
            this.defaultConfig = Object.assign({}, this.defaultConfig, parsedConfig)
            console.log('从localStorage加载配置成功:', this.defaultConfig)
          } catch (error) {
            console.error('解析保存的配置失败:', error)
          }
        }

        // 这里可以调用API获取配置数据
        // const config = await this.$api.promotion.getConfig(this.storeId)
        // this.defaultConfig = config
      }
    },

    // 根据门店ID定制配置
    customizeStoreConfig() {
      const storeConfigs = {
        '1': {
          header: {
            title: '星巴克咖啡店',
            subtitle: '碰一碰，享受优质咖啡体验'
          },
          store: {
            name: '星巴克咖啡店',
            logo: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            promotionText: '🔥 新店开业，全场8折优惠'
          },
          product: {
            name: '经典美式咖啡',
            image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=200&fit=crop',
            status: '🔥 热销中'
          },
          wifi: {
            enabled: true,
            name: 'Starbucks_Free_WiFi',
            password: 'coffee2024',
            buttonText: '一键连接WiFi'
          }
        },
        '2': {
          header: {
            title: '麦当劳快餐店',
            subtitle: '碰一碰，享受美味快餐'
          },
          store: {
            name: '麦当劳快餐店',
            logo: 'https://wpimg.wallstcn.com/57ed425a-c71e-4201-9428-68760c0537c4.gif',
            promotionText: '🍟 限时优惠，买一送一'
          },
          product: {
            name: '巨无霸套餐',
            image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop',
            status: '🔥 人气推荐'
          },
          wifi: {
            enabled: true,
            name: 'McDonalds_WiFi',
            password: 'burger123',
            buttonText: '免费WiFi连接'
          }
        }
      }

      const storeConfig = storeConfigs[this.storeId]
      if (storeConfig) {
        // 合并门店特定配置
        this.defaultConfig = {
          ...this.defaultConfig,
          ...storeConfig
        }
      }
    },

    // 返回门店管理页面
    goBack() {
      // 如果是在新窗口中打开的，关闭窗口
      if (window.opener) {
        window.close()
      } else {
        // 否则导航回门店管理页面
        this.$router.push('/storer/store')
      }
    },

    // 进入DIY编辑模式
    goDIY() {
      const diyUrl = `/promotion/${this.storeId}/diy`
      this.$router.push(diyUrl)
    },

    // 保存页面配置
    savePageConfig() {
      // 保存配置到后端
      console.log('保存页面配置:', this.currentPageConfig)
    },
    
    // 添加localStorage监听器
    addStorageListener() {
      this.storageListener = (e) => {
        const configKey = `promotion_config_${this.storeId}`
        if (e.key === configKey && e.newValue) {
          try {
            const newConfig = JSON.parse(e.newValue)
            this.defaultConfig = Object.assign({}, this.defaultConfig, newConfig)
            console.log('检测到配置更新，已自动刷新页面内容')
          } catch (error) {
            console.error('解析更新的配置失败:', error)
          }
        }
      }
      window.addEventListener('storage', this.storageListener)
    },
    
    // 移除localStorage监听器
    removeStorageListener() {
      if (this.storageListener) {
        window.removeEventListener('storage', this.storageListener)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.promotion-page {
  max-width: 375px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: white;
  overflow-x: hidden;
  position: relative;
}

// 返回按钮样式
.back-button-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.back-button {
  background: rgba(0, 0, 0, 0.3);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  i {
    margin-right: 5px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: translateY(-2px);
  }
}

// 头部样式
.header-section {
  text-align: center;
  margin-bottom: 30px;
  
  .main-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }
  
  .sub-title {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }
}

// 店铺信息样式
.store-info-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  
  .store-card {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .store-logo {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-right: 15px;
      border: 2px solid rgba(255, 255, 255, 0.5);
    }
    
    .store-name {
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .promotion-badge {
    background: #ff4757;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    display: inline-block;
  }
}

// 商品展示样式
.product-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  
  .product-card {
    display: flex;
    align-items: center;
    
    .product-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      margin-right: 15px;
      object-fit: cover;
    }
    
    .product-info {
      flex: 1;
      
      .product-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .product-status {
        background: #2ed573;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        display: inline-block;
      }
    }
  }
}

// 视频发布按钮样式
.video-section {
  text-align: center;
  margin-bottom: 30px;
  
  .video-btn {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 30px rgba(255, 107, 107, 0.4);
    }
  }
}

// 平台图标样式
.platforms-section, .friend-circle-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 30px;
  
  .platform-item, .friend-circle-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 15px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .platform-icon, .friend-circle-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      
      .platform-symbol, .friend-circle-symbol {
        font-size: 24px;
        color: white;
      }
    }
    
    .platform-label, .friend-circle-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// 小程序区域样式
.mini-program-section, .marketing-section, .review-section, .quick-nav-section {
  margin-bottom: 30px;
  
  .section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }
}

.mini-program-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  
  .mini-program-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .mini-program-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      margin-bottom: 10px;
      object-fit: cover;
    }
    
    .mini-program-label {
      font-size: 14px;
      font-weight: 500;
      text-align: center;
    }
  }
}

// 营销私域样式
.marketing-items, .review-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  
  .marketing-item, .review-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .marketing-icon, .review-icon {
      width: 45px;
      height: 45px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      
      .marketing-symbol, .review-symbol {
        font-size: 20px;
        color: white;
      }
    }
    
    .marketing-label, .review-label {
      font-size: 12px;
      font-weight: 500;
      text-align: center;
    }
  }
}

// WiFi连接样式
.wifi-section {
  margin-bottom: 30px;
  
  .wifi-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .wifi-info {
      flex: 1;
      
      .wifi-name, .wifi-password {
        margin: 5px 0;
        font-size: 14px;
      }
    }
    
    .wifi-btn {
      background: #2ed573;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #27ae60;
        transform: translateY(-2px);
      }
    }
  }
}

// 快速导航样式
.quick-nav-product {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  
  .quick-nav-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-right: 15px;
    object-fit: cover;
  }
  
  .quick-nav-info {
    flex: 1;
    
    h4 {
      margin: 0 0 5px 0;
      font-size: 18px;
      font-weight: bold;
    }
    
    .product-code {
      font-size: 12px;
      opacity: 0.8;
      margin: 0 0 8px 0;
    }
    
    .price-info {
      margin-bottom: 8px;
      
      .current-price {
        font-size: 20px;
        font-weight: bold;
        color: #ff4757;
        margin-right: 10px;
      }
      
      .original-price {
        font-size: 14px;
        text-decoration: line-through;
        opacity: 0.6;
      }
    }
    
    .status-text {
      font-size: 12px;
      color: #2ed573;
      font-weight: bold;
    }
  }
  
  .purchase-btn {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .promotion-page {
    padding: 15px;
  }
  
  .platforms-section, .friend-circle-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .marketing-items, .review-items {
    grid-template-columns: repeat(2, 1fr);
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.promotion-page > div {
  animation: fadeInUp 0.6s ease-out;
}
</style>
