# 火山引擎Doubao API测试脚本

Write-Host "=== 火山引擎Doubao API连接测试 ===" -ForegroundColor Cyan

# API配置
$apiUrl = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
$apiKey = "5ad57720-913c-410e-b75f-debd2fe836a4z"
$model = "doubao-seed-1-6-flash-250715"

# 构建请求头
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $apiKey"
}

# 构建请求体
$requestBody = @{
    model = $model
    messages = @(
        @{
            role = "system"
            content = "You are a helpful assistant."
        },
        @{
            role = "user"
            content = "你好，请简单介绍一下自己"
        }
    )
    max_tokens = 100
    temperature = 0.7
} | ConvertTo-Json -Depth 3

Write-Host "API地址: $apiUrl" -ForegroundColor Yellow
Write-Host "模型: $model" -ForegroundColor Yellow
Write-Host "API Key前缀: $($apiKey.Substring(0, 10))..." -ForegroundColor Yellow

Write-Host "`n发送请求..." -ForegroundColor Yellow

try {
    # 发送请求
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Headers $headers -Body $requestBody -TimeoutSec 30
    
    Write-Host "✅ API调用成功!" -ForegroundColor Green
    Write-Host "`n响应详情:" -ForegroundColor Cyan
    
    # 检查响应结构
    if ($response.choices -and $response.choices.Count -gt 0) {
        $content = $response.choices[0].message.content
        Write-Host "生成的内容: $content" -ForegroundColor White
        
        # 显示完整响应
        Write-Host "`n完整响应:" -ForegroundColor Cyan
        $response | ConvertTo-Json -Depth 5
        
        Write-Host "`n🎉 火山引擎Doubao接口测试通过!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 响应格式异常" -ForegroundColor Yellow
        $response | ConvertTo-Json -Depth 5
    }
    
} catch {
    Write-Host "❌ API调用失败!" -ForegroundColor Red
    Write-Host "错误类型: $($_.Exception.GetType().Name)" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试获取详细错误信息
    if ($_.Exception.Response) {
        try {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
            
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $errorBody = $reader.ReadToEnd()
            $reader.Close()
            
            Write-Host "错误响应体: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取错误响应详情" -ForegroundColor Red
        }
    }
    
    Write-Host "`n🔧 可能的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 检查API密钥是否正确" -ForegroundColor White
    Write-Host "2. 检查网络连接" -ForegroundColor White
    Write-Host "3. 检查模型名称是否正确" -ForegroundColor White
    Write-Host "4. 检查API配额是否充足" -ForegroundColor White
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
