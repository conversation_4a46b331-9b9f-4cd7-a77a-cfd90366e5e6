# 测试创建文案库
$baseUrl = "http://localhost:8078"

# 创建请求数据
$createData = @{
    libraryName = "TestLibrary_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    useAi = $true
    shopDetails = "Test shop: A cozy coffee shop"
    prompt = "Generate attractive food promotion copy"
    targetCount = 5
    wordCount = 100
}

$jsonData = $createData | ConvertTo-Json

Write-Host "发送请求数据:" -ForegroundColor Yellow
Write-Host $jsonData

Write-Host "`n测试创建文案库..." -ForegroundColor Yellow
try {
    $result = Invoke-RestMethod -Uri "$baseUrl/ai/copywriting/library" -Method POST -ContentType "application/json" -Body $jsonData
    Write-Host "创建成功:" -ForegroundColor Green
    $result | ConvertTo-Json -Depth 3
} catch {
    Write-Host "创建失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
        
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "响应内容: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取响应内容" -ForegroundColor Red
        }
    }
}
