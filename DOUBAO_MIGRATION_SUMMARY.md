# DeepSeek到火山引擎Doubao接口迁移总结

## 概述
本次更新将项目中的DeepSeek-V3接口完全替换为火山引擎的Doubao接口，使用模型 `doubao-seed-1-6-flash-250715`。

## 更改的文件

### 1. 配置文件更新

#### `ruoyi-admin/src/main/resources/application-dev.yml`
- 将 `baidu.ai` 配置节点更改为 `doubao.ai`
- 更新API密钥为: `5ad57720-913c-410e-b75f-debd2fe836a4z`
- 更新API地址为: `https://ark.cn-beijing.volces.com/api/v3/chat/completions`
- 更新模型名称为: `doubao-seed-1-6-flash-250715`

#### `ruoyi-admin/src/main/resources/application.yml`
- 同样将配置从百度智能云更改为火山引擎Doubao
- 保持相同的超时和重试配置

### 2. Java后端文件更新

#### `ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DeepSeekV3DirectService.java`
- 重命名为: `DoubaoDirectService.java`
- 更新类名和注释
- 更新API URL和API Key
- 更新模型名称
- 更新日志信息

#### `ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/BaiduAiTestController.java`
- 重命名为: `DoubaoAiTestController.java`
- 更新服务引用从 `DeepSeekV3DirectService` 到 `DoubaoDirectService`
- 更新测试接口路径和方法名
- 更新日志和响应信息

#### `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/BaiduAiServiceImpl.java`
- 重命名为: `DoubaoAiServiceImpl.java`
- 更新配置注入从 `${baidu.ai.*}` 到 `${doubao.ai.*}`
- 更新类注释和日志信息

### 3. 前端文件更新

#### `ruoyi-ui/src/views/store/dou-backup.vue`
- 更新页面描述从"基于百度智能云DeepSeek V3"到"基于火山引擎Doubao"
- 更新表单提示信息

#### `ruoyi-ui/src/api/ai/copywriting-test.js`
- 更新API测试函数名从 `testDeepSeekDirect` 到 `testDoubaoDirectService`
- 更新API路径从 `/ai/test/test-deepseek-direct` 到 `/ai/test/test-doubao-direct`

### 4. 测试脚本更新

#### `test-api.ps1`
- 更新PowerShell测试脚本
- 将DeepSeek-V3测试更改为火山引擎Doubao测试
- 更新API路径和输出信息

## 新增文件

### `test-doubao-api.java`
- 创建独立的Java测试类
- 用于验证火山引擎Doubao API是否正常工作
- 包含完整的API调用示例

## API接口变更对比

### 原DeepSeek配置:
```yaml
baidu:
  ai:
    api-key: bce-v3/ALTAK-CtTuLtajO5LKkRGbYDeGT/a693564c5d707af25bc7e85463a690b67733fbda
    base-url: https://qianfan.baidubce.com/v2/chat/completions
    model: deepseek-v3
```

### 新Doubao配置:
```yaml
doubao:
  ai:
    api-key: 5ad57720-913c-410e-b75f-debd2fe836a4z
    base-url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
    model: doubao-seed-1-6-flash-250715
```

## 测试接口变更

### 原接口:
- `/ai/test/test-deepseek-direct`

### 新接口:
- `/ai/test/test-doubao-direct`

## 注意事项

1. **API密钥安全**: 新的API密钥已配置，请确保在生产环境中妥善保管
2. **接口兼容性**: 火山引擎Doubao使用OpenAI兼容的API格式，与原DeepSeek接口保持一致
3. **模型参数**: 新模型支持相同的参数配置（max_tokens, temperature等）
4. **错误处理**: 保持原有的错误处理逻辑不变

## 验证步骤

1. 启动应用服务
2. 访问测试接口: `GET /ai/test/test-doubao-direct`
3. 检查日志输出确认API调用成功
4. 运行独立测试类 `TestDoubaoApi` 验证API连通性

## 回滚方案

如需回滚到DeepSeek，请：
1. 恢复配置文件中的 `baidu.ai` 配置节点
2. 将Java类名和文件名改回原来的名称
3. 恢复原API密钥和接口地址
