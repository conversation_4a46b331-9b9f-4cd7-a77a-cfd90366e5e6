package com.ruoyi.web.controller.ai;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AiCopywritingLibrary;
import com.ruoyi.system.domain.AiCopywritingContent;
import com.ruoyi.system.service.IAiCopywritingService;
import com.ruoyi.system.service.IAiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI文案生成Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/copywriting")
public class AiCopywritingController extends BaseController
{
    private final IAiCopywritingService aiCopywritingService;
    private final IAiService aiService;

    /**
     * 查询文案库列表
     */
    @SaCheckPermission("ai:copywriting:list")
    @GetMapping("/library/list")
    public R<List<AiCopywritingLibrary>> libraryList(AiCopywritingLibrary aiCopywritingLibrary)
    {
        List<AiCopywritingLibrary> list = aiCopywritingService.selectLibraryList(aiCopywritingLibrary);
        return R.ok(list);
    }

    /**
     * 查询文案库列表（测试版本，无需权限）
     */
    @SaIgnore
    @GetMapping("/library/list/test")
    public R<List<AiCopywritingLibrary>> libraryListTest(AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            List<AiCopywritingLibrary> list = aiCopywritingService.selectLibraryList(aiCopywritingLibrary);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取文案库详细信息
     */
    @SaCheckPermission("ai:copywriting:query")
    @GetMapping("/library/{libraryId}")
    public R<AiCopywritingLibrary> getLibraryInfo(@PathVariable("libraryId") Long libraryId)
    {
        return R.ok(aiCopywritingService.selectLibraryById(libraryId));
    }

    /**
     * 创建文案库
     */
    @SaCheckPermission("ai:copywriting:add")
    @Log(title = "AI文案库", businessType = BusinessType.INSERT)
    @PostMapping("/library")
    public R<AiCopywritingLibrary> createLibrary(@RequestBody AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            log.info("收到创建文案库请求: {}", aiCopywritingLibrary.getLibraryName());
            int result = aiCopywritingService.createLibrary(aiCopywritingLibrary);
            if (result > 0) {
                log.info("创建文案库成功: {}", aiCopywritingLibrary.getLibraryId());
                return R.ok("创建成功", aiCopywritingLibrary);
            } else {
                log.error("创建文案库失败，result: {}", result);
                return R.fail("创建失败，请检查输入参数");
            }
        } catch (Exception e) {
            log.error("创建文案库异常", e);
            return R.fail("创建失败：" + e.getMessage());
        }
    }

    /**
     * 创建文案库（测试版本，无需权限）
     */
    @SaIgnore
    @Log(title = "AI文案库测试", businessType = BusinessType.INSERT)
    @PostMapping("/library/test")
    public R<AiCopywritingLibrary> createLibraryTest(@RequestBody AiCopywritingLibrary aiCopywritingLibrary)
    {
        try {
            log.info("收到创建文案库测试请求: {}", aiCopywritingLibrary.getLibraryName());
            int result = aiCopywritingService.createLibrary(aiCopywritingLibrary);
            if (result > 0) {
                log.info("创建文案库测试成功: {}", aiCopywritingLibrary.getLibraryId());
                return R.ok("创建成功", aiCopywritingLibrary);
            } else {
                log.error("创建文案库测试失败，result: {}", result);
                return R.fail("创建失败");
            }
        } catch (Exception e) {
            log.error("创建文案库测试异常", e);
            return R.fail("创建失败：" + e.getMessage());
        }
    }

    /**
     * 简单测试接口
     */
    @SaIgnore
    @GetMapping("/test/simple")
    public R<String> simpleTest()
    {
        return R.ok("测试接口正常工作");
    }

    /**
     * 生成AI文案
     */
    @SaCheckPermission("ai:copywriting:generate")
    @Log(title = "AI文案生成", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public R<String> generateCopywriting(@RequestBody AiCopywritingLibrary request)
    {
        try {
            // 异步生成文案
            aiCopywritingService.generateCopywritingAsync(request);
            return R.ok("文案生成任务已启动");
        } catch (Exception e) {
            return R.fail("文案生成失败：" + e.getMessage());
        }
    }

    /**
     * 查询文案库内容列表
     */
    @SaCheckPermission("ai:copywriting:list")
    @GetMapping("/content/list/{libraryId}")
    public R<List<AiCopywritingContent>> contentList(@PathVariable("libraryId") Long libraryId)
    {
        List<AiCopywritingContent> list = aiCopywritingService.selectContentByLibraryId(libraryId);
        return R.ok(list);
    }

    /**
     * 查询文案库内容列表（测试版本，无需权限）
     */
    @SaIgnore
    @GetMapping("/content/list/test/{libraryId}")
    public R<List<AiCopywritingContent>> contentListTest(@PathVariable("libraryId") Long libraryId)
    {
        try {
            List<AiCopywritingContent> list = aiCopywritingService.selectContentByLibraryId(libraryId);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增文案内容
     */
    @SaCheckPermission("ai:copywriting:add")
    @Log(title = "文案内容", businessType = BusinessType.INSERT)
    @PostMapping("/content")
    public R<String> addContent(@RequestBody AiCopywritingContent content)
    {
        try {
            if (content.getUseAi()) {
                // 使用AI生成
                aiCopywritingService.generateSingleContent(content);
                return R.ok("AI文案生成成功");
            } else {
                // 手动添加
                int result = aiCopywritingService.insertContent(content);
                return result > 0 ? R.ok("添加成功") : R.fail("添加失败");
            }
        } catch (Exception e) {
            return R.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 修改文案内容
     */
    @SaCheckPermission("ai:copywriting:edit")
    @Log(title = "文案内容", businessType = BusinessType.UPDATE)
    @PutMapping("/content")
    public R<String> editContent(@RequestBody AiCopywritingContent content)
    {
        int result = aiCopywritingService.updateContent(content);
        return result > 0 ? R.ok("修改成功") : R.fail("修改失败");
    }

    /**
     * 删除文案内容
     */
    @SaCheckPermission("ai:copywriting:remove")
    @Log(title = "文案内容", businessType = BusinessType.DELETE)
    @DeleteMapping("/content/{contentIds}")
    public R<String> removeContent(@PathVariable Long[] contentIds)
    {
        int result = aiCopywritingService.deleteContentByIds(contentIds);
        return result > 0 ? R.ok("删除成功") : R.fail("删除失败");
    }

    /**
     * 删除文案库
     */
    @SaCheckPermission("ai:copywriting:remove")
    @Log(title = "AI文案库", businessType = BusinessType.DELETE)
    @DeleteMapping("/library/{libraryIds}")
    public R<String> removeLibrary(@PathVariable Long[] libraryIds)
    {
        int result = aiCopywritingService.deleteLibraryByIds(libraryIds);
        return result > 0 ? R.ok("删除成功") : R.fail("删除失败");
    }

    /**
     * 获取生成进度
     */
    @GetMapping("/progress/{libraryId}")
    public R<Map<String, Object>> getProgress(@PathVariable("libraryId") Long libraryId)
    {
        return R.ok(aiCopywritingService.getGenerationProgress(libraryId));
    }

    /**
     * 重新生成文案库
     */
    @SaCheckPermission("ai:copywriting:generate")
    @Log(title = "重新生成文案", businessType = BusinessType.UPDATE)
    @PostMapping("/regenerate/{libraryId}")
    public R<String> regenerateLibrary(@PathVariable("libraryId") Long libraryId)
    {
        try {
            aiCopywritingService.regenerateLibrary(libraryId);
            return R.ok("重新生成任务已启动");
        } catch (Exception e) {
            return R.fail("重新生成失败：" + e.getMessage());
        }
    }
}
