<template>
  <div class="config-container">
    <!-- 配置面板 -->
    <div class="config-panel">
      <el-card class="config-card">
        <div slot="header" class="config-header">
          <span>{{ storeName }} - 推广页面配置</span>
          <el-button type="primary" size="small" @click="saveConfig">保存配置</el-button>
        </div>

        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基础设置 -->
          <el-tab-pane label="基础设置" name="basic">
            <el-form :model="pageConfig" label-width="120px">
              <el-form-item label="页面背景">
                <el-color-picker v-model="pageConfig.background" show-alpha />
                <el-input v-model="pageConfig.background" placeholder="或输入CSS背景样式" style="margin-left: 10px; width: 300px;" />
              </el-form-item>
              
              <el-form-item label="主标题">
                <el-input v-model="pageConfig.header.title" placeholder="请输入主标题" />
              </el-form-item>
              
              <el-form-item label="副标题">
                <el-input v-model="pageConfig.header.subtitle" placeholder="请输入副标题" />
              </el-form-item>
              
              <el-form-item label="店铺名称">
                <el-input v-model="pageConfig.store.name" placeholder="请输入店铺名称" />
              </el-form-item>
              
              <el-form-item label="店铺Logo">
                <el-upload
                  class="logo-uploader"
                  :action="uploadImgUrl"
                  :show-file-list="false"
                  :headers="headers"
                  :on-success="handleLogoSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeLogoUpload">
                  <img v-if="pageConfig.store.logo" :src="pageConfig.store.logo" class="logo">
                  <i v-else class="el-icon-plus logo-uploader-icon"></i>
                </el-upload>
              </el-form-item>
              
              <el-form-item label="促销文字">
                <el-input v-model="pageConfig.store.promotionText" placeholder="请输入促销文字" />
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 商品设置 -->
          <el-tab-pane label="商品设置" name="product">
            <el-form :model="pageConfig.product" label-width="120px">
              <el-form-item label="商品名称">
                <el-input v-model="pageConfig.product.name" placeholder="请输入商品名称" />
              </el-form-item>
              
              <el-form-item label="商品图片">
                <el-upload
                  class="product-uploader"
                  :action="uploadImgUrl"
                  :show-file-list="false"
                  :headers="headers"
                  :on-success="handleProductSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeProductUpload">
                  <img v-if="pageConfig.product.image" :src="pageConfig.product.image" class="product-image">
                  <i v-else class="el-icon-plus product-uploader-icon"></i>
                </el-upload>
              </el-form-item>
              
              <el-form-item label="商品状态">
                <el-input v-model="pageConfig.product.status" placeholder="请输入商品状态" />
              </el-form-item>
              
              <el-form-item label="视频按钮文字">
                <el-input v-model="pageConfig.video.buttonText" placeholder="请输入视频按钮文字" />
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 平台配置 -->
          <el-tab-pane label="平台配置" name="platforms">
            <el-row :gutter="20">
              <el-col :span="12">
                <h4>发布平台</h4>
                <div v-for="(platform, index) in pageConfig.platforms" :key="platform.id" class="platform-config-item">
                  <el-switch v-model="platform.enabled" />
                  <el-input v-model="platform.label" placeholder="平台名称" style="width: 120px; margin: 0 10px;" />
                  <div class="icon-selector-wrapper">
                    <IconSelector v-model="platform.icon" />
                  </div>
                  <el-color-picker v-model="platform.color" />
                  <el-button type="danger" size="mini" @click="removePlatform(index)">删除</el-button>
                </div>
                <el-button type="primary" size="small" @click="addPlatform">添加平台</el-button>
              </el-col>
              
              <el-col :span="12">
                <h4>朋友圈平台</h4>
                <div v-for="(item, index) in pageConfig.friendCircle" :key="item.id" class="platform-config-item">
                  <el-switch v-model="item.enabled" />
                  <el-input v-model="item.label" placeholder="平台名称" style="width: 120px; margin: 0 10px;" />
                  <div class="icon-selector-wrapper">
                    <IconSelector v-model="item.icon" />
                  </div>
                  <el-color-picker v-model="item.color" />
                  <el-button type="danger" size="mini" @click="removeFriendCircle(index)">删除</el-button>
                </div>
                <el-button type="primary" size="small" @click="addFriendCircle">添加平台</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 小程序配置 -->
          <el-tab-pane label="小程序配置" name="miniprogram">
            <el-form :model="pageConfig.miniProgram" label-width="120px">
              <el-form-item label="小程序标题">
                <el-input v-model="pageConfig.miniProgram.title" placeholder="请输入小程序标题" />
              </el-form-item>
              
              <h4>小程序项目</h4>
              <div v-for="(item, index) in pageConfig.miniProgram.items" :key="item.id" class="mini-program-item">
                <el-switch v-model="item.enabled" />
                <el-input v-model="item.label" placeholder="项目名称" style="width: 150px; margin: 0 10px;" />
                <div class="icon-selector-wrapper">
                  <IconSelector v-model="item.icon" />
                </div>
                <el-button type="danger" size="mini" @click="removeMiniProgramItem(index)">删除</el-button>
              </div>
              <el-button type="primary" size="small" @click="addMiniProgramItem">添加项目</el-button>
            </el-form>
          </el-tab-pane>

          <!-- 营销配置 -->
          <el-tab-pane label="营销配置" name="marketing">
            <el-form :model="pageConfig.marketing" label-width="120px">
              <el-form-item label="营销标题">
                <el-input v-model="pageConfig.marketing.title" placeholder="请输入营销标题" />
              </el-form-item>
              
              <h4>营销项目</h4>
              <div v-for="(item, index) in pageConfig.marketing.items" :key="item.id" class="marketing-config-item">
                <el-switch v-model="item.enabled" />
                <el-input v-model="item.label" placeholder="项目名称" style="width: 120px; margin: 0 10px;" />
                <div class="icon-selector-wrapper">
                  <IconSelector v-model="item.icon" />
                </div>
                <el-color-picker v-model="item.color" />
                <el-button type="danger" size="mini" @click="removeMarketingItem(index)">删除</el-button>
              </div>
              <el-button type="primary" size="small" @click="addMarketingItem">添加项目</el-button>
            </el-form>
          </el-tab-pane>

          <!-- WiFi配置 -->
          <el-tab-pane label="WiFi配置" name="wifi">
            <el-form :model="pageConfig.wifi" label-width="120px">
              <el-form-item label="启用WiFi">
                <el-switch v-model="pageConfig.wifi.enabled" />
              </el-form-item>
              
              <el-form-item label="WiFi名称">
                <el-input v-model="pageConfig.wifi.name" placeholder="请输入WiFi名称" />
              </el-form-item>
              
              <el-form-item label="WiFi密码">
                <el-input v-model="pageConfig.wifi.password" placeholder="请输入WiFi密码" />
              </el-form-item>
              
              <el-form-item label="连接按钮文字">
                <el-input v-model="pageConfig.wifi.buttonText" placeholder="请输入连接按钮文字" />
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 点评配置 -->
          <el-tab-pane label="点评配置" name="review">
            <el-form :model="pageConfig.review" label-width="120px">
              <el-form-item label="点评标题">
                <el-input v-model="pageConfig.review.title" placeholder="请输入点评标题" />
              </el-form-item>
              
              <h4>点评项目</h4>
              <div v-for="(item, index) in pageConfig.review.items" :key="item.id" class="review-config-item">
                <el-switch v-model="item.enabled" />
                <el-input v-model="item.label" placeholder="项目名称" style="width: 120px; margin: 0 10px;" />
                <div class="icon-selector-wrapper">
                  <IconSelector v-model="item.icon" />
                </div>
                <el-color-picker v-model="item.color" />
                <el-button type="danger" size="mini" @click="removeReviewItem(index)">删除</el-button>
              </div>
              <el-button type="primary" size="small" @click="addReviewItem">添加项目</el-button>
            </el-form>
          </el-tab-pane>

          <!-- 快速导航配置 -->
          <el-tab-pane label="快速导航" name="quicknav">
            <el-form :model="pageConfig.quickNav" label-width="120px">
              <el-form-item label="启用快速导航">
                <el-switch v-model="pageConfig.quickNav.enabled" />
              </el-form-item>
              
              <el-form-item label="导航标题">
                <el-input v-model="pageConfig.quickNav.title" placeholder="请输入导航标题" />
              </el-form-item>
              
              <h4>产品信息</h4>
              <el-form :model="pageConfig.quickNav.product" label-width="120px">
                <el-form-item label="产品名称">
                  <el-input v-model="pageConfig.quickNav.product.name" placeholder="请输入产品名称" />
                </el-form-item>
                
                <el-form-item label="产品编码">
                  <el-input v-model="pageConfig.quickNav.product.code" placeholder="请输入产品编码" />
                </el-form-item>
                
                <el-form-item label="产品图片">
                  <el-upload
                    class="product-uploader"
                    :action="uploadImgUrl"
                    :show-file-list="false"
                    :headers="headers"
                    :on-success="handleQuickNavProductSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeProductUpload">
                    <img v-if="pageConfig.quickNav.product.image" :src="pageConfig.quickNav.product.image" class="product-image">
                    <i v-else class="el-icon-plus product-uploader-icon"></i>
                  </el-upload>
                </el-form-item>
                
                <el-form-item label="现价">
                  <el-input v-model="pageConfig.quickNav.product.currentPrice" placeholder="请输入现价" />
                </el-form-item>
                
                <el-form-item label="原价">
                  <el-input v-model="pageConfig.quickNav.product.originalPrice" placeholder="请输入原价" />
                </el-form-item>
                
                <el-form-item label="状态文字">
                  <el-input v-model="pageConfig.quickNav.product.statusText" placeholder="请输入状态文字" />
                </el-form-item>
                
                <el-form-item label="按钮文字">
                  <el-input v-model="pageConfig.quickNav.product.buttonText" placeholder="请输入按钮文字" />
                </el-form-item>
              </el-form>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 预览面板 -->
    <div class="preview-panel">
      <div class="preview-header">
        <h3>实时预览</h3>
        <div class="preview-actions">
          <el-button size="small" @click="refreshPreview">刷新预览</el-button>
          <el-button type="success" size="small" @click="openInNewWindow">新窗口预览</el-button>
        </div>
      </div>
      <div class="preview-container">
        <div class="phone-frame">
          <PromotionPage :store-id="storeId" :page-config="pageConfig" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PromotionPage from './PromotionPage.vue'
import IconSelector from '@/components/IconSelector.vue'
import { getToken } from '@/utils/auth'

export default {
  name: 'PromotionPageConfig',
  components: {
    PromotionPage,
    IconSelector
  },
  props: {
    storeId: {
      type: [String, Number],
      default() {
        return this.$route.query.storeId || 1
      }
    },
    storeName: {
      type: String,
      default() {
        return this.$route.query.storeName || '门店'
      }
    }
  },
  data() {
    return {
      activeTab: 'basic',
      // 上传配置
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/oss/upload",
      localUploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      currentUploadType: 'logo', // 跟踪当前上传的字段类型
      pageConfig: {
        // 默认配置，与PromotionPage中的配置保持一致
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        header: {
          title: '碰一碰，领福利',
          subtitle: '请点击进行操作吧'
        },
        store: {
          name: '天府火锅',
          logo: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          promotionText: '爆款团购'
        },
        product: {
          name: '天府火锅二人餐',
          image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
          status: '热销'
        },
        video: {
          buttonText: '视频发布'
        },
        platforms: [
          { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '发抖音', color: '#000000', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '发快手', color: '#ff6600', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书图文', color: '#ff2442', enabled: true },
          { id: 4, icon: require('@/assets/images/platforms/xiaohongshu.png'), label: '小红书视频', color: '#ff2442', enabled: true }
        ],
        friendCircle: [
          { id: 1, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈图文', color: '#33cc33', enabled: true },
          { id: 2, icon: require('@/assets/images/platforms/pengyouquan.png'), label: '朋友圈视频', color: '#33cc33', enabled: true },
          { id: 3, icon: require('@/assets/images/platforms/shipinhao.png'), label: '发视频号', color: '#07c160', enabled: true }
        ],
        miniProgram: {
          title: '自定义链接小程序',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyin.png'), label: '点餐', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/kuaishou.png'), label: '导航名称', enabled: true }
          ]
        },
        marketing: {
          title: '营销私域',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/weixin.png'), label: '加微信', color: '#07c160', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/qq.png'), label: '加QQ', color: '#1296db', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/qiye.png'), label: '加企微', color: '#1296db', enabled: true }
          ]
        },
        wifi: {
          enabled: true,
          name: 'dajiangjia',
          password: '18300250542',
          buttonText: '一键链接'
        },
        review: {
          title: '打卡点评团购入口',
          items: [
            { id: 1, icon: require('@/assets/images/platforms/douyindian.png'), label: '抖音点评', color: '#000000', enabled: true },
            { id: 2, icon: require('@/assets/images/platforms/gaodedian.png'), label: '高德点评', color: '#00a6f7', enabled: true },
            { id: 3, icon: require('@/assets/images/platforms/baidudian.png'), label: '百度点评', color: '#2196f3', enabled: true },
            { id: 4, icon: require('@/assets/images/platforms/meituandian.png'), label: '美团点评', color: '#ffc107', enabled: true },
            { id: 5, icon: require('@/assets/images/platforms/dazhongdian.png'), label: '大众点评', color: '#ff9800', enabled: true }
          ]
        },
        quickNav: {
          enabled: true,
          title: '快速导航',
          product: {
            name: '天府',
            code: '已售 11111',
            image: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
            currentPrice: '￥11111.00',
            originalPrice: '￥11.00',
            statusText: '立即中',
            buttonText: '立即抢购'
          }
        }
      }
    }
  },
  computed: {
    // 动态获取请求头，确保token是最新的
    headers() {
      return {
        Authorization: "Bearer " + getToken()
      }
    }
  },
  mounted() {
    this.loadConfig()
    this.checkAuth()
  },
  methods: {
    // 检查认证状态
    checkAuth() {
      const token = getToken()
      console.log('当前token:', token)
      console.log('当前用户信息:', this.$store.getters.userInfo)
      
      if (!token) {
        this.$message.warning('未检测到登录token，请重新登录！')
        console.warn('未找到认证token')
      } else {
        console.log('认证token有效，上传地址:', this.uploadImgUrl)
        console.log('请求头:', this.headers)
      }
    },
    
    // 加载配置
    loadConfig() {
      // 从localStorage加载店铺的推广页面配置
      const configKey = `promotion_config_${this.storeId}`
      const savedConfig = localStorage.getItem(configKey)
      
      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig)
          // 合并保存的配置到默认配置
          this.pageConfig = Object.assign({}, this.pageConfig, parsedConfig)
          console.log('加载已保存的配置:', this.pageConfig)
        } catch (error) {
          console.error('解析配置失败:', error)
        }
      }
      
      console.log('加载店铺配置:', this.storeId)
    },
    
    // 保存配置
    saveConfig() {
      // 保存配置到localStorage实现临时存储
      const configKey = `promotion_config_${this.storeId}`
      localStorage.setItem(configKey, JSON.stringify(this.pageConfig))
      
      // 保存配置到后端
      console.log('保存配置:', this.pageConfig)
      this.$message.success('配置保存成功！')
      
      // 刷新预览
      this.refreshPreview()
    },
    
    // 刷新预览
    refreshPreview() {
      this.$forceUpdate()
    },
    
    // 新窗口预览
    openInNewWindow() {
      const url = `/promotion/${this.storeId}`
      window.open(url, '_blank')
    },
    
    // 处理Logo上传成功
    handleLogoSuccess(res, file) {
      console.log('Logo上传响应:', res)
      if (res && res.code === 200 && res.url) {
        this.pageConfig.store.logo = res.url
        this.$message.success('Logo上传成功！')
      } else {
        console.error('Logo上传响应格式异常:', res)
        this.$message.error(res.msg || 'Logo上传失败，响应格式异常！')
      }
    },
    
    // 处理商品图片上传成功
    handleProductSuccess(res, file) {
      console.log('商品图片上传响应:', res)
      if (res && res.code === 200 && res.url) {
        this.pageConfig.product.image = res.url
        this.$message.success('商品图片上传成功！')
      } else {
        console.error('商品图片上传响应格式异常:', res)
        this.$message.error(res.msg || '商品图片上传失败，响应格式异常！')
      }
    },
    
    // 处理快速导航商品图片上传成功
    handleQuickNavProductSuccess(res, file) {
      console.log('快速导航商品图片上传响应:', res)
      if (res && res.code === 200 && res.url) {
        this.pageConfig.quickNav.product.image = res.url
        this.$message.success('商品图片上传成功！')
      } else {
        console.error('快速导航商品图片上传响应格式异常:', res)
        this.$message.error(res.msg || '商品图片上传失败，响应格式异常！')
      }
    },
    
    // 处理小程序图标上传成功
    // 处理小程序图标上传成功
    handleMiniProgramIconSuccess(res, index) {
      if (res.code === 200) {
        this.pageConfig.miniProgram.items[index].icon = res.url
        this.$message.success('图标上传成功！')
      } else {
        this.$message.error(res.msg || '图标上传失败！')
      }
    },
    
    // 上传前验证
    beforeLogoUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    
    beforeProductUpload(file) {
      return this.beforeLogoUpload(file)
    },
    
    beforeIconUpload(file) {
      return this.beforeLogoUpload(file)
    },
    
    // 上传错误处理 - 增强版，包含本地上传回退
    handleUploadError(err, file, fileList) {
      console.error('上传错误详情:', err)
      console.error('文件信息:', file)
      console.error('当前token:', this.$store.getters.token)
      console.error('上传URL:', this.uploadImgUrl)
      
      // 检查是否是OSS服务类型无法找到的错误
      let errorMessage = ''
      if (err.response && err.response.data) {
        errorMessage = err.response.data.msg || err.response.data.message || ''
      } else if (err.message) {
        errorMessage = err.message
      }
      
      if (errorMessage.includes('文件存储服务类型无法找到') || errorMessage.includes('OSS')) {
        console.log('检测到OSS配置缺失，尝试使用本地上传...')
        this.tryLocalUpload(file)
        return
      }
      
      if (err.response) {
        console.error('响应状态:', err.response.status)
        console.error('响应数据:', err.response.data)
        
        if (err.response.status === 401) {
          this.$message.error('认证失败，请重新登录！')
          // 可以在这里触发重新登录
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/login'
          })
        } else {
          this.$message.error(`上传失败：${err.response.data.msg || err.message}`)
        }
      } else {
        this.$message.error('上传失败，请检查网络连接！')
      }
    },
    
    // 添加平台
    addPlatform() {
      const newId = Math.max(...this.pageConfig.platforms.map(p => p.id)) + 1
      this.pageConfig.platforms.push({
        id: newId,
        icon: require('@/assets/images/platforms/douyin.png'),
        label: '新平台',
        color: '#409eff',
        enabled: true
      })
    },
    
    // 删除平台
    removePlatform(index) {
      this.pageConfig.platforms.splice(index, 1)
    },
    
    // 添加朋友圈项目
    addFriendCircle() {
      const newId = Math.max(...this.pageConfig.friendCircle.map(p => p.id)) + 1
      this.pageConfig.friendCircle.push({
        id: newId,
        icon: require('@/assets/images/platforms/pengyouquan.png'),
        label: '新项目',
        color: '#67c23a',
        enabled: true
      })
    },
    
    // 删除朋友圈项目
    removeFriendCircle(index) {
      this.pageConfig.friendCircle.splice(index, 1)
    },
    
    // 添加小程序项目
    addMiniProgramItem() {
      const newId = Math.max(...this.pageConfig.miniProgram.items.map(p => p.id)) + 1
      this.pageConfig.miniProgram.items.push({
        id: newId,
        icon: require('@/assets/images/platforms/douyin.png'),
        label: '新项目',
        enabled: true
      })
    },
    
    // 删除小程序项目
    removeMiniProgramItem(index) {
      this.pageConfig.miniProgram.items.splice(index, 1)
    },
    
    // 添加营销项目
    addMarketingItem() {
      const newId = Math.max(...this.pageConfig.marketing.items.map(p => p.id)) + 1
      this.pageConfig.marketing.items.push({
        id: newId,
        icon: require('@/assets/images/platforms/weixin.png'),
        label: '新项目',
        color: '#e6a23c',
        enabled: true
      })
    },
    
    // 删除营销项目
    removeMarketingItem(index) {
      this.pageConfig.marketing.items.splice(index, 1)
    },
    
    // 添加点评项目
    addReviewItem() {
      const newId = Math.max(...this.pageConfig.review.items.map(p => p.id)) + 1
      this.pageConfig.review.items.push({
        id: newId,
        icon: require('@/assets/images/platforms/douyindian.png'),
        label: '新点评',
        color: '#f56c6c',
        enabled: true
      })
    },
    
    // 删除点评项目
    removeReviewItem(index) {
      this.pageConfig.review.items.splice(index, 1)
    },

    // 本地上传回退方法
    tryLocalUpload(file) {
      console.log('尝试本地上传，文件:', file.name)
      this.$message.info('OSS服务不可用，正在尝试本地上传...')
      
      const formData = new FormData()
      formData.append('file', file)
      
      // 使用axios进行本地上传
      this.$http({
        url: this.localUploadUrl,
        method: 'post',
        data: formData,
        headers: {
          ...this.headers,
          'Content-Type': 'multipart/form-data'
        }
      }).then(response => {
        console.log('本地上传成功:', response.data)
        if (response.data && response.data.code === 200) {
          const imageUrl = response.data.url || response.data.data
          this.handleLocalUploadSuccess(imageUrl)
          this.$message.success('图片上传成功！')
        } else {
          this.$message.error(response.data.msg || '本地上传失败！')
        }
      }).catch(error => {
        console.error('本地上传失败:', error)
        this.$message.error('图片上传失败，请检查网络连接！')
      })
    },

    // 处理本地上传成功
    handleLocalUploadSuccess(imageUrl) {
      console.log('处理本地上传成功，URL:', imageUrl, '当前上传类型:', this.currentUploadType)
      
      // 根据当前上传类型设置对应的字段
      switch (this.currentUploadType) {
        case 'logo':
          this.pageConfig.store.logo = imageUrl
          break
        case 'product':
          this.pageConfig.product.image = imageUrl
          break
        case 'icon':
          // 如果是图标上传，这里需要根据具体的业务逻辑来处理
          console.log('图标上传成功，可能需要更新特定的图标字段')
          break
        default:
          console.warn('未知的上传类型:', this.currentUploadType)
      }
    },

    // 上传前处理 - 设置上传类型
    beforeLogoUpload(file) {
      this.currentUploadType = 'logo'
      return this.beforeUpload(file)
    },

    beforeProductUpload(file) {
      this.currentUploadType = 'product'
      return this.beforeUpload(file)
    },

    beforeIconUpload(file) {
      this.currentUploadType = 'icon'
      return this.beforeUpload(file)
    },

    // 通用的上传前检查
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  display: flex;
  height: calc(100vh - 84px);
  background: #f5f5f5;
}

.config-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  
  .config-card {
    height: 100%;
    
    .config-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.preview-panel {
  width: 400px;
  background: #fff;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  
  .preview-header {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      color: #303133;
    }
  }
  
  .preview-container {
    flex: 1;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    overflow-y: auto;
    
    .phone-frame {
      width: 375px;
      border: 8px solid #333;
      border-radius: 20px;
      background: #333;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      overflow: hidden;
    }
  }
}

// 配置项样式
.platform-config-item,
.marketing-config-item,
.review-config-item,
.mini-program-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  flex-wrap: wrap;
  gap: 8px;
}

.icon-selector-wrapper {
  margin: 0 8px;
}

// 上传组件样式
.logo-uploader,
.product-uploader,
.icon-uploader {
  .logo,
  .product-image {
    width: 78px;
    height: 78px;
    display: block;
    border-radius: 4px;
    object-fit: cover;
  }
  
  .mini-icon {
    width: 40px;
    height: 40px;
    display: block;
    border-radius: 4px;
    object-fit: cover;
  }
}

.logo-uploader-icon,
.product-uploader-icon,
.mini-icon-uploader {
  font-size: 28px;
  color: #8c939d;
  width: 78px;
  height: 78px;
  line-height: 78px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:hover {
    border-color: #409eff;
  }
}

.mini-icon-uploader {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}
</style>
