<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 工程项目进度及成本控制管理系统 - 自动更新</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4a5568;
            font-size: 2.2rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .status-card {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #e2e8f0;
        }
        
        .status-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .status-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .status-detail {
            color: #718096;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .update-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 180px;
        }
        
        .update-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
        }
        
        .update-btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            width: 100%;
            height: 50px;
            background: #ffff00 !important;
            border-radius: 25px;
            overflow: hidden;
            margin: 30px 0;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 20px rgba(255,0,0,0.5);
            transition: all 0.3s ease;
            border: 5px solid #ff0000 !important;
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            z-index: 1000;
        }
        
        .progress-bar.show {
            display: block !important;
            opacity: 1;
            visibility: visible !important;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000 0%, #ff6600 100%) !important;
            border-radius: 20px;
            transition: width 0.5s ease;
            width: 30%;
            position: relative;
            overflow: hidden;
            display: block !important;
            min-width: 20px;
            animation: glow 2s infinite alternate;
        }
        
        @keyframes glow {
            0% { box-shadow: 0 0 10px #ff0000; }
            100% { box-shadow: 0 0 30px #ff0000; }
        }
        
        .progress-fill::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000000 !important;
            font-weight: bold !important;
            font-size: 18px !important;
            text-shadow: 2px 2px 4px rgba(255,255,255,0.8);
            z-index: 10;
            background: rgba(255,255,255,0.9);
            padding: 4px 8px;
            border-radius: 6px;
            border: 2px solid #000000;
        }
        
        .progress-stage {
            text-align: center;
            margin-top: 15px;
            color: #ffffff !important;
            font-size: 18px !important;
            font-weight: bold !important;
            background: #ff0000 !important;
            padding: 15px 20px;
            border-radius: 10px;
            border: 3px solid #000000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: flash 1s infinite alternate;
        }
        
        @keyframes flash {
            0% { background: #ff0000 !important; }
            100% { background: #ff6600 !important; }
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            line-height: 1.4;
        }
        
        .log-line {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success {
            color: #68d391;
        }
        
        .log-error {
            color: #fc8181;
        }
        
        .log-warning {
            color: #f6e05e;
        }
        
        .log-info {
            color: #63b3ed;
        }
        
        .version-info {
            background: #edf2f7;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
        }
        
        .version-info h3 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .version-info p {
            color: #4a5568;
            margin: 5px 0;
            font-size: 0.95rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 系统自动更新</h1>
            <p>工程项目进度及成本控制管理系统</p>
        </div>
        
        <div class="status-card" id="statusCard">
            <div class="status-icon" id="statusIcon">🔄</div>
            <div class="status-text" id="statusText">正在检查系统状态...</div>
            <div class="status-detail" id="statusDetail">请稍候，正在获取系统信息</div>
        </div>
        
        <div class="version-info" id="versionInfo" style="display: none;">
            <h3>📊 版本信息</h3>
            <p><strong>当前版本：</strong><span id="currentVersion">检测中...</span></p>
            <p><strong>最新版本：</strong><span id="latestVersion">检测中...</span></p>
            <p><strong>最后更新：</strong><span id="lastUpdate">检测中...</span></p>
        </div>
        
        <div class="progress-bar show" id="progressBar" style="display: block !important; visibility: visible !important; opacity: 1 !important; border: 5px solid #ff0000 !important; background: #ffff00 !important;">
            <div class="progress-fill" id="progressFill" style="width: 30%; background: linear-gradient(90deg, #ff0000 0%, #ff6600 100%) !important;"></div>
            <div class="progress-text" id="progressText" style="color: #000000 !important; font-size: 16px !important; font-weight: bold !important;">30%</div>
        </div>
        <div class="progress-stage" id="progressStage" style="background: #ff0000 !important; color: #ffffff !important; font-size: 18px !important; font-weight: bold !important; padding: 15px !important;">🚀 超级醒目进度条 - 如果您看到这个，说明进度条正常显示！</div>
        
        <div style="margin: 20px 0;">
            <button class="update-btn" id="updateBtn" onclick="startUpdate()" disabled>
                <span id="updateBtnText">🔄 检查更新</span>
            </button>
            <br>
            <button class="update-btn refresh-btn" onclick="checkStatus()">
                🔄 刷新状态
            </button>
            <br>
            <button class="update-btn" onclick="testProgress()" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); margin-top: 10px;">
                🧪 测试进度条
            </button>
        </div>
        
        <div class="log-container" id="logContainer">
            <div id="logContent"></div>
        </div>
        
        <div class="footer">
            <p>💡 更新完成后，系统将自动重启以应用更改</p>
            <p>🔒 更新过程中请不要关闭此页面</p>
        </div>
    </div>

    <script>
        let updateInProgress = false;
        let progressInterval;
        let logCheckInterval;
        
        // 页面加载时检查状态
        window.onload = function() {
            console.log('🔄 页面加载完成，初始化进度条');
            
            // 立即显示进度条
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressStage = document.getElementById('progressStage');
            
            if (progressBar && progressFill && progressText && progressStage) {
                // 强制显示进度条
                progressBar.style.display = 'block';
                progressBar.style.visibility = 'visible';
                progressBar.style.opacity = '1';
                progressBar.classList.add('show');
                
                // 立即设置一个测试进度
                updateProgress(15, '🎯 系统已加载，进度条正常显示');
                console.log('✅ 进度条已显示');
            } else {
                console.error('❌ 进度条元素未找到');
            }
            
            // 然后检查系统状态
            setTimeout(() => {
                checkStatus();
            }, 500);
        };
        
        // DOM加载完成后立即执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM已加载完成');
            
            // 检查关键元素是否存在
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressStage = document.getElementById('progressStage');
            
            if (!progressBar || !progressFill || !progressText || !progressStage) {
                console.error('关键元素缺失:', {
                    progressBar: !!progressBar,
                    progressFill: !!progressFill,
                    progressText: !!progressText,
                    progressStage: !!progressStage
                });
                return;
            }
            
            console.log('✅ 所有进度条元素已就绪');
            
            // 立即显示进度条以确保可见
            updateProgress(10, '🚀 系统已就绪');
        });
        
        // 检查系统状态
        async function checkStatus() {
            try {
                updateStatus('🔄', '正在检查系统状态...', '获取系统版本和更新信息', 'info');
                
                const response = await fetch('/system/update/status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 200) {
                        updateVersionInfo(data.data);
                        if (data.data.hasUpdate) {
                            updateStatus('⬆️', '发现新版本！', '点击下方按钮开始更新', 'warning');
                            document.getElementById('updateBtnText').textContent = '🚀 开始更新';
                        } else {
                            updateStatus('✅', '系统已是最新版本', '当前版本已是最新，无需更新', 'success');
                            document.getElementById('updateBtnText').textContent = '🔄 强制更新';
                        }
                        document.getElementById('updateBtn').disabled = false;
                    } else {
                        updateStatus('❌', '状态检查失败', data.msg || '无法获取系统状态', 'error');
                    }
                } else {
                    updateStatus('❌', '连接失败', '无法连接到更新服务，请检查网络连接', 'error');
                }
            } catch (error) {
                console.error('Status check error:', error);
                updateStatus('❌', '检查失败', '网络错误或服务不可用', 'error');
            }
        }
        
        // 开始更新
        async function startUpdate() {
            if (updateInProgress) return;
            
            console.log('开始更新流程'); // 调试日志
            updateInProgress = true;
            document.getElementById('updateBtn').disabled = true;
            document.getElementById('updateBtnText').innerHTML = '<span class="spinner">🔄</span> 更新中...';
            
            // 立即显示进度条和日志
            showProgress();
            showLog();
            addLog('开始系统更新...', 'info');
            
            // 立即开始进度动画
            startProgressAnimation();
            
            try {
                const response = await fetch('/system/update/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 200) {
                        updateStatus('🔄', '更新进行中...', '正在执行更新操作，请耐心等待', 'info');
                        addLog('更新请求发送成功，开始监控进度...', 'info');
                        startLogMonitoring();
                    } else {
                        addLog('更新失败: ' + (data.msg || '未知错误'), 'error');
                        updateStatus('❌', '更新失败', data.msg || '更新过程中发生错误', 'error');
                        resetUpdateState();
                    }
                } else {
                    addLog('请求失败: HTTP ' + response.status, 'error');
                    updateStatus('❌', '请求失败', '无法启动更新进程', 'error');
                    resetUpdateState();
                }
            } catch (error) {
                console.error('Update error:', error);
                addLog('网络错误: ' + error.message, 'error');
                updateStatus('❌', '网络错误', '无法连接到更新服务', 'error');
                resetUpdateState();
            }
        }
        
        // 监控更新日志
        function startLogMonitoring() {
            logCheckInterval = setInterval(async () => {
                try {
                    const response = await fetch('/system/update/progress');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.code === 200) {
                            if (data.data.completed) {
                                clearInterval(progressInterval);
                                clearInterval(logCheckInterval);
                                
                                if (data.data.success) {
                                    updateProgress(100, '✅ 更新完成！');
                                    updateStatus('✅', '更新完成！', '系统更新成功，即将自动重启', 'success');
                                    addLog('更新完成，系统将在5秒后重启...', 'success');
                                    setTimeout(() => {
                                        addLog('正在重启系统...', 'info');
                                        window.location.reload();
                                    }, 5000);
                                } else {
                                    updateProgress(100, '❌ 更新失败');
                                    updateStatus('❌', '更新失败', data.data.message || '更新过程中发生错误', 'error');
                                    addLog('更新失败: ' + (data.data.message || '未知错误'), 'error');
                                }
                                resetUpdateState();
                            } else {
                                // 更新进行中，显示真实进度
                                if (data.data.progress !== undefined) {
                                    clearInterval(progressInterval); // 停止模拟进度
                                    
                                    let stage = '🔄 正在执行更新...';
                                    let progress = data.data.progress;
                                    
                                    // 根据状态显示不同阶段
                                    if (data.data.status === 'starting') {
                                        stage = '🚀 启动更新进程...';
                                    } else if (data.data.status === 'pulling') {
                                        stage = '📥 正在拉取最新代码...';
                                    } else if (data.data.status === 'building-backend') {
                                        stage = '🏗️ 正在编译后端代码...';
                                    } else if (data.data.status === 'building-frontend') {
                                        stage = '🎨 正在构建前端资源...';
                                    } else if (data.data.status === 'completed') {
                                        stage = '✅ 更新即将完成...';
                                    }
                                    
                                    updateProgress(progress, stage);
                                }
                                
                                // 显示日志
                                if (data.data.logs && data.data.logs.length > 0) {
                                    data.data.logs.forEach(log => addLog(log, 'info'));
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('Log monitoring error:', error);
                }
            }, 2000);
        }
        
        // 更新状态显示
        function updateStatus(icon, text, detail, type) {
            document.getElementById('statusIcon').textContent = icon;
            document.getElementById('statusText').textContent = text;
            document.getElementById('statusDetail').textContent = detail;
            
            const statusCard = document.getElementById('statusCard');
            statusCard.className = 'status-card';
            if (type === 'success') {
                statusCard.style.borderColor = '#68d391';
                statusCard.style.backgroundColor = '#f0fff4';
            } else if (type === 'error') {
                statusCard.style.borderColor = '#fc8181';
                statusCard.style.backgroundColor = '#fff5f5';
            } else if (type === 'warning') {
                statusCard.style.borderColor = '#f6e05e';
                statusCard.style.backgroundColor = '#fffff0';
            } else {
                statusCard.style.borderColor = '#63b3ed';
                statusCard.style.backgroundColor = '#f0f9ff';
            }
        }
        
        // 更新版本信息
        function updateVersionInfo(data) {
            document.getElementById('currentVersion').textContent = data.currentVersion || '未知';
            document.getElementById('latestVersion').textContent = data.latestVersion || '未知';
            document.getElementById('lastUpdate').textContent = data.lastUpdate || '未知';
            document.getElementById('versionInfo').style.display = 'block';
        }
        
        // 显示进度条
        function showProgress() {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.display = 'block';
            progressBar.style.visibility = 'visible';
            progressBar.classList.add('show');
            console.log('✅ 进度条已显示'); // 调试日志
            updateProgress(5, '🚀 初始化更新进程...');
        }
        
        // 更新进度
        function updateProgress(percent, stage = '') {
            try {
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                const progressStage = document.getElementById('progressStage');
                
                // 检查元素是否存在
                if (!progressFill || !progressText || !progressStage) {
                    console.error('进度条元素不存在');
                    return;
                }
                
                // 确保进度在0-100之间
                percent = Math.max(0, Math.min(100, percent));
                
                console.log(`更新进度: ${percent}%, 阶段: ${stage}`); // 调试日志
                
                progressFill.style.width = percent + '%';
                progressText.textContent = Math.round(percent) + '%';
                
                if (stage) {
                    progressStage.textContent = stage;
                }
                
                // 根据进度改变颜色
                if (percent < 25) {
                    progressFill.style.background = 'linear-gradient(90deg, #fc8181 0%, #f56565 100%)';
                } else if (percent < 50) {
                    progressFill.style.background = 'linear-gradient(90deg, #f6e05e 0%, #ecc94b 100%)';
                } else if (percent < 75) {
                    progressFill.style.background = 'linear-gradient(90deg, #63b3ed 0%, #4299e1 100%)';
                } else {
                    progressFill.style.background = 'linear-gradient(90deg, #68d391 0%, #48bb78 100%)';
                }
            } catch (error) {
                console.error('更新进度时出错:', error);
            }
        }
        
        // 开始进度动画（改进版）
        function startProgressAnimation() {
            let progress = 0;
            let stage = '正在初始化...';
            
            progressInterval = setInterval(() => {
                // 模拟进度增长，但不超过90%，等待真实进度
                if (progress < 20) {
                    progress += Math.random() * 5;
                    stage = '🔍 检查系统状态...';
                } else if (progress < 40) {
                    progress += Math.random() * 3;
                    stage = '📥 准备更新资源...';
                } else if (progress < 60) {
                    progress += Math.random() * 2;
                    stage = '⏳ 等待服务器响应...';
                } else if (progress < 85) {
                    progress += Math.random() * 1;
                    stage = '🔄 正在处理更新...';
                } else {
                    // 在85%处等待真实进度
                    stage = '⏳ 等待更新完成...';
                }
                
                updateProgress(Math.min(progress, 85), stage);
            }, 1500);
        }
        
        // 显示日志
        function showLog() {
            const logContainer = document.getElementById('logContainer');
            logContainer.style.display = 'block';
            console.log('显示日志容器'); // 调试日志
        }
        
        // 添加日志
        function addLog(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const logLine = document.createElement('div');
            logLine.className = 'log-line log-' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            logLine.textContent = `[${timestamp}] ${message}`;
            
            logContent.appendChild(logLine);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        // 重置更新状态
        function resetUpdateState() {
            updateInProgress = false;
            document.getElementById('updateBtn').disabled = false;
            document.getElementById('updateBtnText').textContent = '🔄 重新更新';
            
            // 重置进度条 - 延迟隐藏以便用户看到最终状态
            setTimeout(() => {
                const progressBar = document.getElementById('progressBar');
                progressBar.style.display = 'none';
                progressBar.classList.remove('show');
                updateProgress(0, '');
                console.log('隐藏进度条'); // 调试日志
            }, 3000); // 3秒后隐藏进度条
            
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            if (logCheckInterval) {
                clearInterval(logCheckInterval);
                logCheckInterval = null;
            }
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            checkStatus();
        };
        
        // 测试进度条功能
        function testProgress() {
            try {
                console.log('🧪 开始测试进度条');
                
                // 检查所有必要元素
                const progressBar = document.getElementById('progressBar');
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                const progressStage = document.getElementById('progressStage');
                const logContainer = document.getElementById('logContainer');
                
                if (!progressBar || !progressFill || !progressText || !progressStage) {
                    const missing = {
                        progressBar: !progressBar,
                        progressFill: !progressFill,
                        progressText: !progressText,
                        progressStage: !progressStage
                    };
                    console.error('❌ 进度条元素缺失:', missing);
                    alert('错误：进度条元素不存在！缺失: ' + JSON.stringify(missing));
                    return;
                }
                
                console.log('✅ 所有进度条元素存在，开始测试');
                
                // 强制显示进度条
                progressBar.style.display = 'block';
                progressBar.style.visibility = 'visible';
                progressBar.style.opacity = '1';
                progressBar.classList.add('show');
                
                // 立即设置样式确保可见
                progressBar.style.cssText = `
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    width: 100% !important;
                    height: 25px !important;
                    background: #e2e8f0 !important;
                    border: 2px solid #cbd5e0 !important;
                    border-radius: 12px !important;
                    margin: 20px 0 !important;
                    position: relative !important;
                `;
                
                showLog();
                addLog('🧪 开始测试进度条显示...', 'info');
                
                const testStages = [
                    { progress: 0, text: '⏳ 开始测试...', delay: 0 },
                    { progress: 20, text: '🔍 测试阶段 1...', delay: 800 },
                    { progress: 40, text: '📥 测试阶段 2...', delay: 1600 },
                    { progress: 60, text: '🏗️ 测试阶段 3...', delay: 2400 },
                    { progress: 80, text: '🎨 测试阶段 4...', delay: 3200 },
                    { progress: 100, text: '✅ 测试完成！', delay: 4000 }
                ];
                
                testStages.forEach((stage, index) => {
                    setTimeout(() => {
                        updateProgress(stage.progress, stage.text);
                        addLog(`测试进度: ${stage.progress}% - ${stage.text}`, 'info');
                        if (index === testStages.length - 1) {
                            addLog('🎉 进度条测试完成！可以正常工作！', 'success');
                            // 测试完成后保持100%显示3秒
                            setTimeout(() => {
                                updateProgress(10, '🚀 测试完成，进度条已重置');
                            }, 3000);
                        }
                    }, stage.delay);
                });
            } catch (error) {
                console.error('测试进度条时出错:', error);
                alert('测试失败: ' + error.message);
                addLog('❌ 测试失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
