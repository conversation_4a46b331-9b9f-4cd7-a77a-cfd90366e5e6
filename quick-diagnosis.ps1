Write-Host "=== 快速诊断 ===" -ForegroundColor Cyan

# 检查关键文件是否存在
Write-Host "`n1. 检查关键文件..." -ForegroundColor Yellow

$files = @{
    "DoubaoAiTestController" = "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoAiTestController.java"
    "DoubaoDirectService" = "ruoyi-admin/src/main/java/com/ruoyi/web/controller/ai/DoubaoDirectService.java"
    "DoubaoAiServiceImpl" = "ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DoubaoAiServiceImpl.java"
    "IAiService" = "ruoyi-system/src/main/java/com/ruoyi/system/service/IAiService.java"
    "application.yml" = "ruoyi-admin/src/main/resources/application.yml"
    "application-dev.yml" = "ruoyi-admin/src/main/resources/application-dev.yml"
}

foreach ($name in $files.Keys) {
    $path = $files[$name]
    if (Test-Path $path) {
        Write-Host "✅ $name" -ForegroundColor Green
    } else {
        Write-Host "❌ $name - $path" -ForegroundColor Red
    }
}

# 检查配置
Write-Host "`n2. 检查Doubao配置..." -ForegroundColor Yellow
$configFile = "ruoyi-admin/src/main/resources/application-dev.yml"
if (Test-Path $configFile) {
    $content = Get-Content $configFile -Raw
    if ($content -match "doubao:") {
        Write-Host "✅ 找到Doubao配置节点" -ForegroundColor Green
        if ($content -match "5ad57720-913c-410e-b75f-debd2fe836a4") {
            Write-Host "✅ API密钥配置正确" -ForegroundColor Green
        } else {
            Write-Host "❌ API密钥配置错误" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 未找到Doubao配置" -ForegroundColor Red
    }
}

# 检查端口占用
Write-Host "`n3. 检查端口8078..." -ForegroundColor Yellow
try {
    $portCheck = netstat -an | Select-String ":8078"
    if ($portCheck) {
        Write-Host "⚠️ 端口8078已被占用:" -ForegroundColor Yellow
        $portCheck | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "✅ 端口8078可用" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 无法检查端口状态" -ForegroundColor Yellow
}

# 检查Java进程
Write-Host "`n4. 检查Java进程..." -ForegroundColor Yellow
try {
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
    if ($javaProcesses) {
        Write-Host "发现Java进程:" -ForegroundColor Yellow
        $javaProcesses | ForEach-Object {
            Write-Host "  PID: $($_.Id), 内存: $([math]::Round($_.WorkingSet64/1MB, 2))MB" -ForegroundColor White
        }
    } else {
        Write-Host "✅ 没有运行中的Java进程" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 无法检查Java进程" -ForegroundColor Yellow
}

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Cyan

# 提供解决方案
Write-Host "`n🔧 问题解决建议:" -ForegroundColor Yellow
Write-Host "1. 如果端口被占用，请停止占用进程或更改端口" -ForegroundColor White
Write-Host "2. 确保所有配置文件都已正确更新" -ForegroundColor White
Write-Host "3. 尝试重新编译项目: mvn clean compile" -ForegroundColor White
Write-Host "4. 启动应用: mvn spring-boot:run -pl ruoyi-admin" -ForegroundColor White
