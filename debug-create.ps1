# 调试创建文案库问题
$baseUrl = "http://localhost:8078"

Write-Host "=== 调试创建文案库问题 ===" -ForegroundColor Green

# 1. 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/ai/test/health" -Method GET
    Write-Host "健康检查成功: $($health.msg)" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试创建文案库 - 最简单的数据
Write-Host "`n2. 测试创建文案库（最简数据）..." -ForegroundColor Yellow
$simpleData = @{
    libraryName = "SimpleTest"
    useAi = $false
    targetCount = 0
    wordCount = 100
} | ConvertTo-Json

Write-Host "请求数据: $simpleData"

try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $response = Invoke-WebRequest -Uri "$baseUrl/ai/copywriting/library" -Method POST -Body $simpleData -Headers $headers -UseBasicParsing
    
    Write-Host "HTTP状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response.Content)" -ForegroundColor Green
    
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = [int]$_.Exception.Response.StatusCode
        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
        
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            $reader.Close()
            $stream.Close()
            
            Write-Host "错误响应: $responseBody" -ForegroundColor Red
            
            # 尝试解析JSON错误信息
            try {
                $errorJson = $responseBody | ConvertFrom-Json
                Write-Host "错误代码: $($errorJson.code)" -ForegroundColor Red
                Write-Host "错误消息: $($errorJson.msg)" -ForegroundColor Red
            } catch {
                Write-Host "无法解析错误JSON" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "无法读取错误响应" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== 调试完成 ===" -ForegroundColor Green
