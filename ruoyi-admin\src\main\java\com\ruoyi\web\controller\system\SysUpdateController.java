package com.ruoyi.web.controller.system;

import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.domain.AjaxResult;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.text.SimpleDateFormat;

/**
 * 系统更新控制器
 */
@RestController
@RequestMapping("/system")
public class SysUpdateController {
    
    // 更新状态管理
    private static final AtomicBoolean updateInProgress = new AtomicBoolean(false);
    private static final AtomicReference<String> updateStatus = new AtomicReference<>("idle");
    private static final List<String> updateLogs = Collections.synchronizedList(new ArrayList<>());
    private static final AtomicReference<Integer> updateProgress = new AtomicReference<>(0);
    private static final AtomicBoolean updateCompleted = new AtomicBoolean(false);
    private static final AtomicBoolean updateSuccess = new AtomicBoolean(false);
    private static final AtomicReference<String> updateMessage = new AtomicReference<>("");
    
    /**
     * 获取系统更新状态
     */
    @GetMapping("/update/status")
    public AjaxResult getUpdateStatus() {
        try {
            Map<String, Object> statusData = new HashMap<>();
            
            // 简化版本，避免Git操作可能的问题
            statusData.put("hasUpdate", false); // 暂时设为false
            statusData.put("currentVersion", "1.0.0");
            statusData.put("latestVersion", "1.0.0");
            statusData.put("lastUpdate", "2025-07-15 09:00:00");
            statusData.put("updateInProgress", updateInProgress.get());
            statusData.put("status", updateStatus.get());
            
            // 添加调试信息
            System.out.println("=== 更新状态检查 ===");
            System.out.println("是否有更新: " + statusData.get("hasUpdate"));
            System.out.println("当前版本: " + statusData.get("currentVersion"));
            System.out.println("最新版本: " + statusData.get("latestVersion"));
            System.out.println("更新状态: " + updateStatus.get());
            
            return AjaxResult.success("状态获取成功", statusData);
        } catch (Exception e) {
            System.err.println("获取更新状态失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("获取状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 执行系统更新（网页版）
     */
    @PostMapping("/update/execute")
    public AjaxResult executeWebUpdate() {
        if (updateInProgress.get()) {
            return AjaxResult.error("更新正在进行中，请稍候");
        }
        
        // 重置状态
        updateInProgress.set(true);
        updateCompleted.set(false);
        updateSuccess.set(false);
        updateProgress.set(0);
        updateLogs.clear();
        updateStatus.set("starting");
        updateMessage.set("");
        
        // 异步执行更新
        new Thread(() -> {
            try {
                addUpdateLog("开始系统更新...");
                performWebUpdate();
            } catch (Exception e) {
                addUpdateLog("更新失败：" + e.getMessage());
                updateCompleted.set(true);
                updateSuccess.set(false);
                updateMessage.set(e.getMessage());
                updateInProgress.set(false);
            }
        }).start();
        
        return AjaxResult.success("更新已启动");
    }
    
    /**
     * 获取更新进度
     */
    @GetMapping("/update/progress")
    public AjaxResult getUpdateProgress() {
        Map<String, Object> progressData = new HashMap<>();
        progressData.put("inProgress", updateInProgress.get());
        progressData.put("completed", updateCompleted.get());
        progressData.put("success", updateSuccess.get());
        progressData.put("progress", updateProgress.get());
        progressData.put("status", updateStatus.get());
        progressData.put("message", updateMessage.get());
        progressData.put("logs", new ArrayList<>(updateLogs));
        
        return AjaxResult.success("进度获取成功", progressData);
    }
    
    /**
     * 获取更新页面
     */
    @GetMapping("/update/page")
    public AjaxResult getUpdatePage() {
        try {
            java.nio.file.Path filePath = java.nio.file.Paths.get("ruoyi-admin/src/main/resources/static/index.html");
            byte[] bytes = java.nio.file.Files.readAllBytes(filePath);
            String html = new String(bytes, java.nio.charset.StandardCharsets.UTF_8);
            return AjaxResult.success("页面获取成功", html);
        } catch (Exception e) {
            return AjaxResult.error("页面获取失败：" + e.getMessage());
        }
    }
    
    /**
     * 执行系统更新（需要认证）
     */
    @PostMapping("/update")
    public AjaxResult executeUpdate() {
        return performUpdate();
    }
    
    /**
     * 执行系统更新（本地调用，不需要认证）
     * 该接口仅用于本地自动更新，请确保安全配置
     */
    @PostMapping("/update/local")
    public AjaxResult executeLocalUpdate() {
        return performUpdate();
    }
    
    /**
     * 通用更新执行方法
     */
    private AjaxResult performUpdate() {
        try {
            // 获取项目根目录 - 确保是正确的工作目录
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            // 如果当前目录是ruoyi-admin，需要回到上级目录
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            java.io.File updateScript = new java.io.File(projectRoot, "update-local.bat");
            java.io.File projectDir = new java.io.File(projectRoot);
            
            // 详细的错误检查和日志
            System.out.println("=== 更新脚本执行信息 ===");
            System.out.println("用户目录: " + userDir);
            System.out.println("项目根目录: " + projectRoot);
            System.out.println("更新脚本路径: " + updateScript.getAbsolutePath());
            System.out.println("脚本文件存在: " + updateScript.exists());
            System.out.println("脚本文件可读: " + updateScript.canRead());
            System.out.println("项目目录存在: " + projectDir.exists());
            
            if (!updateScript.exists()) {
                return AjaxResult.error("更新脚本不存在：" + updateScript.getAbsolutePath() + "，请手动执行update-local.bat文件");
            }
            
            if (!updateScript.canRead()) {
                return AjaxResult.error("更新脚本无法读取，请检查文件权限，或手动执行update-local.bat文件");
            }
            
            // 构建命令 - 使用绝对路径
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("cmd", "/c", "\"" + updateScript.getAbsolutePath() + "\"");
            pb.directory(projectDir);
            pb.redirectErrorStream(true);
            
            System.out.println("执行命令: " + pb.command());
            System.out.println("工作目录: " + pb.directory().getAbsolutePath());
            
            Process process = pb.start();
            
            // 异步执行并记录输出
            new Thread(() -> {
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(process.getInputStream(), "GBK"))) {
                    
                    String line;
                    System.out.println("=== 更新脚本输出 ===");
                    while ((line = reader.readLine()) != null) {
                        System.out.println(line);
                    }
                    
                    int exitCode = process.waitFor();
                    System.out.println("=== 更新脚本执行完成 ===");
                    System.out.println("退出码: " + exitCode);
                    
                    if (exitCode == 0) {
                        System.out.println("✅ 更新脚本执行成功");
                    } else {
                        System.err.println("❌ 更新脚本执行失败，退出码: " + exitCode);
                    }
                    
                } catch (Exception e) {
                    System.err.println("读取更新脚本输出时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }).start();
            
            return AjaxResult.success("更新命令已执行，系统正在后台更新...");
            
        } catch (IOException e) {
            String errorMsg = "更新失败：" + e.getMessage();
            System.err.println(errorMsg);
            e.printStackTrace();
            return AjaxResult.error(errorMsg + "，请手动执行update-local.bat文件");
        } catch (Exception e) {
            String errorMsg = "更新过程中发生未知错误：" + e.getMessage();
            System.err.println(errorMsg);
            e.printStackTrace();
            return AjaxResult.error(errorMsg + "，请手动执行update-local.bat文件");
        }
    }
    
    /**
     * 网页版更新执行方法
     */
    private void performWebUpdate() {
        try {
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            java.io.File updateScript = new java.io.File(projectRoot, "update-local.bat");
            java.io.File projectDir = new java.io.File(projectRoot);
            
            addUpdateLog("项目根目录: " + projectRoot);
            addUpdateLog("更新脚本: " + updateScript.getAbsolutePath());
            
            if (!updateScript.exists()) {
                throw new RuntimeException("更新脚本不存在: " + updateScript.getAbsolutePath());
            }
            
            updateStatus.set("starting");
            updateProgress.set(5);
            addUpdateLog("🚀 开始执行更新脚本...");
            
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("cmd", "/c", "\"" + updateScript.getAbsolutePath() + "\"");
            pb.directory(projectDir);
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            // 读取输出并更新进度
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream(), "GBK"))) {
                
                String line;
                int progress = 10;
                while ((line = reader.readLine()) != null) {
                    addUpdateLog(line);
                    
                    // 根据输出内容智能更新进度和状态
                    if (line.contains("检查更新状态") || line.contains("Git状态检查")) {
                        progress = 15;
                        updateStatus.set("checking");
                        updateProgress.set(progress);
                    } else if (line.contains("正在获取最新代码") || line.contains("git fetch")) {
                        progress = 25;
                        updateStatus.set("fetching");
                        updateProgress.set(progress);
                    } else if (line.contains("正在拉取") || line.contains("git pull")) {
                        progress = 35;
                        updateStatus.set("pulling");
                        updateProgress.set(progress);
                    } else if (line.contains("正在编译后端") || line.contains("mvn clean package")) {
                        progress = 50;
                        updateStatus.set("building-backend");
                        updateProgress.set(progress);
                    } else if (line.contains("正在构建前端") || line.contains("npm run build")) {
                        progress = 75;
                        updateStatus.set("building-frontend");
                        updateProgress.set(progress);
                    } else if (line.contains("更新完成") || line.contains("BUILD SUCCESS")) {
                        progress = 95;
                        updateStatus.set("finishing");
                        updateProgress.set(progress);
                    }
                    
                    // 检测错误
                    if (line.contains("ERROR") || line.contains("FAILED") || line.contains("❌")) {
                        updateStatus.set("error");
                        throw new RuntimeException("更新过程中检测到错误: " + line);
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0) {
                    addUpdateLog("✅ 更新成功完成！");
                    updateProgress.set(100);
                    updateStatus.set("completed");
                    updateSuccess.set(true);
                    updateMessage.set("更新成功");
                } else {
                    addUpdateLog("❌ 更新失败，退出码: " + exitCode);
                    updateStatus.set("error");
                    updateSuccess.set(false);
                    updateMessage.set("更新脚本执行失败，退出码: " + exitCode);
                }
            }
            
        } catch (Exception e) {
            addUpdateLog("❌ 更新过程中发生错误: " + e.getMessage());
            updateStatus.set("error");
            updateSuccess.set(false);
            updateMessage.set(e.getMessage());
        } finally {
            updateCompleted.set(true);
            updateInProgress.set(false);
        }
    }
    
    /**
     * 检查是否有更新
     */
    private boolean checkForUpdates() {
        try {
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("git", "fetch", "origin", "main");
            pb.directory(new java.io.File(projectRoot));
            pb.redirectErrorStream(true);
            
            Process fetchProcess = pb.start();
            fetchProcess.waitFor();
            
            // 检查本地和远程是否有差异
            pb = new ProcessBuilder();
            pb.command("git", "rev-list", "--count", "HEAD..origin/main");
            pb.directory(new java.io.File(projectRoot));
            pb.redirectErrorStream(true);
            
            Process countProcess = pb.start();
            
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(countProcess.getInputStream()))) {
                String result = reader.readLine();
                countProcess.waitFor();
                
                return result != null && !result.trim().equals("0");
            }
            
        } catch (Exception e) {
            System.err.println("检查更新时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前版本
     */
    private String getCurrentVersion() {
        try {
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("git", "rev-parse", "--short", "HEAD");
            pb.directory(new java.io.File(projectRoot));
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String version = reader.readLine();
                process.waitFor();
                return version != null ? version.trim() : "未知";
            }
            
        } catch (Exception e) {
            return "未知";
        }
    }
    
    /**
     * 获取最新版本
     */
    private String getLatestVersion() {
        try {
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("git", "rev-parse", "--short", "origin/main");
            pb.directory(new java.io.File(projectRoot));
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String version = reader.readLine();
                process.waitFor();
                return version != null ? version.trim() : "未知";
            }
            
        } catch (Exception e) {
            return "未知";
        }
    }
    
    /**
     * 获取最后更新时间
     */
    private String getLastUpdateTime() {
        try {
            String userDir = System.getProperty("user.dir");
            String projectRoot = userDir;
            
            if (userDir.endsWith("ruoyi-admin")) {
                projectRoot = new java.io.File(userDir).getParent();
            }
            
            ProcessBuilder pb = new ProcessBuilder();
            pb.command("git", "log", "-1", "--format=%cd", "--date=format:%Y-%m-%d %H:%M:%S");
            pb.directory(new java.io.File(projectRoot));
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String time = reader.readLine();
                process.waitFor();
                return time != null ? time.trim() : "未知";
            }
            
        } catch (Exception e) {
            return "未知";
        }
    }
    
    /**
     * 添加更新日志
     */
    private void addUpdateLog(String message) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String logMessage = "[" + sdf.format(new Date()) + "] " + message;
        updateLogs.add(logMessage);
        System.out.println(logMessage);
        
        // 限制日志数量
        if (updateLogs.size() > 100) {
            updateLogs.remove(0);
        }
    }
}
