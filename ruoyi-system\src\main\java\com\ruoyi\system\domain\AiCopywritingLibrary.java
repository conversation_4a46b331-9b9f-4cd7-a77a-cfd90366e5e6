package com.ruoyi.system.domain;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI文案库对象 ai_copywriting_library
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class AiCopywritingLibrary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文案库ID */
    private Long libraryId;

    /** 文案库名称 */
    @ExcelProperty(value = "文案库名称")
    private String libraryName;

    /** 是否使用AI生成 */
    @ExcelProperty(value = "是否使用AI生成")
    private Boolean useAi;

    /** 店铺详情 */
    @ExcelProperty(value = "店铺详情")
    private String shopDetails;

    /** AI提示词 */
    @ExcelProperty(value = "AI提示词")
    private String prompt;

    /** 目标生成条数 */
    @ExcelProperty(value = "目标生成条数")
    private Integer targetCount;

    /** 已生成条数 */
    @ExcelProperty(value = "已生成条数")
    private Integer generatedCount;

    /** 文案字数要求 */
    @ExcelProperty(value = "文案字数要求")
    private Integer wordCount;

    /** 生成状态 */
    @ExcelProperty(value = "生成状态")
    private String status;

    /** 错误信息 */
    private String errorMessage;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long storeId;

    public void setLibraryId(Long libraryId) 
    {
        this.libraryId = libraryId;
    }

    public Long getLibraryId() 
    {
        return libraryId;
    }
    public void setLibraryName(String libraryName) 
    {
        this.libraryName = libraryName;
    }

    public String getLibraryName() 
    {
        return libraryName;
    }
    public void setUseAi(Boolean useAi) 
    {
        this.useAi = useAi;
    }

    public Boolean getUseAi() 
    {
        return useAi;
    }
    public void setShopDetails(String shopDetails) 
    {
        this.shopDetails = shopDetails;
    }

    public String getShopDetails() 
    {
        return shopDetails;
    }
    public void setPrompt(String prompt) 
    {
        this.prompt = prompt;
    }

    public String getPrompt() 
    {
        return prompt;
    }
    public void setTargetCount(Integer targetCount) 
    {
        this.targetCount = targetCount;
    }

    public Integer getTargetCount() 
    {
        return targetCount;
    }
    public void setGeneratedCount(Integer generatedCount) 
    {
        this.generatedCount = generatedCount;
    }

    public Integer getGeneratedCount() 
    {
        return generatedCount;
    }
    public void setWordCount(Integer wordCount) 
    {
        this.wordCount = wordCount;
    }

    public Integer getWordCount() 
    {
        return wordCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setStoreId(Long storeId) 
    {
        this.storeId = storeId;
    }

    public Long getStoreId() 
    {
        return storeId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("libraryId", getLibraryId())
            .append("libraryName", getLibraryName())
            .append("useAi", getUseAi())
            .append("shopDetails", getShopDetails())
            .append("prompt", getPrompt())
            .append("targetCount", getTargetCount())
            .append("generatedCount", getGeneratedCount())
            .append("wordCount", getWordCount())
            .append("status", getStatus())
            .append("errorMessage", getErrorMessage())
            .append("userId", getUserId())
            .append("storeId", getStoreId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
