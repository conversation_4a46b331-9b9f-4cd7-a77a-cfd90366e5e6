<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文案库测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .btn {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #66b1ff;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #f0f9ff;
            border-left: 4px solid #409EFF;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .error {
            background: #fef0f0;
            border-left-color: #f56c6c;
            color: #f56c6c;
        }
        .success {
            background: #f0f9ff;
            border-left-color: #67c23a;
            color: #67c23a;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 60px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI文案库测试页面</h1>
            <p>此页面用于测试AI文案库功能</p>
        </div>

        <!-- 健康检查 -->
        <div class="test-section">
            <h3>🔍 健康检查</h3>
            <button class="btn" onclick="testHealth()">健康检查</button>
            <div id="health-result"></div>
        </div>

        <!-- 创建文案库 -->
        <div class="test-section">
            <h3>✨ 创建文案库测试</h3>
            <div class="form-group">
                <label>文案库名称:</label>
                <input type="text" id="libraryName" value="测试文案库">
            </div>
            <div class="form-group">
                <label>店铺详情:</label>
                <textarea id="shopDetails">测试商户：一家温馨的咖啡厅，主营手工咖啡和精致甜点</textarea>
            </div>
            <div class="form-group">
                <label>AI提示词:</label>
                <textarea id="prompt">生成吸引人的美食推广文案，要求语言生动、有食欲感</textarea>
            </div>
            <button class="btn" onclick="createLibrary()">创建文案库</button>
            <div id="create-result"></div>
        </div>

        <!-- 查询文案库列表 -->
        <div class="test-section">
            <h3>📚 查询文案库列表</h3>
            <button class="btn" onclick="loadLibraryList()">查询列表</button>
            <div id="list-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8078';

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                console.log('发送请求:', API_BASE + url, options);
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                console.log('响应数据:', data);
                return data;
            } catch (error) {
                console.error('请求失败:', error);
                throw new Error('网络请求失败: ' + error.message);
            }
        }

        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 健康检查
        async function testHealth() {
            try {
                const result = await apiRequest('/ai/test/health');
                showResult('health-result', result);
            } catch (error) {
                showResult('health-result', { error: error.message }, true);
            }
        }

        // 创建文案库
        async function createLibrary() {
            try {
                const libraryData = {
                    libraryName: document.getElementById('libraryName').value,
                    useAi: true,
                    shopDetails: document.getElementById('shopDetails').value,
                    prompt: document.getElementById('prompt').value,
                    targetCount: 5,
                    wordCount: 100
                };

                console.log('创建文案库数据:', libraryData);

                // 首先尝试正式API
                let result;
                try {
                    result = await apiRequest('/ai/copywriting/library', {
                        method: 'POST',
                        body: JSON.stringify(libraryData)
                    });
                    showResult('create-result', { message: '使用正式API创建成功', data: result });
                } catch (error) {
                    console.log('正式API失败，尝试测试API:', error);
                    
                    // 如果正式API失败，尝试测试API
                    try {
                        result = await apiRequest('/ai/copywriting/library/test', {
                            method: 'POST',
                            body: JSON.stringify(libraryData)
                        });
                        showResult('create-result', { message: '使用测试API创建成功', data: result });
                    } catch (testError) {
                        console.log('测试API也失败:', testError);
                        showResult('create-result', { 
                            error: '所有API都失败了', 
                            originalError: error.message,
                            testError: testError.message 
                        }, true);
                    }
                }
                
            } catch (error) {
                showResult('create-result', { error: error.message }, true);
            }
        }

        // 查询文案库列表
        async function loadLibraryList() {
            try {
                // 首先尝试正式API
                let result;
                try {
                    result = await apiRequest('/ai/copywriting/library/list');
                    showResult('list-result', { message: '使用正式API查询成功', data: result });
                } catch (error) {
                    console.log('正式API失败，尝试测试API:', error);
                    
                    // 如果正式API失败，尝试测试API
                    try {
                        result = await apiRequest('/ai/copywriting/library/list/test');
                        showResult('list-result', { message: '使用测试API查询成功', data: result });
                    } catch (testError) {
                        console.log('测试API也失败:', testError);
                        showResult('list-result', { 
                            error: '所有API都失败了', 
                            originalError: error.message,
                            testError: testError.message 
                        }, true);
                    }
                }
            } catch (error) {
                showResult('list-result', { error: error.message }, true);
            }
        }

        // 页面加载时自动执行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
