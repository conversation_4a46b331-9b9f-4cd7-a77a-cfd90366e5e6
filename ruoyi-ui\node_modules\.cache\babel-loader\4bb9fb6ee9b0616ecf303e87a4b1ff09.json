{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\router\\index.js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\router\\index.js", "mtime": 1754543407189}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1753759488589}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_SimpleLayout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "meta", "title", "redirect", "name", "icon", "dynamicRoutes", "router", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes", "resetRouter", "newRouter", "matcher", "_default"], "sources": ["E:/ry-vue-flowable-xg-main/ruoyi-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\n\nVue.use(Router)\n\n/* Layout */\nimport Layout from '@/layout/SimpleLayout'\n\n/**\n * Note: 路由配置项\n *\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\n * roles: ['admin', 'common']       // 访问路由的角色权限\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\n * meta : {\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\n  }\n */\n\n// 公共路由\nexport const constantRoutes = [\n  {\n    path: '/redirect',\n    component: Layout,\n    hidden: true,\n    children: [\n      {\n        path: '/redirect/:path(.*)',\n        component: () => import('@/views/redirect')\n      }\n    ]\n  },\n  {\n    path: '/login',\n    component: () => import('@/views/login'),\n    hidden: true\n  },\n  {\n    path: '/update-progress',\n    component: () => import('@/views/update/UpdateProgressFixed'),\n    hidden: true,\n    meta: { title: '系统更新进度' }\n  },\n  {\n    path: '/copywriting-test',\n    component: () => import('@/views/store/copywriting-test'),\n    hidden: true,\n    meta: { title: 'AI文案库测试' }\n  },\n  {\n    path: '/404',\n    component: () => import('@/views/error/404'),\n    hidden: true\n  },\n  {\n    path: '/401',\n    component: () => import('@/views/error/401'),\n    hidden: true\n  },\n  {\n    path: '/',\n    redirect: '/storer/index',\n    hidden: true\n  },\n  {\n    path: '/index',\n    redirect: '/storer/index',\n    hidden: true\n  },\n  {\n    path: '/agent',\n    component: Layout,\n    children: [\n      {\n        path: 'list',\n        component: () => import('@/views/marketing/config.vue'),\n        name: 'MarketingConfig',\n        meta: { title: '营销配置管理', icon: 'peoples' }\n      }\n    ]\n  },\n  {\n    path: '/merchant',\n    component: Layout,\n    children: [\n      {\n        path: 'list',\n        component: () => import('@/views/merchant/list.vue'),\n        name: 'MerchantList',\n        meta: { title: '商家列表', icon: 'shopping' }\n      }\n    ]\n  },\n  {\n    path: '/finance',\n    component: Layout,\n    redirect: 'noRedirect',\n    name: 'Finance',\n    meta: { title: '财务管理', icon: 'money' },\n    children: [\n      {\n        path: 'overview',\n        component: () => import('@/views/finance/overview.vue'),\n        name: 'FinanceOverview',\n        meta: { title: '财务概览' }\n      },\n      {\n        path: 'record',\n        component: () => import('@/views/finance/record.vue'),\n        name: 'FinanceRecord',\n        meta: { title: '交易记录' }\n      }\n    ]\n  },\n  {\n    path: '/system',\n    component: Layout,\n    redirect: 'noRedirect',\n    name: 'System',\n    meta: { title: '系统管理', icon: 'system' },\n    children: [\n      {\n        path: 'menu',\n        component: () => import('@/views/system/menu/index.vue'),\n        name: 'Menu',\n        meta: { title: '菜单管理' }\n      },\n      {\n        path: 'dict',\n        component: () => import('@/views/system/dict/index.vue'),\n        name: 'Dict',\n        meta: { title: '字典管理' }\n      },\n      {\n        path: 'config',\n        component: () => import('@/views/system/config/index.vue'),\n        name: 'Config',\n        meta: { title: '参数设置' }\n      }\n    ]\n  },\n  {\n    path: '/storer',\n    component: Layout,\n    redirect: '/storer/index',\n    name: 'Storer',\n    meta: { title: '店铺管理', icon: 'shopping' },\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/dashboard/index.vue'),\n        name: 'StorerIndex',\n        meta: { title: '工作台' }\n      },\n      {\n        path: 'store',\n        component: () => import('@/views/store/store.vue'),\n        name: 'StorerStore',\n        meta: { title: '门店列表' }\n      },\n      {\n        path: 'shipin',\n        component: () => import('@/views/store/shipin.vue'),\n        name: 'StorerShipin',\n        meta: { title: 'AI剪辑文案' }\n      },\n      {\n        path: 'dou',\n        component: () => import('@/views/store/dou.vue'),\n        name: 'StorerDou',\n        meta: { title: '抖音/快手文案' }\n      },\n      {\n        path: 'hong',\n        component: () => import('@/views/store/hong.vue'),\n        name: 'StorerHong',\n        meta: { title: '小红书文案' }\n      },\n      {\n        path: 'daka',\n        component: () => import('@/views/store/daka.vue'),\n        name: 'StorerDaka',\n        meta: { title: '打卡点评/朋友圈文案' }\n      },\n      {\n        path: 'up',\n        component: () => import('@/views/store/up.vue'),\n        name: 'StorerUp',\n        meta: { title: '素材上传' }\n      },\n      {\n        path: 'huati',\n        component: () => import('@/views/store/huati.vue'),\n        name: 'StorerHuati',\n        meta: { title: '话题创建' }\n      },\n      {\n        path: 'mingxi',\n        component: () => import('@/views/store/mingxi.vue'),\n        name: 'StorerMingxi',\n        meta: { title: '算力明细' }\n      },\n      {\n        path: 'sk',\n        component: () => import('@/views/store/sk.vue'),\n        name: 'StorerSk',\n        meta: { title: '待发布视频库' }\n      },\n      {\n        path: 'tk',\n        component: () => import('@/views/store/tk.vue'),\n        name: 'StorerTk',\n        meta: { title: '图片库营销活动' }\n      },\n      {\n        path: 'jiang',\n        component: () => import('@/views/store/jiang.vue'),\n        name: 'StorerJiang',\n        meta: { title: '奖品设置' }\n      },\n      {\n        path: 'zhuanp',\n        component: () => import('@/views/store/zhuanp.vue'),\n        name: 'StorerZhuanp',\n        meta: { title: '大转盘配置' }\n      },\n      {\n        path: 'daijiang',\n        component: () => import('@/views/store/daijiang.vue'),\n        name: 'StorerDaijiang',\n        meta: { title: '待领取信息' }\n      },\n      {\n        path: 'dijin',\n        component: () => import('@/views/store/dijin.vue'),\n        name: 'StorerDijin',\n        meta: { title: 'AI递进式剪辑' }\n      },\n      {\n        path: 'djindu',\n        component: () => import('@/views/store/djindu.vue'),\n        name: 'StorerDjindu',\n        meta: { title: '递进剪辑进度' }\n      },\n      {\n        path: 'ai-test',\n        component: () => import('@/views/ai/test.vue'),\n        name: 'AiTest',\n        meta: { title: 'AI接口测试' }\n      },\n      {\n        path: 'quick-test',\n        component: () => import('@/views/ai/quick-test.vue'),\n        name: 'QuickTest',\n        meta: { title: 'AI快速测试' }\n      }\n    ]\n  },\n  // DIY编辑器路由\n  {\n    path: '/promotion/:storeId/diy',\n    component: () => import('@/views/promotion/PromotionDIYFixed.vue'),\n    name: 'PromotionDIY',\n    hidden: true,\n    meta: { title: '专业DIY页面编辑器' }\n  },\n  // NFC推广页面路由（客户访问的页面）\n  {\n    path: '/promotion/:id',\n    component: () => import('@/views/promotion/PromotionPreview.vue'),\n    name: 'PromotionPreview',\n    hidden: true,\n    meta: { title: '推广页面预览' }\n  },\n  {\n    path: '/promotion/config',\n    component: Layout,\n    name: 'PromotionConfig',\n    hidden: true,\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/promotion/PromotionPageConfig.vue'),\n        name: 'PromotionConfigIndex',\n        meta: { title: '推广页面配置' }\n      }\n    ]\n  },\n  {\n    path: '/changelog',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/changelog/index.vue'),\n        name: 'Changelog',\n        meta: { title: '更新日志', icon: 'documentation' }\n      }\n    ]\n  },\n\n  // 404 页面必须放在末尾\n  { path: '*', redirect: '/404', hidden: true }\n]\n\n// 动态路由，基于用户权限动态去加载\nexport const dynamicRoutes = []\n\nconst router = new Router({\n  mode: 'history', // 去掉url中的#\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n\n// 重置路由\nexport function resetRouter() {\n  const newRouter = new Router({\n    mode: 'history',\n    scrollBehavior: () => ({ y: 0 }),\n    routes: constantRoutes\n  })\n  router.matcher = newRouter.matcher\n}\n\nexport default router\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAC5B;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,qBAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,oCAAoC;IAAA;EAAA,CAAC;EAC7DW,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAS;AAC1B,CAAC,EACD;EACEX,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,gCAAgC;IAAA;EAAA,CAAC;EACzDW,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAU;AAC3B,CAAC,EACD;EACEX,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,GAAG;EACTY,QAAQ,EAAE,eAAe;EACzBT,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdY,QAAQ,EAAE,eAAe;EACzBT,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDqB,IAAI,EAAE,iBAAiB;IACvBH,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEG,IAAI,EAAE;IAAU;EAC3C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE;IAAW;EAC1C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,SAAS;EACfH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAQ,CAAC;EACtCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDqB,IAAI,EAAE,iBAAiB;IACvBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDqB,IAAI,EAAE,eAAe;IACrBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,QAAQ;EACdH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAS,CAAC;EACvCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDqB,IAAI,EAAE,MAAM;IACZH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDqB,IAAI,EAAE,MAAM;IACZH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DqB,IAAI,EAAE,QAAQ;IACdH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE,QAAQ;EACdH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAW,CAAC;EACzCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAM;EACvB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uBAAuB;MAAA;IAAA,CAAC;IAChDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EACD;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDqB,IAAI,EAAE,YAAY;IAClBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDqB,IAAI,EAAE,YAAY;IAClBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAa;EAC9B,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDqB,IAAI,EAAE,gBAAgB;IACtBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,qBAAqB;MAAA;IAAA,CAAC;IAC9CqB,IAAI,EAAE,QAAQ;IACdH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC;AAEL,CAAC;AACD;AACA;EACEX,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yCAAyC;IAAA;EAAA,CAAC;EAClEqB,IAAI,EAAE,cAAc;EACpBV,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAa;AAC9B,CAAC;AACD;AACA;EACEX,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wCAAwC;IAAA;EAAA,CAAC;EACjEqB,IAAI,EAAE,kBAAkB;EACxBV,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAS;AAC1B,CAAC,EACD;EACEX,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,qBAAM;EACjBW,IAAI,EAAE,iBAAiB;EACvBV,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2CAA2C;MAAA;IAAA,CAAC;IACpEqB,IAAI,EAAE,sBAAsB;IAC5BH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE;IAAgB;EAC/C,CAAC;AAEL,CAAC;AAED;AACA;EAAEd,IAAI,EAAE,GAAG;EAAEY,QAAQ,EAAE,MAAM;EAAET,MAAM,EAAE;AAAK,CAAC,CAC9C;;AAED;AACO,IAAMY,aAAa,GAAAhB,OAAA,CAAAgB,aAAA,GAAG,EAAE;AAE/B,IAAMC,MAAM,GAAG,IAAInB,kBAAM,CAAC;EACxBoB,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAEtB;AACV,CAAC,CAAC;;AAEF;AACO,SAASuB,WAAWA,CAAA,EAAG;EAC5B,IAAMC,SAAS,GAAG,IAAIzB,kBAAM,CAAC;IAC3BoB,IAAI,EAAE,SAAS;IACfC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;MAAA,OAAS;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAChCC,MAAM,EAAEtB;EACV,CAAC,CAAC;EACFkB,MAAM,CAACO,OAAO,GAAGD,SAAS,CAACC,OAAO;AACpC;AAAC,IAAAC,QAAA,GAAAzB,OAAA,CAAAU,OAAA,GAEcO,MAAM", "ignoreList": []}]}