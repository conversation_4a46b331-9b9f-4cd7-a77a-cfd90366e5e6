package com.ruoyi.system.domain;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI文案内容对象 ai_copywriting_content
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class AiCopywritingContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 内容ID */
    private Long contentId;

    /** 文案库ID */
    @ExcelProperty(value = "文案库ID")
    private Long libraryId;

    /** 文案内容 */
    @ExcelProperty(value = "文案内容")
    private String content;

    /** 文案标题 */
    @ExcelProperty(value = "文案标题")
    private String title;

    /** 字数统计 */
    @ExcelProperty(value = "字数统计")
    private Integer wordCount;

    /** 是否AI生成 */
    @ExcelProperty(value = "是否AI生成")
    private Boolean isAiGenerated;

    /** 生成状态 */
    @ExcelProperty(value = "生成状态")
    private String status;

    /** 质量评分 */
    @ExcelProperty(value = "质量评分")
    private Integer qualityScore;

    /** 使用次数 */
    @ExcelProperty(value = "使用次数")
    private Integer useCount;

    /** 点赞数 */
    @ExcelProperty(value = "点赞数")
    private Integer likeCount;

    /** 复制次数 */
    @ExcelProperty(value = "复制次数")
    private Integer copyCount;

    /** 导出次数 */
    @ExcelProperty(value = "导出次数")
    private Integer exportCount;

    /** 用户ID */
    private Long userId;

    /** 是否使用AI */
    private Boolean useAi;

    /** 生成条数 */
    private Integer count;

    /** 店铺详情 */
    private String shopDetails;

    /** AI提示词 */
    private String prompt;

    public void setContentId(Long contentId) 
    {
        this.contentId = contentId;
    }

    public Long getContentId() 
    {
        return contentId;
    }
    public void setLibraryId(Long libraryId) 
    {
        this.libraryId = libraryId;
    }

    public Long getLibraryId() 
    {
        return libraryId;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setWordCount(Integer wordCount) 
    {
        this.wordCount = wordCount;
    }

    public Integer getWordCount() 
    {
        return wordCount;
    }
    public void setIsAiGenerated(Boolean isAiGenerated) 
    {
        this.isAiGenerated = isAiGenerated;
    }

    public Boolean getIsAiGenerated() 
    {
        return isAiGenerated;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setQualityScore(Integer qualityScore) 
    {
        this.qualityScore = qualityScore;
    }

    public Integer getQualityScore() 
    {
        return qualityScore;
    }
    public void setUseCount(Integer useCount) 
    {
        this.useCount = useCount;
    }

    public Integer getUseCount() 
    {
        return useCount;
    }
    public void setLikeCount(Integer likeCount) 
    {
        this.likeCount = likeCount;
    }

    public Integer getLikeCount() 
    {
        return likeCount;
    }
    public void setCopyCount(Integer copyCount) 
    {
        this.copyCount = copyCount;
    }

    public Integer getCopyCount() 
    {
        return copyCount;
    }
    public void setExportCount(Integer exportCount) 
    {
        this.exportCount = exportCount;
    }

    public Integer getExportCount() 
    {
        return exportCount;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public Boolean getUseAi() {
        return useAi;
    }

    public void setUseAi(Boolean useAi) {
        this.useAi = useAi;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getShopDetails() {
        return shopDetails;
    }

    public void setShopDetails(String shopDetails) {
        this.shopDetails = shopDetails;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("contentId", getContentId())
            .append("libraryId", getLibraryId())
            .append("content", getContent())
            .append("title", getTitle())
            .append("wordCount", getWordCount())
            .append("isAiGenerated", getIsAiGenerated())
            .append("status", getStatus())
            .append("qualityScore", getQualityScore())
            .append("useCount", getUseCount())
            .append("likeCount", getLikeCount())
            .append("copyCount", getCopyCount())
            .append("exportCount", getExportCount())
            .append("userId", getUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
