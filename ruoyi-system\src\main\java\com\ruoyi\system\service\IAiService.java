package com.ruoyi.system.service;

import java.util.Map;

/**
 * AI服务接口 (支持火山引擎Doubao)
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IAiService
{
    /**
     * 获取访问令牌
     * 
     * @return 访问令牌
     */
    String getAccessToken();

    /**
     * 生成文案
     * 
     * @param prompt 提示词
     * @param shopDetails 店铺详情
     * @param maxTokens 最大令牌数
     * @return 生成的文案
     */
    String generateCopywriting(String prompt, String shopDetails, int maxTokens);

    /**
     * 批量生成文案
     * 
     * @param prompt 提示词
     * @param shopDetails 店铺详情
     * @param count 生成数量
     * @param maxTokens 最大令牌数
     * @return 生成的文案数组
     */
    String[] batchGenerateCopywriting(String prompt, String shopDetails, int count, int maxTokens);

    /**
     * 验证API配置
     * 
     * @return 是否配置正确
     */
    boolean validateApiConfig();

    /**
     * 获取模型信息
     * 
     * @return 模型信息
     */
    Map<String, Object> getModelInfo();
}
